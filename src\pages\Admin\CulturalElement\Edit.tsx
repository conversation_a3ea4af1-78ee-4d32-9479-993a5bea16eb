import AncientCitySelector from '@/components/AncientCitySelector';
import FormLayout from '@/components/FormLayout';
import LocationPicker from '@/components/LocationPicker';
import PhotoUpload from '@/components/PhotoUpload';
import TypeTreeSelect from '@/components/TypeTreeSelect';
import YearPicker from '@/components/YearPicker';
import {
  createCulturalElement,
  getCulturalElementDetail,
  updateCulturalElement,
} from '@/services/culturalElement';
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { history, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  Spin,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { TextArea } = Input;

const CulturalElementEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [data, setData] = useState<API.CulturalElement | null>(null);

  const isEdit = !!id && id !== 'create';

  // 获取详情数据（编辑模式）
  const fetchDetail = async () => {
    if (!isEdit) return;

    setLoading(true);
    try {
      const response = await getCulturalElementDetail(Number(id));

      if (response.errCode === 0 && response.data) {
        const detail = response.data;
        setData(detail);

        // 填充表单
        form.setFieldsValue({
          name: detail.name,
          code: detail.code,
          typeDictId: detail.typeDictId,
          ancientCityId: detail.ancientCityId,
          constructionYear: detail.constructionYear,
          longitude: detail.longitude,
          latitude: detail.latitude,
          height: detail.height,
          lengthArea: detail.lengthArea,
          locationDescription: detail.locationDescription,
          historicalRecords: detail.historicalRecords,
          description: detail.description,
          status: detail.status ?? 1, // 默认启用
          sort: detail.sort,
        });
      } else {
        message.error(response.msg || '获取详情失败');
        history.back();
      }
    } catch (error: any) {
      console.error('获取文化要素详情失败:', error);
      message.error(error?.message || '获取详情失败');
      history.back();
    } finally {
      setLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    setSubmitLoading(true);
    try {
      const params = {
        name: values.name,
        code: values.code,
        typeDictId: values.typeDictId,
        ancientCityId: values.ancientCityId,
        constructionYear: values.constructionYear,
        longitude: values.longitude,
        latitude: values.latitude,
        height: values.height,
        lengthArea: values.lengthArea,
        locationDescription: values.locationDescription,
        historicalRecords: values.historicalRecords,
        description: values.description,
        status: values.status,
        sort: values.sort,
      };

      let response;
      if (isEdit) {
        response = await updateCulturalElement(Number(id), params);
      } else {
        response = await createCulturalElement(params);
      }

      if (response.errCode === 0) {
        message.success(isEdit ? '更新成功' : '创建成功');
        history.push('/admin/cultural-element');
      } else {
        message.error(response.msg || (isEdit ? '更新失败' : '创建失败'));
      }
    } catch (error: any) {
      console.error('提交失败:', error);
      message.error(error?.message || (isEdit ? '更新失败' : '创建失败'));
    } finally {
      setSubmitLoading(false);
    }
  };

  // 位置选择回调
  const handleLocationChange = (longitude: number, latitude: number) => {
    form.setFieldsValue({ longitude, latitude });
  };

  // 初始化数据
  useEffect(() => {
    if (isEdit) {
      fetchDetail();
    }
  }, [id, isEdit]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <FormLayout
      title={isEdit ? '编辑文化要素' : '新增文化要素'}
      subtitle={isEdit ? `编辑：${data?.name || ''}` : '创建新的文化要素'}
      breadcrumb={[
        { title: '管理后台', path: '/admin' },
        { title: '文化要素管理', path: '/admin/cultural-element' },
        { title: isEdit ? '编辑' : '新增' },
      ]}
      actions={
        <Space>
          <Button icon={<ArrowLeftOutlined />} onClick={() => history.back()}>
            返回
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={submitLoading}
            onClick={() => form.submit()}
          >
            {isEdit ? '更新' : '创建'}
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
      >
        <Row gutter={[16, 16]}>
          {/* 基本信息 */}
          <Col xs={24}>
            <Card title="基本信息" size="small">
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="名称"
                    name="name"
                    rules={[
                      { required: true, message: '请输入名称' },
                      { max: 100, message: '名称不能超过100个字符' },
                    ]}
                  >
                    <Input placeholder="请输入文化要素名称" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="编号"
                    name="code"
                    rules={[{ max: 50, message: '编号不能超过50个字符' }]}
                  >
                    <Input placeholder="请输入编号（可选）" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="类型"
                    name="typeDictId"
                    rules={[{ required: true, message: '请选择类型' }]}
                  >
                    <TypeTreeSelect
                      placeholder="请选择类型"
                      treeDefaultExpandAll={true}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item label="古城" name="ancientCityId">
                    <AncientCitySelector
                      placeholder="请选择古城（可选）"
                      allowClear
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item label="建造年份" name="constructionYear">
                    <YearPicker placeholder="请选择建造年份（可选）" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 位置信息 */}
          <Col xs={24}>
            <Card title="位置信息" size="small">
              <Row gutter={16}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="经度"
                    name="longitude"
                    rules={[
                      {
                        type: 'number',
                        min: -180,
                        max: 180,
                        message: '经度范围：-180 ~ 180',
                      },
                    ]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="请输入经度"
                      precision={6}
                      min={-180}
                      max={180}
                      step={0.000001}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="纬度"
                    name="latitude"
                    rules={[
                      {
                        type: 'number',
                        min: -90,
                        max: 90,
                        message: '纬度范围：-90 ~ 90',
                      },
                    ]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="请输入纬度"
                      precision={6}
                      min={-90}
                      max={90}
                      step={0.000001}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item label=" " style={{ marginBottom: 0 }}>
                    <LocationPicker
                      longitude={form.getFieldValue('longitude')}
                      latitude={form.getFieldValue('latitude')}
                      onChange={handleLocationChange}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="位置描述"
                    name="locationDescription"
                    rules={[{ max: 500, message: '位置描述不能超过500个字符' }]}
                  >
                    <Input placeholder="请输入位置描述（可选）" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 特有属性 */}
          <Col xs={24}>
            <Card title="特有属性" size="small">
              <Row gutter={16}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="高度（米）"
                    name="height"
                    rules={[
                      {
                        type: 'number',
                        min: 0,
                        message: '高度必须大于等于0',
                      },
                    ]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="请输入高度（可选）"
                      precision={2}
                      min={0}
                      step={0.01}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="长度/面积"
                    name="lengthArea"
                    rules={[
                      { max: 100, message: '长度/面积不能超过100个字符' },
                    ]}
                  >
                    <Input placeholder="请输入长度或面积（可选）" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 描述信息 */}
          <Col xs={24}>
            <Card title="描述信息" size="small">
              <Row gutter={16}>
                <Col xs={24}>
                  <Form.Item
                    label="描述"
                    name="description"
                    rules={[{ max: 2000, message: '描述不能超过2000个字符' }]}
                  >
                    <TextArea
                      rows={4}
                      placeholder="请输入文化要素的详细描述（可选）"
                      showCount
                      maxLength={2000}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    label="历史记载"
                    name="historicalRecords"
                    rules={[
                      { max: 3000, message: '历史记载不能超过3000个字符' },
                    ]}
                  >
                    <TextArea
                      rows={4}
                      placeholder="请输入历史记载（可选）"
                      showCount
                      maxLength={3000}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 系统设置 */}
          <Col xs={24}>
            <Card title="系统设置" size="small">
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="状态"
                    name="status"
                    initialValue={1}
                    rules={[{ required: true, message: '请选择状态' }]}
                  >
                    <Select placeholder="请选择状态">
                      <Select.Option value={1}>启用</Select.Option>
                      <Select.Option value={0}>禁用</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="排序号"
                    name="sort"
                    rules={[
                      {
                        type: 'number',
                        min: 0,
                        message: '排序号必须大于等于0',
                      },
                    ]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="请输入排序号（可选）"
                      min={0}
                      step={1}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 照片上传（仅编辑模式） */}
          {isEdit && data && (
            <Col xs={24}>
              <Card title="相关照片" size="small">
                <PhotoUpload
                  entityType="culturalElement"
                  entityId={Number(id)}
                  maxCount={10}
                />
              </Card>
            </Col>
          )}
        </Row>
      </Form>
    </FormLayout>
  );
};

export default CulturalElementEdit;
