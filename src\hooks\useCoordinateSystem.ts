/**
 * @file 坐标系管理Hook
 * @description 提供坐标系转换和管理功能
 * <AUTHOR> Assistant
 * @date 2025-10-16
 */

import {
  batchConvertCoordinates,
  convertCoordinateByConfig,
  type Coordinate,
  type CoordinateSystem,
  type CoordinateSystemConfig,
} from '@/utils/coordinateTransform';
import { useCallback, useMemo } from 'react';

/**
 * 坐标系管理Hook
 */
export const useCoordinateSystem = () => {
  // 从全局配置获取坐标系设置
  const config: CoordinateSystemConfig = useMemo(() => {
    const amapConfig = (window as any).AMAP_CONFIG || {};
    return {
      storage: amapConfig.coordinateSystem?.storage || 'GCJ02',
      display: amapConfig.coordinateSystem?.display || 'GCJ02',
      autoConvert: amapConfig.coordinateSystem?.enableConversion || false,
    };
  }, []);

  /**
   * 将存储坐标转换为显示坐标（用于地图显示）
   */
  const storageToDisplay = useCallback(
    (coordinate: Coordinate): Coordinate => {
      return convertCoordinateByConfig(
        coordinate,
        config,
        'storage-to-display',
      );
    },
    [config],
  );

  /**
   * 将显示坐标转换为存储坐标（用于数据保存）
   */
  const displayToStorage = useCallback(
    (coordinate: Coordinate): Coordinate => {
      return convertCoordinateByConfig(
        coordinate,
        config,
        'display-to-storage',
      );
    },
    [config],
  );

  /**
   * 批量转换存储坐标为显示坐标
   */
  const batchStorageToDisplay = useCallback(
    (coordinates: Coordinate[]): Coordinate[] => {
      if (!config.autoConvert || config.storage === config.display) {
        return coordinates;
      }
      return batchConvertCoordinates(
        coordinates,
        config.storage,
        config.display,
      );
    },
    [config],
  );

  /**
   * 批量转换显示坐标为存储坐标
   */
  const batchDisplayToStorage = useCallback(
    (coordinates: Coordinate[]): Coordinate[] => {
      if (!config.autoConvert || config.storage === config.display) {
        return coordinates;
      }
      return batchConvertCoordinates(
        coordinates,
        config.display,
        config.storage,
      );
    },
    [config],
  );

  /**
   * 转换用户输入的坐标为存储坐标
   */
  const inputToStorage = useCallback(
    (coordinate: Coordinate, inputSystem?: CoordinateSystem): Coordinate => {
      const inputCoordSystem = inputSystem || config.storage;
      if (!config.autoConvert || inputCoordSystem === config.storage) {
        return coordinate;
      }
      return batchConvertCoordinates(
        [coordinate],
        inputCoordSystem,
        config.storage,
      )[0];
    },
    [config],
  );

  /**
   * 获取坐标系描述
   */
  function getCoordinateSystemDescription(system: CoordinateSystem): string {
    switch (system) {
      case 'WGS84':
        return 'WGS-84 世界大地坐标系（GPS坐标）';
      case 'GCJ02':
        return 'GCJ-02 国测局坐标系（火星坐标）';
      case 'BD09':
        return 'BD-09 百度坐标系';
      default:
        return '未知坐标系';
    }
  }

  /**
   * 转换存储坐标为用户输入格式
   */
  const storageToInput = useCallback(
    (coordinate: Coordinate, outputSystem?: CoordinateSystem): Coordinate => {
      const outputCoordSystem = outputSystem || config.storage;
      if (!config.autoConvert || config.storage === outputCoordSystem) {
        return coordinate;
      }
      return batchConvertCoordinates(
        [coordinate],
        config.storage,
        outputCoordSystem,
      )[0];
    },
    [config],
  );

  /**
   * 获取当前坐标系配置信息
   */
  const getCoordinateSystemInfo = useCallback(() => {
    return {
      storage: config.storage,
      display: config.display,
      autoConvert: config.autoConvert,
      description: {
        storage: getCoordinateSystemDescription(config.storage),
        display: getCoordinateSystemDescription(config.display),
      },
    };
  }, [config]);

  return {
    config,
    storageToDisplay,
    displayToStorage,
    batchStorageToDisplay,
    batchDisplayToStorage,
    inputToStorage,
    storageToInput,
    getCoordinateSystemInfo,
  };
};

/**
 * 坐标系切换Hook
 * 用于管理端配置坐标系切换
 */
export const useCoordinateSystemSwitch = () => {
  /**
   * 切换到WGS84坐标系
   */
  const switchToWGS84 = useCallback(() => {
    // 这里可以实现切换逻辑，比如：
    // 1. 更新全局配置
    // 2. 转换现有数据
    // 3. 刷新地图显示
    console.log('切换到WGS84坐标系');
    // 实际实现需要根据具体需求来做
  }, []);

  /**
   * 切换到GCJ02坐标系
   */
  const switchToGCJ02 = useCallback(() => {
    console.log('切换到GCJ02坐标系');
  }, []);

  /**
   * 获取支持的坐标系列表
   */
  const getSupportedSystems = useCallback((): Array<{
    value: CoordinateSystem;
    label: string;
    description: string;
  }> => {
    return [
      {
        value: 'WGS84',
        label: 'WGS-84',
        description: '世界大地坐标系，GPS设备使用的标准坐标系',
      },
      {
        value: 'GCJ02',
        label: 'GCJ-02',
        description: '国测局坐标系，中国地图服务商使用的加密坐标系',
      },
      {
        value: 'BD09',
        label: 'BD-09',
        description: '百度坐标系，百度地图使用的坐标系',
      },
    ];
  }, []);

  return {
    switchToWGS84,
    switchToGCJ02,
    getSupportedSystems,
  };
};

/**
 * 坐标验证Hook
 */
export const useCoordinateValidation = () => {
  /**
   * 验证坐标是否有效
   */
  const validateCoordinate = useCallback(
    (
      coordinate: Coordinate,
    ): {
      isValid: boolean;
      errors: string[];
    } => {
      const errors: string[] = [];

      // 检查经度范围
      if (coordinate.longitude < -180 || coordinate.longitude > 180) {
        errors.push('经度范围应在-180到180之间');
      }

      // 检查纬度范围
      if (coordinate.latitude < -90 || coordinate.latitude > 90) {
        errors.push('纬度范围应在-90到90之间');
      }

      // 检查是否为有效数字
      if (isNaN(coordinate.longitude) || isNaN(coordinate.latitude)) {
        errors.push('坐标必须为有效数字');
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
    [],
  );

  /**
   * 验证坐标是否在中国境内（用于GCJ02转换）
   */
  const isInChina = useCallback((coordinate: Coordinate): boolean => {
    const { longitude, latitude } = coordinate;
    return !(
      longitude < 72.004 ||
      longitude > 137.8347 ||
      latitude < 0.8293 ||
      latitude > 55.8271
    );
  }, []);

  return {
    validateCoordinate,
    isInChina,
  };
};
