import NetworkGraph from '@/components/NetworkGraph';
import PhotoGallery from '@/components/PhotoGallery';
import { getCulturalElementDetail } from '@/services/culturalElement';
import { getPublicNetworkGraphData } from '@/services/relationship';
import { getEntityPhotoList } from '@/services/upload';
import { ArrowLeftOutlined, EditOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Row,
  Space,
  Spin,
  Tag,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import './Detail.less';

const { Title } = Typography;

const CulturalElementDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<API.CulturalElement | null>(null);
  const [photos, setPhotos] = useState<any[]>([]);
  const [networkData, setNetworkData] = useState<API.NetworkGraphData | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [photosLoading, setPhotosLoading] = useState(false);
  const [networkLoading, setNetworkLoading] = useState(false);

  // 获取详情数据
  const fetchDetail = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await getCulturalElementDetail(Number(id));

      if (response.errCode === 0 && response.data) {
        setData(response.data);
      } else {
        message.error(response.msg || '获取详情失败');
        history.back();
      }
    } catch (error: any) {
      console.error('获取文化要素详情失败:', error);
      message.error(error?.message || '获取详情失败');
      history.back();
    } finally {
      setLoading(false);
    }
  };

  // 获取照片数据
  const fetchPhotos = async () => {
    if (!id) return;

    setPhotosLoading(true);
    try {
      const response = await getEntityPhotoList('culturalElement', Number(id));

      if (response.errCode === 0 && response.data) {
        setPhotos(response.data.list || []);
      } else {
        console.warn('获取照片失败:', response.msg);
        setPhotos([]);
      }
    } catch (error: any) {
      console.error('获取照片失败:', error);
      setPhotos([]);
    } finally {
      setPhotosLoading(false);
    }
  };

  // 获取网络图数据
  const fetchNetworkData = async () => {
    if (!id) return;

    setNetworkLoading(true);
    try {
      const response = await getPublicNetworkGraphData({
        status: 1,
        culturalElementId: Number(id),
      });

      if (response.errCode === 0 && response.data) {
        setNetworkData(response.data);
      } else {
        console.warn('获取网络图数据失败:', response.msg);
        setNetworkData(null);
      }
    } catch (error: any) {
      console.error('获取网络图数据失败:', error);
      setNetworkData(null);
    } finally {
      setNetworkLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchDetail();
    fetchPhotos();
    fetchNetworkData();
  }, [id]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <PageContainer
      title={data.name}
      subTitle={`编号: ${data.code || '无'}`}
      extra={
        <Space>
          <Button icon={<ArrowLeftOutlined />} onClick={() => history.back()}>
            返回
          </Button>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => history.push(`/admin/cultural-element/edit/${id}`)}
          >
            编辑
          </Button>
        </Space>
      }
      className="cultural-element-detail"
    >
      <Row gutter={[16, 16]}>
        {/* 基本信息 */}
        <Col xs={24} lg={12}>
          <Card title="基本信息" size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="名称">
                <Title level={5} style={{ margin: 0 }}>
                  {data.name}
                </Title>
              </Descriptions.Item>
              <Descriptions.Item label="编号">
                {data.code || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="类型">
                <Tag color="blue">
                  {data.typeName || data.typeDict?.typeName || '-'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="古城">
                <Tag color="orange">
                  {data.cityName ||
                    data.ancientCityName ||
                    data.ancientCity?.name ||
                    '-'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="建造年份">
                {data.constructionYear ? (
                  <span>
                    {data.constructionYear > 0
                      ? `公元${data.constructionYear}年`
                      : `公元前${Math.abs(data.constructionYear)}年`}
                  </span>
                ) : (
                  '-'
                )}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={data.status === 1 ? 'green' : 'red'}>
                  {data.status === 1 ? '启用' : '禁用'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 位置信息 */}
        <Col xs={24} lg={12}>
          <Card title="位置信息" size="small" style={{ height: '100%' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="经度">
                {data.longitude || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="纬度">
                {data.latitude || '-'}
              </Descriptions.Item>
              {data.height && (
                <Descriptions.Item label="高度">
                  {data.height}米
                </Descriptions.Item>
              )}
              {data.lengthArea && (
                <Descriptions.Item label="长度/面积">
                  {data.lengthArea}
                </Descriptions.Item>
              )}
              {data.locationDescription && (
                <Descriptions.Item label="位置描述">
                  {data.locationDescription}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
        </Col>

        {/* 详细信息 */}
        {(data.historicalRecords || data.description) && (
          <Col xs={24}>
            <Card title="详细信息" size="small">
              <Descriptions column={1} size="small" layout="vertical">
                {data.historicalRecords && (
                  <Descriptions.Item label="历史记载">
                    <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                      {data.historicalRecords}
                    </div>
                  </Descriptions.Item>
                )}
                {data.description && (
                  <Descriptions.Item label="描述信息">
                    <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                      {data.description}
                    </div>
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          </Col>
        )}

        {/* 照片展示 */}
        <Col xs={24}>
          <Card title="相关照片" size="small">
            <PhotoGallery
              photos={photos}
              loading={photosLoading}
              entityType="culturalElement"
              entityId={Number(id)}
              onUpdate={fetchPhotos}
              enableBatchOperations={true}
            />
          </Card>
        </Col>

        {/* 关系图谱 */}
        <Col xs={24}>
          <Card title="关系网络图" size="small">
            <NetworkGraph
              data={networkData}
              loading={networkLoading}
              height={500}
              title=""
              onNodeClick={(nodeData: any) => {
                console.log('节点点击:', nodeData);
                // 可以在这里实现节点点击跳转到对应要素详情
              }}
              onLinkClick={(linkData: any) => {
                console.log('连线点击:', linkData);
              }}
            />
          </Card>
        </Col>

        {/* 系统信息 */}
        <Col xs={24}>
          <Card title="系统信息" size="small">
            <Descriptions column={2} size="small">
              <Descriptions.Item label="创建时间">
                {data.createdAt
                  ? new Date(data.createdAt).toLocaleString()
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {data.updatedAt
                  ? new Date(data.updatedAt).toLocaleString()
                  : '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default CulturalElementDetail;
