import PageHeader from '@/components/PageHeader';
import PublicLayout from '@/components/PublicLayout';
import { getCulturalElementListAdapter } from '@/utils/culturalElementAdapter';
import {
  EnvironmentOutlined,
  FilterOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { history, useDispatch, useSearchParams, useSelector } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Empty,
  Image,
  List,
  Row,
  Space,
  Spin,
  Tag,
  Tree,
  Typography,
  message,
} from 'antd';
import { useEffect, useState } from 'react';
import './index.less';

const { Title, Text } = Typography;

// 将类型字典数据转换为Tree组件需要的格式
const convertTypeDataToTreeData = (typeList: API.TypeDict[]) => {
  const convertNode = (node: API.TypeDict): any => {
    // 递归过滤并转换子节点
    const enabledChildren = node.children
      ? node.children
          .filter((child) => child.status === 1) // 只保留启用状态的子节点
          .map(convertNode)
      : undefined;

    return {
      title: node.typeName,
      key: node.id.toString(),
      value: node.id,
      children:
        enabledChildren && enabledChildren.length > 0
          ? enabledChildren
          : undefined,
    };
  };

  return typeList
    .filter((item) => item.status === 1) // 只显示启用状态的根节点
    .map(convertNode);
};

// 从选中的类型键值中提取所有类型ID
const extractTypeIdsFromKeys = (keys: string[]): number[] => {
  return keys.map((key) => parseInt(key, 10)).filter((id) => !isNaN(id));
};

const CulturalElementPage: React.FC = () => {
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  // 从dva中获取古城和类型数据
  const { ancientCityList, loading: ancientCityLoading } = useSelector(
    (state: any) => state.ancientCity,
  );
  const { typeList, loading: typeLoading } = useSelector(
    (state: any) => state.dictionary,
  );

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.CulturalElement[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [selectedTypeId, setSelectedTypeId] = useState<string | undefined>();
  const [selectedAncientCityId, setSelectedAncientCityId] = useState<
    number | undefined
  >();
  const [selectedTypeKeys, setSelectedTypeKeys] = useState<string[]>([]);

  // 转换类型数据为Tree组件格式
  const treeData = convertTypeDataToTreeData(typeList || []);

  const fetchList = async (
    p = page,
    ps = pageSize,
    typeId?: string,
    ancientCityId?: number,
  ) => {
    setLoading(true);
    try {
      const params: any = { page: p, pageSize: ps };
      if (typeId) params.typeDictId = typeId;
      if (ancientCityId) params.ancientCityId = ancientCityId;

      // 使用适配器函数获取文化要素数据
      const res = await getCulturalElementListAdapter(params);
      if (res.errCode === 0 && res.data) {
        const elements = res.data.list || [];
        setData(elements);
        setTotal(res.data.total || 0);
        setPage(res.data.page || p);
        setPageSize(res.data.pageSize || ps);

        // 图片链接现在直接从后台返回，不需要单独获取
      } else {
        message.error(res.msg || '获取数据失败');
      }
    } catch (error) {
      console.error('获取文化要素列表失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据获取
  useEffect(() => {
    // 获取古城数据
    if (ancientCityList.length === 0) {
      dispatch({ type: 'ancientCity/fetchAncientCityList' });
    }

    // 获取类型数据
    if (typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
  }, []);

  // 处理URL参数并初始化筛选条件
  useEffect(() => {
    const ancientCityIdParam = searchParams.get('ancientCityId');
    const ancientCityNameParam = searchParams.get('ancientCityName');

    if (ancientCityIdParam) {
      const ancientCityId = parseInt(ancientCityIdParam, 10);
      if (!isNaN(ancientCityId)) {
        setSelectedAncientCityId(ancientCityId);
      }
    } else if (ancientCityNameParam && ancientCityList.length > 0) {
      // 根据古城名称查找对应的ID
      const foundCity = ancientCityList.find(
        (city: any) =>
          city.cityName === ancientCityNameParam ||
          city.name === ancientCityNameParam,
      );
      if (foundCity) {
        setSelectedAncientCityId(foundCity.id);
      }
    }
  }, [searchParams, ancientCityList]);

  // 当筛选条件确定后，获取文化要素列表
  useEffect(() => {
    if (ancientCityList.length > 0 && typeList.length > 0) {
      fetchList(1, pageSize, selectedTypeId, selectedAncientCityId);
    }
  }, [ancientCityList, typeList, selectedAncientCityId]);

  // 当选择的类型键值改变时，提取对应的类型ID进行筛选
  useEffect(() => {
    if (selectedTypeKeys.length > 0) {
      // 提取所有选中的类型ID
      const typeIds = extractTypeIdsFromKeys(selectedTypeKeys);

      if (typeIds.length > 0) {
        // 如果有多个选中的类型，优先使用最具体的（通常是最后选中的叶子节点）
        // 或者可以根据业务需求调整为使用第一个、所有类型等
        const primaryTypeId = typeIds.join(',');
        setSelectedTypeId(primaryTypeId);
        fetchList(1, pageSize, primaryTypeId, selectedAncientCityId);
      }
    } else {
      setSelectedTypeId(undefined);
      fetchList(1, pageSize, undefined, selectedAncientCityId);
    }
  }, [selectedTypeKeys]);

  const handleTypeChange = (checkedKeys: any) => {
    const keys = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked;
    setSelectedTypeKeys(keys);
    setPage(1);
    // 类型筛选逻辑在useEffect中处理
  };

  const handleAncientCityChange = (ancientCityId: number | undefined) => {
    setSelectedAncientCityId(ancientCityId);
    setPage(1);
    fetchList(1, pageSize, selectedTypeId, ancientCityId);
  };

  return (
    <PublicLayout>
      <PageHeader
        title="文化要素"
        description="探索关中地区丰富的历史文化要素，包括山塬、水系、历史建筑等各类文化遗产。"
        backgroundType="hero"
        height={300}
      />

      <div className="cultural-element-page">
        <div className="content-card">
          <Card className="filter-section" style={{ marginBottom: 24 }}>
            <div className="filter-header">
              <Title
                level={5}
                style={{ margin: 0, display: 'flex', alignItems: 'center' }}
              >
                <EnvironmentOutlined style={{ marginRight: 8 }} />
                古城筛选
              </Title>
            </div>
            <div className="ancient-city-filter">
              <Spin spinning={ancientCityLoading}>
                <Space wrap size={[8, 8]}>
                  <Tag.CheckableTag
                    checked={!selectedAncientCityId}
                    onChange={() => handleAncientCityChange(undefined)}
                    className="city-tag"
                  >
                    全部
                  </Tag.CheckableTag>
                  {ancientCityList.map((city: API.AncientCityDict) => (
                    <Tag.CheckableTag
                      key={city.id}
                      checked={selectedAncientCityId === city.id}
                      onChange={() => handleAncientCityChange(city.id)}
                      className="city-tag"
                    >
                      {city.cityName}
                    </Tag.CheckableTag>
                  ))}
                </Space>
              </Spin>
            </div>
          </Card>

          <Row gutter={24}>
            <Col xs={24} sm={6} md={6} lg={5} xl={4}>
              <Card className="filter-card">
                <div className="filter-header">
                  <Title
                    level={5}
                    style={{ margin: 0, display: 'flex', alignItems: 'center' }}
                  >
                    <FilterOutlined style={{ marginRight: 8 }} />
                    类型筛选
                  </Title>
                </div>
                <div style={{ marginTop: 16 }}>
                  <Spin spinning={typeLoading}>
                    <Tree
                      checkable
                      checkedKeys={selectedTypeKeys}
                      onCheck={handleTypeChange}
                      treeData={treeData}
                      style={{ width: '100%' }}
                    />
                  </Spin>
                </div>
              </Card>
            </Col>

            <Col xs={24} sm={18} md={18} lg={19} xl={20}>
              <Card className="list-container">
                <div className="list-header">
                  <div className="list-info">
                    <Title level={5} style={{ margin: 0 }}>
                      文化要素列表
                    </Title>
                    <Text type="secondary">共 {total} 个要素</Text>
                  </div>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() =>
                      fetchList(
                        page,
                        pageSize,
                        selectedTypeId,
                        selectedAncientCityId,
                      )
                    }
                  >
                    刷新
                  </Button>
                </div>

                <Spin spinning={loading}>
                  {data.length === 0 && !loading ? (
                    <Empty
                      description="暂无文化要素数据"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      style={{ margin: '60px 0' }}
                    />
                  ) : (
                    <List
                      className="cultural-element-list"
                      grid={{
                        gutter: [24, 24],
                        xs: 1,
                        sm: 2,
                        md: 2,
                        lg: 3,
                        xl: 3,
                        xxl: 4,
                      }}
                      dataSource={data}
                      pagination={{
                        current: page,
                        pageSize,
                        total,
                        onChange: (cp, cps) =>
                          fetchList(
                            cp,
                            cps,
                            selectedTypeId,
                            selectedAncientCityId,
                          ),
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) =>
                          `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                        style: { marginTop: 24, textAlign: 'center' },
                      }}
                      renderItem={(item) => (
                        <List.Item>
                          <Card
                            hoverable
                            className="element-card"
                            onClick={() =>
                              history.push(`/detail/culturalElement/${item.id}`)
                            }
                            cover={
                              <Image
                                src={item.photos?.[0]?.url}
                                alt={item.name}
                                style={{
                                  width: '100%',
                                  height: '200px',
                                  objectFit: 'cover',
                                }}
                                fallback="/images/banners/home.jpg"
                                preview={false}
                              />
                            }
                          >
                            <div className="card-content">
                              <div className="card-title">
                                <Title
                                  level={5}
                                  ellipsis={{ tooltip: item.name }}
                                  style={{ margin: 0, marginBottom: 8 }}
                                >
                                  {item.name}
                                </Title>
                                {item.typeName && (
                                  <Tag color="blue">{item.typeName}</Tag>
                                )}
                              </div>

                              <div className="card-description">
                                <Space
                                  direction="vertical"
                                  size={4}
                                  style={{ width: '100%' }}
                                >
                                  {item.ancientCityName && (
                                    <div className="info-item">
                                      <EnvironmentOutlined
                                        style={{
                                          color: '#1890ff',
                                          marginRight: 4,
                                        }}
                                      />
                                      <Text
                                        type="secondary"
                                        style={{ fontSize: '12px' }}
                                      >
                                        {item.ancientCityName}
                                      </Text>
                                    </div>
                                  )}
                                  {item.regionName && (
                                    <div className="info-item">
                                      <Text
                                        type="secondary"
                                        style={{ fontSize: '12px' }}
                                      >
                                        区域：{item.regionName}
                                      </Text>
                                    </div>
                                  )}
                                  {item.constructionYear ? (
                                    <div className="info-item">
                                      <Text
                                        type="secondary"
                                        style={{ fontSize: '12px' }}
                                      >
                                        {item.constructionYear > 0
                                          ? `公元${item.constructionYear}年`
                                          : `公元前${Math.abs(
                                              item.constructionYear,
                                            )}年`}
                                      </Text>
                                    </div>
                                  ) : (
                                    <div className="info-item">-</div>
                                  )}
                                  {item.locationDescription ? (
                                    <div
                                      className="description-text"
                                      style={{ marginTop: 8 }}
                                    >
                                      <Text
                                        type="secondary"
                                        style={{ fontSize: '12px' }}
                                      >
                                        {item.locationDescription.length > 60
                                          ? `${item.locationDescription.substring(
                                              0,
                                              60,
                                            )}...`
                                          : item.locationDescription}
                                      </Text>
                                    </div>
                                  ) : (
                                    <div className="info-item">-</div>
                                  )}
                                </Space>
                              </div>
                            </div>
                          </Card>
                        </List.Item>
                      )}
                    />
                  )}
                </Spin>
              </Card>
            </Col>
          </Row>
        </div>
      </div>
    </PublicLayout>
  );
};

export default CulturalElementPage;
