import NetworkGraphWithControls from '@/components/NetworkGraph/NetworkGraphWithControls';
import PageHeader from '@/components/PageHeader';
import PublicLayout from '@/components/PublicLayout';
import React, { useEffect, useMemo, useState } from 'react';
import StatisticsCards from './components/StatisticsCards';
import StatisticsCharts from './components/StatisticsCharts';
import StatisticsFilters from './components/StatisticsFilters';
import useDigitalStatistics from './hooks/useDigitalStatistics';

const DigitalPage: React.FC = () => {
  const [dateRange, setDateRange] = useState<any>(null);

  // 修复页面初始化时的下拉框定位问题
  useEffect(() => {
    // 确保body的overflow样式正确
    document.body.style.overflow = 'auto';

    // 强制重新计算下拉框位置
    const fixDropdownPosition = () => {
      window.dispatchEvent(new Event('resize'));
      window.dispatchEvent(new Event('scroll'));
    };

    // 延迟执行，确保页面完全渲染
    const timer1 = setTimeout(fixDropdownPosition, 100);
    const timer2 = setTimeout(fixDropdownPosition, 300);
    const timer3 = setTimeout(fixDropdownPosition, 500);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []);

  // 移除区域筛选逻辑

  const startTime = useMemo(() => {
    return dateRange?.[0]?.toISOString();
  }, [dateRange]);

  const endTime = useMemo(() => {
    return dateRange?.[1]?.toISOString();
  }, [dateRange]);

  // 使用统计数据Hook
  const {
    basicData,
    timelineData,
    networkData,
    loading,
    refreshData,
    loadNetworkData,
  } = useDigitalStatistics({
    startTime,
    endTime,
    autoLoad: true,
  });

  // 处理刷新
  const handleRefresh = () => {
    refreshData();
  };

  // 判断是否正在加载
  const isLoading = loading.basic || loading.overview || loading.timeline;

  // 网络图是否正在加载
  const isNetworkLoading = loading.network;

  return (
    <PublicLayout>
      {/* 页面头部Banner */}
      <PageHeader
        title="数字化统计分析"
        description="通过数据可视化技术，深入分析关中地区历史文化要素的分布规律和发展趋势。"
        backgroundType="digital"
        height={300}
      />

      <div className="content-card" style={{ padding: '24px' }}>
        {/* 筛选器 */}
        <StatisticsFilters
          dateRange={dateRange}
          loading={isLoading}
          onDateRangeChange={setDateRange}
          onRefresh={handleRefresh}
        />

        {/* 统计卡片 */}
        <StatisticsCards data={basicData} loading={isLoading} />

        {/* 关系网络图 */}
        <div style={{ marginTop: 24 }}>
          <NetworkGraphWithControls
            data={networkData}
            loading={isNetworkLoading}
            title="要素关系网络图"
            onRefresh={loadNetworkData}
          />
        </div>

        {/* 图表区域 */}
        <StatisticsCharts
          basicData={basicData}
          timelineData={timelineData}
          loading={isLoading}
        />
      </div>
    </PublicLayout>
  );
};

export default DigitalPage;
