import { FullscreenOutlined, ReloadOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, message, Select, Space, Spin } from 'antd';
import * as echarts from 'echarts';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import type {
  NetworkGraphData,
  NetworkGraphFilters,
  NetworkGraphProps,
} from './types';

const { Option } = Select;

const RelationshipNetworkGraph: React.FC<NetworkGraphProps> = ({
  height = 600,
  title = '要素关联网络图',
  showFilters = true,
  showCard = true,
  isPublic = false,
  onDataFetch,
  initialFilters = {},
  className,
  style,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<NetworkGraphFilters>({
    status: isPublic ? 1 : undefined, // 公开模式默认只显示启用状态
    ...initialFilters,
  });

  // 动态导入服务函数
  const getDataService = useCallback(async () => {
    if (onDataFetch) {
      return onDataFetch;
    }

    if (isPublic) {
      // 门户端使用公开接口
      const { getPublicNetworkGraphData } = await import(
        '@/services/relationship'
      );
      return async (filters: NetworkGraphFilters) => {
        try {
          const response = await getPublicNetworkGraphData(filters);
          return response.errCode === 0 ? response.data || null : null;
        } catch (error) {
          console.error('获取公开网络图数据失败:', error);
          return null;
        }
      };
    } else {
      // 管理端使用管理接口
      const { getNetworkGraphData } = await import('@/services/relationship');
      return async (filters: NetworkGraphFilters) => {
        try {
          const response = await getNetworkGraphData(filters);
          return response.errCode === 0 ? response.data || null : null;
        } catch (error) {
          console.error('获取网络图数据失败:', error);
          return null;
        }
      };
    }
  }, [isPublic, onDataFetch]);

  // 初始化图表
  const initChart = useCallback(() => {
    if (!chartRef.current) return;

    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    chartInstance.current = echarts.init(chartRef.current);
  }, []);

  // 渲染图表
  const renderChart = useCallback(
    (data: NetworkGraphData) => {
      if (!chartInstance.current) return;

      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
          },
          show: !showCard, // 如果有Card包装，则不显示图表标题
        },
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            if (params.dataType === 'node') {
              return `
              <div>
                <strong>${params.data.name}</strong><br/>
                类型: ${params.data.category === 0 ? '古城' : '文化要素'}<br/>
                连接数: ${params.data.symbolSize}
              </div>
            `;
            } else if (params.dataType === 'edge') {
              return `
              <div>
                <strong>${params.data.name}</strong><br/>
                ${params.data.source} → ${params.data.target}
              </div>
            `;
            }
            return '';
          },
        },
        legend: {
          data: data.categories.map((cat) => cat.name),
          bottom: 10,
        },
        series: [
          {
            type: 'graph',
            layout: 'force',
            data: data.nodes,
            links: data.links,
            categories: data.categories,
            roam: true,
            focusNodeAdjacency: true,
            draggable: true,
            force: {
              repulsion: 100,
              edgeLength: [50, 200],
              gravity: 0.1,
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{b}',
              fontSize: 12,
            },
            lineStyle: {
              color: 'source',
              curveness: 0.1,
              opacity: 0.8,
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 3,
              },
              itemStyle: {
                borderWidth: 2,
                borderColor: '#fff',
              },
            },
            animation: true,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
        ],
      };

      chartInstance.current.setOption(option, true);
    },
    [title, showCard],
  );

  // 获取网络图数据
  const fetchNetworkData = useCallback(async () => {
    setLoading(true);
    try {
      const dataService = await getDataService();
      const data = await dataService(filters);

      if (data) {
        renderChart(data);
      } else {
        message.error('获取网络图数据失败');
      }
    } catch (error: any) {
      console.error('获取网络图数据失败:', error);
      message.error(error?.message || '获取网络图数据失败');
    } finally {
      setLoading(false);
    }
  }, [filters, getDataService]);

  // 处理筛选条件变化
  const handleFilterChange = useCallback((key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // 重新加载数据
  const handleReload = useCallback(() => {
    fetchNetworkData();
  }, [fetchNetworkData]);

  // 全屏显示
  const handleFullscreen = useCallback(() => {
    if (chartRef.current) {
      if (chartRef.current.requestFullscreen) {
        chartRef.current.requestFullscreen();
      }
    }
  }, []);

  // 响应式处理
  const handleResize = useCallback(() => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  }, []);

  useEffect(() => {
    initChart();
    fetchNetworkData();

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [initChart, fetchNetworkData, handleResize]);

  // 当筛选条件变化时重新获取数据
  useEffect(() => {
    if (chartInstance.current) {
      fetchNetworkData();
    }
  }, [filters, fetchNetworkData]);

  // 筛选器组件
  const FiltersComponent = showFilters ? (
    <Space>
      <Select
        placeholder="源要素类型"
        style={{ width: 120 }}
        allowClear
        value={filters.sourceEntityType}
        onChange={(value) => handleFilterChange('sourceEntityType', value)}
      >
        <Option value="ancient_city">古城</Option>
        <Option value="cultural_element">文化要素</Option>
      </Select>
      <Select
        placeholder="目标要素类型"
        style={{ width: 120 }}
        allowClear
        value={filters.targetEntityType}
        onChange={(value) => handleFilterChange('targetEntityType', value)}
      >
        <Option value="ancient_city">古城</Option>
        <Option value="cultural_element">文化要素</Option>
      </Select>
      {!isPublic && (
        <Select
          placeholder="状态"
          style={{ width: 100 }}
          value={filters.status}
          onChange={(value) => handleFilterChange('status', value)}
        >
          <Option value={1}>启用</Option>
          <Option value={0}>禁用</Option>
          <Option value={undefined}>全部</Option>
        </Select>
      )}
    </Space>
  ) : null;

  // 操作按钮组件
  const ActionsComponent = (
    <Space>
      <Button
        type="text"
        icon={<ReloadOutlined />}
        onClick={handleReload}
        loading={loading}
      />
      <Button
        type="text"
        icon={<FullscreenOutlined />}
        onClick={handleFullscreen}
      />
    </Space>
  );

  // 图表内容
  const ChartContent = (
    <Spin spinning={loading}>
      <div
        ref={chartRef}
        style={{
          width: '100%',
          height: `${height}px`,
          minHeight: '400px',
        }}
      />
    </Spin>
  );

  // 如果不显示Card包装，直接返回图表
  if (!showCard) {
    return (
      <div className={className} style={style}>
        {showFilters && (
          <div
            style={{
              marginBottom: 16,
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            {FiltersComponent}
            {ActionsComponent}
          </div>
        )}
        {ChartContent}
      </div>
    );
  }

  // 使用Card包装
  return (
    <Card
      className={className}
      style={style}
      title={
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <span>{title}</span>
          {ActionsComponent}
        </div>
      }
      extra={FiltersComponent}
    >
      {ChartContent}
    </Card>
  );
};

export default RelationshipNetworkGraph;

export type {
  NetworkCategory,
  NetworkGraphData,
  NetworkGraphFilters,
  NetworkGraphProps,
  NetworkLink,
  NetworkNode,
} from './types';
