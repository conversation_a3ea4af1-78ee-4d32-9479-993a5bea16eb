import { PlusOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { Upload, message } from 'antd';
import React, { useState } from 'react';

export interface PhotoUploadProps {
  entityType: string;
  entityId: number;
  maxCount?: number;
  onSuccess?: () => void;
}

const PhotoUpload: React.FC<PhotoUploadProps> = ({
  maxCount = 10,
  onSuccess,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  const handleUpload = async (_file: File) => {
    void _file;
    try {
      // 这里应该调用上传照片的API
      // await uploadElementPhoto(entityType, entityId, file);
      message.success('上传成功');
      onSuccess?.();
    } catch (error: any) {
      console.error('上传照片失败:', error);
      message.error(error?.message || '上传失败');
    } finally {
      // setUploading(false);
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传</div>
    </div>
  );

  return (
    <Upload
      listType="picture-card"
      fileList={fileList}
      onChange={handleChange}
      beforeUpload={(file) => {
        handleUpload(file);
        return false;
      }}
      accept="image/*"
      multiple
    >
      {fileList.length >= maxCount ? null : uploadButton}
    </Upload>
  );
};

export default PhotoUpload;
