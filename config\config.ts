import { defineConfig } from '@umijs/max';
import routes from './routes';

export default defineConfig({
  // 在构建过程中对立即调用函数表达式 (IIFE) 进行压缩。这可以帮助减小构建输出的文件大小，提高前端应用的性能
  esbuildMinifyIIFE: true,
  antd: {},
  access: {},
  model: {},
  dva: {},
  initialState: {},
  request: {},
  styledComponents: {
    babelPlugin: {},
  },
  layout: {
    title: '关中历史城镇营建经验数据分析平台',
  },
  favicons: ['/favicon.svg'],
  define: {
    // 高德地图配置
    AMAP_CONFIG: {
      // 高德地图 API Key（需要在高德开放平台申请）
      key: process.env.AMAP_KEY || '5241c2421a303eeb89c0f5f1aadd04cc',

      // 高德地图安全密钥（可选，用于更高的安全性）
      securityJsCode:
        process.env.AMAP_SECURITY_CODE || 'e1ad54f8442f742ee317abe4e840acad',

      // API 版本
      version: '2.0',

      // 默认插件
      plugins: [
        'AMap.Scale',
        'AMap.ToolBar',
        'AMap.InfoWindow',
        'AMap.Marker',
        'AMap.Geocoder',
      ],

      // 默认地图配置
      defaultMapOptions: {
        zoom: 11,
        center: [108.9398, 34.3412], // 关中地区中心坐标
        mapStyle: 'amap://styles/normal',
        features: ['bg', 'road', 'building', 'point'],
        showLabel: true,
      },

      // 坐标系配置
      coordinateSystem: {
        // 数据库存储的坐标系（实际为GCJ02）
        storage: 'GCJ02',
        // 地图显示使用的坐标系（高德地图固定为GCJ02）
        display: 'GCJ02',
        // 是否启用坐标转换（GCJ02->GCJ02无需转换）
        enableConversion: false,
        // 输入坐标系（用户输入的坐标系）
        input: 'GCJ02',
      },

      // 标记点图标配置（统一使用古城图标）
      markerIcons: {
        ancientCity: '/images/markers/ancient-city.svg',
        culturalElement: '/images/markers/ancient-city.svg',
        default: '/images/markers/ancient-city.svg',
      },
    },
  },
  routes,
  npmClient: 'pnpm',
  proxy: {
    '/api': {
      target: 'http://127.0.0.1:7001',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
  },
});
