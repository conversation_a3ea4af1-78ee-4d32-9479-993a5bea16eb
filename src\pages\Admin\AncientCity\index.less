.ancient-city-management {
  background: #f5f5f5;
  min-height: 100vh;

  .search-card,
  .ant-card {
    .ant-card-body {
      padding: 20px;
    }
  }

  .search-card {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  // 表格样式
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }

  // 操作按钮样式
  .ant-btn-link {
    padding: 4px 8px;
    height: auto;
  }

  // 状态标签样式
  .ant-tag {
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ancient-city-management {
    .search-card .ant-card-body {
      padding: 16px;
    }

    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px;
        font-size: 12px;
      }
    }
  }
}
