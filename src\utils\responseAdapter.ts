/**
 * @file 响应格式适配器
 * @description 处理新旧API响应格式的兼容性
 * <AUTHOR> Assistant
 * @date 2025-10-09
 */

/**
 * 检查响应是否为新格式
 */
export function isNewResponseFormat(
  response: any,
): response is API.ResType<any> {
  return (
    typeof response.success === 'boolean' &&
    typeof response.message === 'string' &&
    typeof response.timestamp === 'string'
  );
}

/**
 * 检查响应是否为旧格式
 */
export function isLegacyResponseFormat(
  response: any,
): response is API.LegacyResType<any> {
  return (
    typeof response.errCode === 'number' &&
    (response.msg === undefined || typeof response.msg === 'string')
  );
}

/**
 * 将新格式响应转换为旧格式（用于兼容现有代码）
 */
export function adaptNewToLegacy<T>(
  response: API.ResType<T>,
): API.LegacyResType<T> {
  return {
    errCode: response.success ? 0 : response.code || -1,
    data: response.data,
    msg: response.message,
  };
}

/**
 * 将旧格式响应转换为新格式
 */
export function adaptLegacyToNew<T>(
  response: API.LegacyResType<T>,
): API.ResType<T> {
  return {
    success: response.errCode === 0,
    message: response.msg || (response.errCode === 0 ? '操作成功' : '操作失败'),
    data: response.data,
    code: response.errCode,
    timestamp: new Date().toISOString(),
  };
}

/**
 * 统一响应格式适配器
 * 自动检测响应格式并转换为旧格式（保持向后兼容）
 */
export function adaptResponse<T>(response: any): API.LegacyResType<T> {
  if (isNewResponseFormat(response)) {
    return adaptNewToLegacy(response);
  } else if (isLegacyResponseFormat(response)) {
    return response;
  } else {
    // 未知格式，返回错误响应
    return {
      errCode: -1,
      msg: '响应格式错误',
    };
  }
}

/**
 * 检查响应是否成功
 */
export function isResponseSuccess(response: any): boolean {
  if (isNewResponseFormat(response)) {
    return response.success;
  } else if (isLegacyResponseFormat(response)) {
    return response.errCode === 0;
  }
  return false;
}

/**
 * 获取响应错误信息
 */
export function getResponseMessage(response: any): string {
  if (isNewResponseFormat(response)) {
    return response.message;
  } else if (isLegacyResponseFormat(response)) {
    return response.msg || '未知错误';
  }
  return '响应格式错误';
}

/**
 * 获取响应数据
 */
export function getResponseData<T>(response: any): T | undefined {
  if (isNewResponseFormat(response) || isLegacyResponseFormat(response)) {
    return response.data;
  }
  return undefined;
}
