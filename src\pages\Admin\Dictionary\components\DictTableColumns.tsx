/**
 * @file 字典表格列配置组件
 * @description 根据字典类型动态生成表格列配置，包括基础列和操作列
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import type { TableColumnProps, TableColumnsType } from 'antd';
import { Badge, Button, Popconfirm, Space, Switch } from 'antd';
import { COLUMN_WIDTHS, MESSAGES } from '../constants';
import type { DictType, RelationshipDict, TypeDict } from '../dict-types';

interface DictTableColumnsProps {
  type: DictType;
  onEdit: (record: any, type: DictType) => void;
  onDelete: (id: number, type: DictType) => void;
  onStatusToggle: (id: number, type: DictType) => void;
}

// 检查记录是否有子项的工具函数
const hasChildren = (record: any): boolean => {
  return record.children && record.children.length > 0;
};

// 获取删除确认消息
const getDeleteConfirmMessage = (record: any): string => {
  return hasChildren(record)
    ? MESSAGES.confirm.deleteWithChildren
    : MESSAGES.confirm.delete;
};

export const useDictTableColumns = ({
  type,
  onEdit,
  onDelete,
  onStatusToggle,
}: DictTableColumnsProps) => {
  // 基础列配置
  const baseColumns: TableColumnProps<any>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: COLUMN_WIDTHS.id,
      hidden: true,
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      width: COLUMN_WIDTHS.sort,
      align: 'center' as const,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: COLUMN_WIDTHS.status,
      render: (status: number, record: any) => (
        <Space>
          <Badge status={status === 1 ? 'success' : 'default'} />
          <Switch
            checked={status === 1}
            onChange={() => onStatusToggle(record.id, type)}
            size="small"
          />
        </Space>
      ),
    },
  ];

  // 操作列
  const actionColumn = {
    title: '操作',
    key: 'action',
    align: 'center' as const,
    width: COLUMN_WIDTHS.action,
    render: (_: any, record: any) => (
      <Space size="middle">
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => onEdit(record, type)}
          style={{ padding: 0 }}
        >
          编辑
        </Button>
        <Popconfirm
          title={getDeleteConfirmMessage(record)}
          description={
            hasChildren(record)
              ? `该记录包含 ${record.children?.length || 0} 个子项`
              : undefined
          }
          onConfirm={() => onDelete(record.id, type)}
          okText="确定删除"
          cancelText="取消"
          okType={hasChildren(record) ? 'danger' : 'primary'}
          icon={
            hasChildren(record) ? (
              <DeleteOutlined style={{ color: '#ff4d4f' }} />
            ) : undefined
          }
        >
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            style={{ padding: 0 }}
          >
            删除
          </Button>
        </Popconfirm>
      </Space>
    ),
  };

  // 根据类型返回不同的列配置
  const getColumns = (): TableColumnsType<any> => {
    switch (type) {
      case 'type':
        return [
          baseColumns[0], // ID
          { title: '类型名称', dataIndex: 'typeName', key: 'typeName' },
          {
            title: '类型编码',
            dataIndex: 'typeCode',
            key: 'typeCode',
            width: COLUMN_WIDTHS.code,
          },
          baseColumns[1], // 排序
          baseColumns[2], // 状态
          {
            title: '描述',
            dataIndex: 'typeDesc',
            key: 'typeDesc',
            ellipsis: true,
          },
          actionColumn,
        ] as TableColumnsType<TypeDict>;

      case 'relation':
        return [
          baseColumns[0], // ID
          { title: '关系名称', dataIndex: 'relationName', key: 'relationName' },
          {
            title: '关系编码',
            dataIndex: 'relationCode',
            key: 'relationCode',
            width: COLUMN_WIDTHS.code,
          },
          baseColumns[1], // 排序
          baseColumns[2], // 状态
          {
            title: '描述',
            dataIndex: 'relationDesc',
            key: 'relationDesc',
            ellipsis: true,
          },
          actionColumn,
        ] as TableColumnsType<RelationshipDict>;

      default:
        return [];
    }
  };

  return getColumns();
};
