/**
 * @file 文化要素操作管理Hook
 * @description 管理文化要素的增删改查、导入导出等操作功能
 * <AUTHOR> Assistant
 * @date 2025-10-09
 */

import {
  deleteCulturalElement,
  executeCulturalElementImport,
  exportCulturalElements,
  getCulturalElementImportTemplate,
  previewCulturalElementImport,
} from '@/services/culturalElement';
import { message, Modal } from 'antd';
import { useCallback, useState } from 'react';

export interface UseCulturalElementOperationsReturn {
  // 加载状态
  deleteLoading: boolean;
  batchDeleteLoading: boolean;
  importLoading: boolean;
  exportLoading: boolean;

  // 操作方法
  handleDelete: (
    id: number,
    name: string,
    onSuccess?: () => void,
  ) => Promise<void>;
  handleBatchDelete: (
    ids: React.Key[],
    onSuccess?: () => void,
    onClear?: () => void,
  ) => Promise<void>;
  handleImport: (
    file: File,
    onSuccess?: () => void,
  ) => Promise<{
    success: boolean;
    data?: API.CulturalElementImportResponse;
    message?: string;
  }>;
  handlePreviewImport: (file: File) => Promise<{
    success: boolean;
    data?: API.CulturalElementImportPreviewResponse;
    message?: string;
  }>;
  handleExport: (params?: any) => Promise<void>;
  handleDownloadTemplate: () => Promise<void>;
}

/**
 * 文化要素操作管理Hook
 */
const useCulturalElementOperations = (): UseCulturalElementOperationsReturn => {
  // 加载状态
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [batchDeleteLoading, setBatchDeleteLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  // 删除单个文化要素
  const handleDelete = useCallback(
    async (id: number, name: string, onSuccess?: () => void) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除文化要素"${name}"吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        okType: 'danger',
        onOk: async () => {
          setDeleteLoading(true);
          try {
            const response = await deleteCulturalElement(id);

            if (response.errCode === 0) {
              message.success('删除成功');
              onSuccess?.();
            } else {
              message.error(response.msg || '删除失败');
            }
          } catch (error: any) {
            console.error('删除文化要素失败:', error);
            message.error(error?.message || '删除失败');
          } finally {
            setDeleteLoading(false);
          }
        },
      });
    },
    [],
  );

  // 批量删除文化要素
  const handleBatchDelete = useCallback(
    async (ids: React.Key[], onSuccess?: () => void, onClear?: () => void) => {
      if (ids.length === 0) {
        message.warning('请选择要删除的文化要素');
        return;
      }

      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${ids.length} 个文化要素吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        okType: 'danger',
        onOk: async () => {
          setBatchDeleteLoading(true);
          try {
            // 由于没有批量删除API，使用循环删除
            let successCount = 0;
            let failureCount = 0;
            let firstError: string | undefined;

            for (const id of ids) {
              try {
                const response = await deleteCulturalElement(id as number);
                if (response.errCode === 0) {
                  successCount++;
                } else {
                  failureCount++;
                  if (!firstError) {
                    firstError = response.msg;
                  }
                }
              } catch (error: any) {
                failureCount++;
                if (!firstError) {
                  firstError = error?.message;
                }
              }
            }

            if (successCount > 0) {
              message.success(
                `成功删除 ${successCount} 个文化要素${
                  failureCount > 0 ? `，失败 ${failureCount} 个` : ''
                }`,
              );
              onSuccess?.();
              onClear?.();
            } else {
              message.error(firstError || '批量删除失败');
            }
          } catch (error: any) {
            console.error('批量删除文化要素失败:', error);
            message.error(error?.message || '批量删除失败');
          } finally {
            setBatchDeleteLoading(false);
          }
        },
      });
    },
    [],
  );

  // 预览导入数据
  const handlePreviewImport = useCallback(async (file: File) => {
    try {
      const response = await previewCulturalElementImport(file);

      if (response.errCode === 0 && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          message: response.msg || '预览失败',
        };
      }
    } catch (error: any) {
      console.error('预览导入数据失败:', error);
      return {
        success: false,
        message: error?.message || '预览失败',
      };
    }
  }, []);

  // 导入文化要素数据
  const handleImport = useCallback(
    async (file: File, onSuccess?: () => void) => {
      setImportLoading(true);
      try {
        const response = await executeCulturalElementImport(file);

        if (response.errCode === 0 && response.data) {
          onSuccess?.();
          return {
            success: true,
            data: response.data,
          };
        } else {
          return {
            success: false,
            message: response.msg || '导入失败',
          };
        }
      } catch (error: any) {
        console.error('导入文化要素数据失败:', error);
        return {
          success: false,
          message: error?.message || '导入失败',
        };
      } finally {
        setImportLoading(false);
      }
    },
    [],
  );

  // 导出文化要素数据
  const handleExport = useCallback(async (params?: any) => {
    setExportLoading(true);
    try {
      const response = await exportCulturalElements(params);

      if (response.errCode === 0 && response.data) {
        // 根据返回的数据类型处理下载
        if (response.data.type === 'Buffer' && response.data.data) {
          // 如果返回的是Buffer类型，数据在data.data数组中
          try {
            // 将数组转换为Uint8Array
            const bytes = new Uint8Array(response.data.data);

            // 创建Blob并下载
            const blob = new Blob([bytes], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download =
              response.data.filename ||
              `文化要素数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            message.success('导出成功');
          } catch (bufferError) {
            console.error('处理Buffer数据失败:', bufferError);
            message.error('文件处理失败，请重试');
          }
        } else if (response.data.downloadUrl) {
          // 如果返回的是下载链接
          const link = document.createElement('a');
          link.href = response.data.downloadUrl;
          link.download =
            response.data.filename ||
            `文化要素数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          message.success('导出成功');
        } else {
          message.error('未找到可下载的文件数据');
        }
      } else {
        message.error(response.msg || '导出失败');
      }
    } catch (error: any) {
      console.error('导出文化要素数据失败:', error);
      message.error(error?.message || '导出失败');
    } finally {
      setExportLoading(false);
    }
  }, []);

  // 下载导入模板
  const handleDownloadTemplate = useCallback(async () => {
    try {
      const response = await getCulturalElementImportTemplate();

      if (response.errCode === 0 && response.data) {
        // 根据返回的数据类型处理下载
        if (response.data.buffer) {
          // 如果返回的是base64编码的文件内容
          try {
            // 将base64字符串转换为二进制数据
            const binaryString = atob(response.data.buffer);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }

            // 创建Blob并下载
            const blob = new Blob([bytes], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = response.data.filename || '文化要素导入模板.xlsx';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            message.success('模板下载成功');
          } catch (decodeError) {
            console.error('解码base64文件失败:', decodeError);
            message.error('文件解码失败，请重试');
          }
        } else if (response.data.downloadUrl) {
          // 如果返回的是下载链接
          const link = document.createElement('a');
          link.href = response.data.downloadUrl;
          link.download = response.data.filename || '文化要素导入模板.xlsx';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          message.success('模板下载成功');
        } else {
          message.error('未找到可下载的文件数据');
        }
      } else {
        message.error(response.msg || '模板下载失败');
      }
    } catch (error: any) {
      console.error('下载模板失败:', error);
      message.error(error?.message || '模板下载失败');
    }
  }, []);

  return {
    // 加载状态
    deleteLoading,
    batchDeleteLoading,
    importLoading,
    exportLoading,

    // 操作方法
    handleDelete,
    handleBatchDelete,
    handleImport,
    handlePreviewImport,
    handleExport,
    handleDownloadTemplate,
  };
};

export default useCulturalElementOperations;
