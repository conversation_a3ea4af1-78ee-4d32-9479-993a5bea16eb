/* 数据质量报告样式 */
.data-quality-report {
  .quality-card {
    .ant-statistic-title {
      font-size: 14px;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      margin-bottom: 12px;
    }

    .ant-progress {
      margin-bottom: 8px;
    }
  }

  .missing-fields-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
    }

    .ant-tag {
      font-weight: 500;
    }
  }

  .chart-container {
    .echarts-for-react {
      width: 100% !important;
    }
  }

  .system-status {
    .ant-descriptions-item-label {
      font-weight: 500;
      color: #666;
    }

    .ant-descriptions-item-content {
      color: #333;
    }
  }

  .quick-actions {
    .ant-btn {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #999;

    .anticon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #52c41a;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-quality-report {
    .ant-col {
      margin-bottom: 16px;
    }

    .ant-descriptions {
      .ant-descriptions-item {
        padding-bottom: 12px;
      }
    }

    .ant-table {
      font-size: 12px;
    }
  }
}

/* 加载状态优化 */
.ant-spin-container {
  min-height: 200px;
}

/* 进度条颜色渐变 */
.ant-progress-bg {
  transition: all 0.3s ease;
}

/* 卡片悬停效果 */
.ant-card {
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
  }
}

/* 按钮组样式 */
.ant-space-vertical {
  width: 100%;

  .ant-btn {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

/* 统计数值样式 */
.ant-statistic-content-value {
  font-weight: 600;
}

/* 标签样式优化 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}
