import { Empty, Spin } from 'antd';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';
import './index.less';

export interface RelationshipGraphProps {
  relations: API.ElementRelation[];
  loading?: boolean;
  centerNodeId: number;
  centerNodeName: string;
  centerNodeType: string;
  height?: number;
}

const RelationshipGraph: React.FC<RelationshipGraphProps> = ({
  relations,
  loading = false,
  centerNodeId,
  centerNodeName,
  centerNodeType,
  height = 400,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<echarts.ECharts | null>(null);

  // 构建图谱数据
  const buildGraphData = () => {
    if (!relations.length) return { nodes: [], links: [] };

    const nodes: any[] = [];
    const links: any[] = [];
    const nodeMap = new Map();

    // 添加中心节点
    const centerNode = {
      id: centerNodeId,
      name: centerNodeName,
      category: 0,
      symbolSize: 60,
      itemStyle: {
        color: '#1890ff',
      },
      label: {
        show: true,
        fontSize: 14,
        fontWeight: 'bold',
      },
    };
    nodes.push(centerNode);
    nodeMap.set(centerNodeId, centerNode);

    // 添加关联节点和连线
    relations.forEach((relation) => {
      const isSource = relation.sourceElementId === centerNodeId;
      const relatedNodeId = isSource
        ? relation.targetElementId
        : relation.sourceElementId;
      const relatedNodeName = isSource
        ? relation.targetElementName
        : relation.sourceElementName;

      if (!nodeMap.has(relatedNodeId)) {
        const relatedNode = {
          id: relatedNodeId,
          name: relatedNodeName,
          category: 1,
          symbolSize: 40,
          itemStyle: {
            color: '#52c41a',
          },
          label: {
            show: true,
            fontSize: 12,
          },
        };
        nodes.push(relatedNode);
        nodeMap.set(relatedNodeId, relatedNode);
      }

      // 添加连线
      links.push({
        source: isSource ? centerNodeId : relatedNodeId,
        target: isSource ? relatedNodeId : centerNodeId,
        label: {
          show: true,
          formatter: relation.relationName,
          fontSize: 10,
        },
        lineStyle: {
          color: '#999',
          width: 2,
        },
      });
    });

    return { nodes, links };
  };

  useEffect(() => {
    if (!containerRef.current || loading) {
      return;
    }

    // 初始化或获取图表实例
    if (!chartRef.current) {
      chartRef.current = echarts.init(containerRef.current);
    }

    if (relations.length === 0) {
      chartRef.current.clear();
      return;
    }

    const { nodes, links } = buildGraphData();

    const option = {
      title: {
        text: `${centerNodeName} 关系图谱`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          if (params.dataType === 'node') {
            return `${params.data.name}`;
          } else if (params.dataType === 'edge') {
            return `${params.data.label?.formatter || '关联'}`;
          }
          return '';
        },
      },
      legend: {
        data: ['中心节点', '关联节点'],
        bottom: 10,
      },
      series: [
        {
          type: 'graph',
          layout: 'force',
          data: nodes,
          links: links,
          categories: [{ name: '中心节点' }, { name: '关联节点' }],
          roam: true,
          focusNodeAdjacency: true,
          draggable: true,
          force: {
            repulsion: 1000,
            gravity: 0.1,
            edgeLength: 150,
            layoutAnimation: true,
          },
          lineStyle: {
            color: 'source',
            curveness: 0.1,
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 4,
            },
          },
        },
      ],
      animationDuration: 1500,
      animationEasingUpdate: 'quinticInOut',
    };

    chartRef.current.setOption(option, true);

    // 处理窗口大小变化
    const handleResize = () => {
      chartRef.current?.resize();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [relations, loading, centerNodeId, centerNodeName, centerNodeType]);

  // 组件卸载时销毁图表
  useEffect(() => {
    return () => {
      if (chartRef.current) {
        chartRef.current.dispose();
        chartRef.current = null;
      }
    };
  }, []);

  if (loading) {
    return (
      <div className="relationship-graph-loading" style={{ height }}>
        <Spin size="large" />
      </div>
    );
  }

  if (relations.length === 0) {
    return (
      <div className="relationship-graph-empty" style={{ height }}>
        <Empty description="暂无关联关系" />
      </div>
    );
  }

  return (
    <div className="relationship-graph">
      <div ref={containerRef} className="graph-container" style={{ height }} />
    </div>
  );
};

export default RelationshipGraph;
