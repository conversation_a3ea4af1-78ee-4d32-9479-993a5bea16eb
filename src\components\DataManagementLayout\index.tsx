/**
 * @file 数据管理布局组件
 * @description 通用的数据管理页面布局，包含左侧区域选择器和右侧内容区域
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */
import { Card, Col, Row } from 'antd';
import React from 'react';
import './index.less';

export interface DataManagementLayoutProps {
  /** 左侧选择器的宽度，默认为6 */
  selectorSpan?: number;
  /** 右侧内容区域的宽度，默认为18 */
  contentSpan?: number;
  /** 右侧内容区域 */
  children: React.ReactNode;
  /** 是否显示选择器，默认为true */
  showSelector?: boolean;
  /** 选择器标题，默认为"筛选" */
  selectorTitle?: string;
  /** 内容区域标题 */
  contentTitle?: string;
  /** 左侧选择器内容 */
  selectorContent?: React.ReactNode;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const DataManagementLayout: React.FC<DataManagementLayoutProps> = ({
  selectorSpan = 6,
  contentSpan = 18,
  children,
  showSelector = true,
  selectorTitle = '筛选',
  contentTitle,
  selectorContent,
  className,
  style,
}) => {
  // 如果不显示选择器，内容区域占满整行
  const actualContentSpan = showSelector ? contentSpan : 24;
  const actualSelectorSpan = showSelector ? selectorSpan : 0;

  return (
    <div className={`data-management-layout ${className || ''}`} style={style}>
      <Row gutter={16} style={{ height: '100%' }}>
        {/* 左侧区域选择器 */}
        {showSelector && (
          <Col span={actualSelectorSpan}>
            <Card title={selectorTitle} styles={{ body: { padding: '12px' } }}>
              {selectorContent || <div>选择器内容</div>}
            </Card>
          </Col>
        )}

        {/* 右侧内容区域 */}
        <Col span={actualContentSpan}>
          <Card
            title={contentTitle}
            className="content-card"
            styles={{ body: { padding: '16px' } }}
          >
            {children}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DataManagementLayout;
