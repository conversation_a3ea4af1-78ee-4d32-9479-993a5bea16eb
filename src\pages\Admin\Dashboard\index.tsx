// 移除模拟数据导入，使用真实API数据
import type { DictionaryState } from '@/models/dictionary';
import type { DashboardOverviewData } from '@/services/dashboard';
import { getDashboardOverview } from '@/services/dashboard';
import { connect } from '@umijs/max';
import { message, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import DashboardCards from './components/DashboardCards';

const { Title } = Typography;

interface AdminDashboardProps {
  dictionary: DictionaryState;
  dispatch: any;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({
  dictionary,
  dispatch,
}) => {
  const [overview, setOverview] = useState<DashboardOverviewData | null>(null);

  // 加载字典数据
  useEffect(() => {
    if (dictionary.typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
  }, [dictionary.typeList.length, dispatch]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getDashboardOverview();
        if (res.errCode === 0 && res.data) {
          setOverview(res.data);
        } else {
          message.error(res.msg || '获取仪表盘概览失败');
        }
      } catch (e: any) {
        message.error(e?.message || '获取仪表盘概览失败');
      } finally {
      }
    };
    fetchData();
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        仪表盘
      </Title>

      {/* 统一的仪表盘卡片组件 */}
      <DashboardCards overviewData={overview} />
    </div>
  );
};

export default connect(({ dictionary }: { dictionary: DictionaryState }) => ({
  dictionary,
}))(AdminDashboard);
