.cultural-element-detail {
  .ant-page-header {
    background: #fff;
    margin-bottom: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 3%);
  }

  .ant-card {
    margin-bottom: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 3%);

    &:last-child {
      margin-bottom: 0;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 20px 24px;
    }
  }

  .ant-descriptions {
    .ant-descriptions-item-label {
      font-weight: 500;
      color: #595959;
      width: 100px;
    }

    .ant-descriptions-item-content {
      color: #262626;
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    .ant-page-header {
      margin-bottom: 12px;

      .ant-page-header-heading-title {
        font-size: 18px;
      }

      .ant-page-header-heading-sub-title {
        font-size: 14px;
      }
    }

    .ant-card {
      margin-bottom: 12px;

      .ant-card-body {
        padding: 16px;
      }
    }

    .ant-descriptions {
      .ant-descriptions-item-label {
        width: 80px;
        font-size: 14px;
      }

      .ant-descriptions-item-content {
        font-size: 14px;
      }
    }

    // 在移动端将基本信息和位置信息改为单列布局
    .ant-row {
      .ant-col {
        &:nth-child(1),
        &:nth-child(2) {
          margin-bottom: 12px;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .ant-page-header {
      .ant-page-header-heading-extra {
        margin-top: 12px;

        .ant-btn {
          margin-right: 8px;
          font-size: 14px;
          height: 32px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .ant-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 16px;
        }
      }

      .ant-card-body {
        padding: 12px;
      }
    }

    .ant-descriptions {
      .ant-descriptions-item-label {
        width: 70px;
        font-size: 13px;
      }

      .ant-descriptions-item-content {
        font-size: 13px;
      }
    }

    // 小屏幕下的详细信息文本样式
    .ant-descriptions-item-content {
      div {
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}
