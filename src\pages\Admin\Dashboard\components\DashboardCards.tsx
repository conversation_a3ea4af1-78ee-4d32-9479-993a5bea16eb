/**
 * @file 仪表盘卡片组件
 * @description 统一管理仪表盘中的所有卡片组件
 * <AUTHOR> Assistant
 * @date 2025-10-20
 */

import type { DictionaryState } from '@/models/dictionary';
import type {
  DashboardOverviewData,
  SystemStatusData,
} from '@/services/dashboard';
import { getSystemStatus } from '@/services/dashboard';
import {
  BookOutlined,
  DatabaseOutlined,
  HomeOutlined,
  LinkOutlined,
} from '@ant-design/icons';
import { connect, history } from '@umijs/max';
import {
  Card,
  Col,
  Descriptions,
  List,
  Row,
  Statistic,
  Tag,
  Typography,
} from 'antd';
import ReactECharts from 'echarts-for-react';
import React, { useCallback, useEffect, useState } from 'react';

const { Text } = Typography;

interface DashboardCardsProps {
  style?: React.CSSProperties;
  overviewData?: DashboardOverviewData | null;
  dictionary: DictionaryState;
  dispatch: any;
}

const DashboardCards: React.FC<DashboardCardsProps> = ({
  style,
  overviewData,
  dictionary,
  dispatch,
}) => {
  const [systemStatus, setSystemStatus] = useState<SystemStatusData | null>(
    null,
  );
  const [loading, setLoading] = useState(false);

  const overview = overviewData;

  // 加载字典数据
  useEffect(() => {
    if (dictionary.typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
  }, [dictionary.typeList.length, dispatch]);

  const fetchSystemStatus = async () => {
    setLoading(true);
    try {
      const statusResponse = await getSystemStatus();
      if (statusResponse.errCode === 0 && statusResponse.data) {
        setSystemStatus(statusResponse.data);
      } else {
        console.warn('获取系统状态失败:', statusResponse.msg);
      }
    } catch (error) {
      console.error('获取系统状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemStatus();
  }, []);

  // 格式化运行时间
  const formatUptime = useCallback((seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  }, []);

  // 格式化内存使用
  const formatMemory = useCallback(
    (memory: {
      process: {
        heapUsed: number;
        heapTotal: number;
        external: number;
      };
      system: {
        total: number;
        free: number;
        used: number;
      };
      rss: number;
    }) => {
      // 进程堆内存使用情况
      const processHeapUsed = (memory.process.heapUsed / 1024 / 1024).toFixed(
        1,
      );
      const processHeapTotal = (memory.process.heapTotal / 1024 / 1024).toFixed(
        1,
      );

      // 系统内存使用情况
      const systemUsed = (memory.system.used / 1024 / 1024 / 1024).toFixed(1);
      const systemTotal = (memory.system.total / 1024 / 1024 / 1024).toFixed(1);

      // 计算使用率
      const heapUsagePercent = (
        (memory.process.heapUsed / memory.process.heapTotal) *
        100
      ).toFixed(1);
      const systemUsagePercent = (
        (memory.system.used / memory.system.total) *
        100
      ).toFixed(1);

      // 返回分行显示的格式，更易读
      return (
        <div>
          <div>
            堆内存: {processHeapUsed}MB/{processHeapTotal}MB ({heapUsagePercent}
            %)
          </div>
          <div>
            系统内存: {systemUsed}GB/{systemTotal}GB ({systemUsagePercent}%)
          </div>
        </div>
      );
    },
    [],
  );

  // 饼图配置
  const pieOption = {
    title: {
      text: '要素类型分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12,
      },
    },
    series: [
      {
        name: '要素数量',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          {
            name: '文化要素',
            value: overview?.statistics?.culturalElement || 0,
          },
          {
            name: '古城',
            value: overview?.statistics?.ancientCity || 0,
          },
          {
            name: '关联关系',
            value: overview?.statistics?.relationship || 0,
          },
          {
            name: '字典',
            value: overview?.statistics?.dict || 0,
          },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  // 最近更新数据
  const recentData = [
    ...(overview?.recentData?.culturalElements?.slice(0, 3).map((item) => ({
      title: item.name || '未命名',
      type: '文化要素',
      description: `${item.typeName || '未分类'} • ${
        item.ancientCityName || '未知古城'
      }`,
      time: new Date(item.createdAt).toLocaleDateString(),
      avatar: '文',
    })) || []),
    ...(overview?.recentData?.relationships?.slice(0, 2).map((item) => ({
      title: item.relationDict?.relationName || '未命名关系',
      type: '关联关系',
      description: `${item.sourceElement?.name} → ${item.targetElement?.name}`,
      time: new Date(item.createdAt).toLocaleDateString(),
      avatar: '关',
    })) || []),
  ].slice(0, 5);

  return (
    <div style={style}>
      {/* 统计数据卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="古城总数"
              value={overview?.statistics?.ancientCity || 0}
              prefix={<HomeOutlined style={{ color: '#13c2c2' }} />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="文化要素总数"
              value={overview?.statistics?.culturalElement || 0}
              prefix={<BookOutlined style={{ color: '#eb2f96' }} />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="关联关系总数"
              value={overview?.statistics?.relationship || 0}
              prefix={<LinkOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="字典总数"
              value={overview?.statistics?.dict || 0}
              prefix={<DatabaseOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表和列表 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card title="要素类型分布" style={{ height: 400 }}>
            <ReactECharts option={pieOption} style={{ height: '300px' }} />
          </Card>
        </Col>

        <Col span={16}>
          <Card title="按古城分布">
            <div style={{ height: '300px', overflowY: 'auto' }}>
              {overview?.distribution?.byRegion &&
              overview.distribution.byRegion.length > 0 ? (
                overview.distribution.byRegion.map((region, index) => {
                  return (
                    <div key={region.regionName} style={{ marginBottom: 12 }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom: 4,
                        }}
                      >
                        <Typography.Text>{region.regionName}</Typography.Text>
                        <Typography.Text strong>{region.count}</Typography.Text>
                      </div>
                      <div
                        style={{
                          width: '100%',
                          height: '8px',
                          background: '#f0f0f0',
                          borderRadius: '4px',
                          overflow: 'hidden',
                        }}
                      >
                        <div
                          style={{
                            width: `${region.percentage}%`,
                            height: '100%',
                            background: `hsl(${(index * 60) % 360}, 70%, 50%)`,
                            transition: 'width 0.3s ease',
                          }}
                        />
                      </div>
                    </div>
                  );
                })
              ) : (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Typography.Text type="secondary">
                    暂无区域分布数据
                  </Typography.Text>
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 快捷操作 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => {
              history.push('/admin/cultural-element');
            }}
          >
            <BookOutlined
              style={{ fontSize: 36, color: '#eb2f96', marginBottom: 12 }}
            />
            <div>文化要素管理</div>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => {
              history.push('/admin/ancient-city');
            }}
          >
            <HomeOutlined
              style={{ fontSize: 36, color: '#13c2c2', marginBottom: 12 }}
            />
            <div>古城管理</div>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => {
              history.push('/admin/element-relation');
            }}
          >
            <LinkOutlined
              style={{ fontSize: 36, color: '#722ed1', marginBottom: 12 }}
            />
            <div>关联关系管理</div>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => {
              history.push('/admin/dictionary');
            }}
          >
            <DatabaseOutlined
              style={{ fontSize: 36, color: '#52c41a', marginBottom: 12 }}
            />
            <div>字典管理</div>
          </Card>
        </Col>
      </Row>

      {/* 数据分布可视化 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={24}>
          <Card title="最近更新" style={{ height: 400 }}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              <List
                itemLayout="horizontal"
                dataSource={recentData}
                renderItem={(item) => (
                  <List.Item
                    extra={
                      <div style={{ marginLeft: '16px', color: '#999' }}>
                        {item.time}
                      </div>
                    }
                  >
                    <List.Item.Meta
                      avatar={
                        <div
                          style={{
                            width: 32,
                            height: 32,
                            borderRadius: '50%',
                            backgroundColor: '#1890ff',
                            color: 'white',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: 14,
                            fontWeight: 'bold',
                          }}
                        >
                          {item.avatar}
                        </div>
                      }
                      title={item.title}
                      description={
                        <div>
                          <div>{`${item.type}`}</div>
                          {item.description && (
                            <div
                              style={{
                                fontSize: '12px',
                                color: '#999',
                                marginTop: '4px',
                              }}
                            >
                              {item.description}
                            </div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 系统状态监控 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="系统状态" loading={loading}>
            {systemStatus ? (
              <Descriptions
                column={{ xs: 1, sm: 2, md: 4 }}
                bordered
                size="small"
              >
                <Descriptions.Item label="系统运行时间">
                  {formatUptime(systemStatus.uptime)}
                </Descriptions.Item>
                <Descriptions.Item label="内存使用">
                  <div
                    style={{
                      fontSize: '12px',
                      lineHeight: '1.4',
                    }}
                  >
                    {formatMemory(systemStatus.memory)}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="环境">
                  <Tag
                    color={
                      systemStatus.environment === 'production'
                        ? 'green'
                        : 'orange'
                    }
                  >
                    {systemStatus.environment}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="版本">
                  <Tag color="blue">{systemStatus.version}</Tag>
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <Text type="secondary">系统状态信息获取失败</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default connect(({ dictionary }: { dictionary: DictionaryState }) => ({
  dictionary,
}))(DashboardCards);
