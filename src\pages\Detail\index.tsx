import NetworkGraph from '@/components/NetworkGraph';
import PublicLayout from '@/components/PublicLayout';
import type { DictionaryState } from '@/models/dictionary';
import type { TypeDict } from '@/pages/Admin/Dictionary/dict-types';
import { getPublicElementRelationsByElement } from '@/services/elementRelation';
import { getCulturalElementDetailAdapter } from '@/utils/culturalElementAdapter';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { connect, useParams } from '@umijs/max';
import { Button, Card, Empty, Image, message, Spin, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import './styles.less';

interface DetailPageProps {
  typeList: TypeDict[];
  dispatch: any;
}

const DetailPage: React.FC<DetailPageProps> = ({ typeList, dispatch }) => {
  const { type, id } = useParams();
  const numericId = Number(id);

  const [loading, setLoading] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>(null);
  const [photos, setPhotos] = useState<
    Array<{ id: number; name: string; url: string }>
  >([]);
  const [relations, setRelations] = useState<API.ElementRelation[]>([]);
  const [relationsLoading, setRelationsLoading] = useState<boolean>(false);
  const [networkData, setNetworkData] = useState<API.NetworkGraphData | null>(
    null,
  );
  const [networkLoading, setNetworkLoading] = useState<boolean>(false);

  const getTypeName = (elementType?: string) => {
    const typeToCheck = elementType || type;
    switch (typeToCheck) {
      case 'culturalElement':
      case 'cultural_element':
        return '文化要素';
      case 'ancientCity':
      case 'ancient_city':
        return '古城';
      default:
        return '文化要素'; // 默认为文化要素
    }
  };

  // 关联信息分组函数 - 复用首页的实现
  const groupRelationsByType = (relations: any[]) => {
    const groups: { [key: string]: any[] } = {};
    relations.forEach((relation) => {
      // 优先使用relationName字段作为分组依据
      const relationName =
        relation.relationDict?.relationName ||
        relation.relationName ||
        '其他关系';

      if (!groups[relationName]) {
        groups[relationName] = [];
      }
      groups[relationName].push(relation);
    });

    return groups;
  };

  // 递归查找字典项
  const findDictItem = (list: any[], id: number): any => {
    for (const item of list) {
      if (item.id === id) {
        return item;
      }
      if (item.children && item.children.length > 0) {
        const found = findDictItem(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 获取类型名称
  const getTypeDictName = (typeDictId?: number) => {
    if (!typeDictId || !typeList || typeList.length === 0) {
      return detail?.typeDict?.typeName || '-';
    }
    const typeItem = findDictItem(typeList, typeDictId);
    return typeItem?.typeName || detail?.typeDict?.typeName || '-';
  };

  // 获取关联关系数据
  const loadRelations = async () => {
    if (!type || !numericId) return;

    setRelationsLoading(true);
    try {
      // 统一使用 culturalElement 类型
      const elementType = 'culturalElement';

      const response = await getPublicElementRelationsByElement(
        elementType,
        numericId,
      );
      if (response.errCode === 0) {
        const relationData = response.data || [];
        setRelations(relationData);

        // 关联关系数据已设置，网络图组件会自动处理
        console.log('🔗 关联关系数据:', relationData);
      }
    } catch (error: any) {
      console.error('加载关联关系失败:', error);
      message.error('加载关联关系失败');
    } finally {
      setRelationsLoading(false);
    }
  };

  // 获取网络图数据 - 复用首页的实现，添加要素ID参数
  const loadNetworkData = async (detailData?: any) => {
    const currentDetail = detailData || detail;
    if (!currentDetail) return;

    setNetworkLoading(true);
    try {
      // 使用首页相同的网络图接口，但添加要素ID参数
      const { getPublicNetworkGraphData } = await import(
        '@/services/relationship'
      );

      // 根据要素类型构建筛选参数
      const filters: any = {
        status: 1, // 只显示启用状态的关系
      };

      // 添加当前文化要素ID作为筛选条件
      filters.culturalElementId = numericId;
      // 可以根据需要添加更多筛选条件，比如按古城筛选
      if (currentDetail.ancientCityName) {
        filters.ancientCityName = currentDetail.ancientCityName;
      }

      console.log('🔍 网络图API参数:', filters);

      const response = await getPublicNetworkGraphData(filters);
      if (response.errCode === 0) {
        console.log('📊 网络图数据:', response.data);
        setNetworkData(response.data || null);
      } else {
        console.error('获取网络图数据失败:', response.msg);
        setNetworkData(null);
      }
    } catch (error: any) {
      console.error('加载网络图数据失败:', error);
      setNetworkData(null);
    } finally {
      setNetworkLoading(false);
    }
  };

  // 初始化字典数据
  useEffect(() => {
    if (!typeList || typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
  }, [dispatch, typeList]);

  useEffect(() => {
    if (!type || !numericId) return;
    const load = async () => {
      setLoading(true);
      try {
        let detailData = null;
        // 统一使用文化要素详情适配器
        const d = await getCulturalElementDetailAdapter(numericId);
        if (d.errCode === 0) {
          detailData = d.data || null;
          setDetail(detailData);
          // 照片信息现在直接包含在详情数据中
          setPhotos(detailData?.photos || []);
        }

        // 在详情加载完成后加载关联关系和网络图数据
        if (detailData) {
          await Promise.all([loadRelations(), loadNetworkData(detailData)]);
        }
      } catch (e: any) {
        message.error(e?.message || '加载详情失败');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [type, numericId]);

  // 获取banner图片
  const getBannerImage = () => {
    // 优先使用关联的图片
    if (photos && photos.length > 0) {
      return photos[0].url;
    }

    // 使用本地的默认背景图片
    const defaultImages = {
      mountain: '/images/banners/mountain.jpg',
      waterSystem: '/images/banners/water.jpg',
      historicalElement: '/images/banners/historical.jpg',
      default: '/images/banners/forest.jpg',
    };

    return (
      defaultImages[type as keyof typeof defaultImages] || defaultImages.default
    );
  };

  return (
    <div className="detail-page">
      <PublicLayout>
        {/* Banner区域 */}
        <div className="banner-section">
          <div
            className="banner-image"
            style={{
              backgroundImage: `url(${getBannerImage()})`,
            }}
          >
            <div className="banner-overlay">
              <div className="banner-content">
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={() => window.history.back()}
                  className="back-button"
                >
                  返回
                </Button>

                <div className="banner-title-section">
                  <h1 className="page-title">
                    {detail?.name || getTypeName()}
                  </h1>
                  {detail?.code && (
                    <div className="page-code">
                      <span className="code-tag">{detail.code}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          className="content-card"
          style={{ padding: '32px', margin: '32px auto', maxWidth: '1400px' }}
        >
          <div className="detail-layout">
            <Card loading={loading} title="基本信息" className="detail-card">
              <div className="basic-info">
                <div className="info-item">
                  <span className="info-label">名称：</span>
                  <span className="info-value">{detail?.name || '-'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">编码：</span>
                  <span className="info-value">{detail?.code || '-'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">所属区域：</span>
                  <span className="info-value">{detail?.cityName || '-'}</span>
                </div>
                {(detail?.typeDictId || detail?.typeDict) && (
                  <div className="info-item">
                    <span className="info-label">所属类型：</span>
                    <span className="info-value">
                      {getTypeDictName(detail?.typeDictId)}
                    </span>
                  </div>
                )}

                {/* 通用字段 - 根据数据动态显示 */}
                {detail?.height && (
                  <div className="info-item">
                    <span className="info-label">海拔高度：</span>
                    <span className="info-value">{detail.height}米</span>
                  </div>
                )}

                {detail?.longitude && detail?.latitude && (
                  <div className="info-item">
                    <span className="info-label">地理坐标：</span>
                    <span className="info-value">
                      {detail.longitude}, {detail.latitude}
                    </span>
                  </div>
                )}

                {detail?.locationDescription && (
                  <div className="info-item">
                    <span className="info-label">位置描述：</span>
                    <span className="info-value">
                      {detail.locationDescription}
                    </span>
                  </div>
                )}

                {/* 水系特有字段 */}
                {type === 'waterSystem' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">长度面积：</span>
                      <span className="info-value">
                        {detail?.lengthArea || '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">地理坐标：</span>
                      <span className="info-value">
                        {detail?.longitude && detail?.latitude
                          ? `${detail.longitude}, ${detail.latitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">
                        {detail?.locationDescription || '-'}
                      </span>
                    </div>
                  </>
                )}

                {/* 历史要素特有字段 */}
                {type === 'historicalElement' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">建造坐标：</span>
                      <span className="info-value">
                        {detail?.constructionLongitude &&
                        detail?.constructionLatitude
                          ? `${detail.constructionLongitude}, ${detail.constructionLatitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">
                        {detail?.locationDescription || '-'}
                      </span>
                    </div>
                    {detail?.constructionYear && (
                      <div className="info-item">
                        <span className="info-label">建造年份：</span>
                        <span className="info-value">
                          {detail.constructionYear < 0
                            ? `公元前${Math.abs(detail.constructionYear)}年`
                            : `公元${detail.constructionYear}年`}
                        </span>
                      </div>
                    )}
                  </>
                )}

                {/* 文化要素字段 */}
                {type === 'culturalElement' && (
                  <>
                    {detail?.ancientCityName && (
                      <div className="info-item">
                        <span className="info-label">所属古城：</span>
                        <span className="info-value">
                          {detail.ancientCityName}
                        </span>
                      </div>
                    )}
                    <div className="info-item">
                      <span className="info-label">地理坐标：</span>
                      <span className="info-value">
                        {detail?.longitude && detail?.latitude
                          ? `${detail.longitude}, ${detail.latitude}`
                          : '-'}
                      </span>
                    </div>
                    {detail?.locationDescription && (
                      <div className="info-item">
                        <span className="info-label">位置描述：</span>
                        <span className="info-value">
                          {detail.locationDescription}
                        </span>
                      </div>
                    )}
                    {detail?.constructionYear && (
                      <div className="info-item">
                        <span className="info-label">建造年份：</span>
                        <span className="info-value">
                          {detail.constructionYear < 0
                            ? `公元前${Math.abs(detail.constructionYear)}年`
                            : `公元${detail.constructionYear}年`}
                        </span>
                      </div>
                    )}
                    {detail?.height && (
                      <div className="info-item">
                        <span className="info-label">高度：</span>
                        <span className="info-value">{detail.height}米</span>
                      </div>
                    )}
                    {detail?.lengthArea && (
                      <div className="info-item">
                        <span className="info-label">长度面积：</span>
                        <span className="info-value">{detail.lengthArea}</span>
                      </div>
                    )}
                  </>
                )}

                {/* 历史记载 - 所有类型都有 */}
                {detail?.historicalRecords && (
                  <div className="info-item">
                    <span className="info-label">历史记载：</span>
                    <div className="info-value historical-records">
                      {detail.historicalRecords}
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <Card
              loading={loading}
              title="相关图片"
              className="detail-card photos-section"
            >
              {photos?.length ? (
                <div
                  style={{ display: 'flex', flexDirection: 'column', gap: 12 }}
                >
                  {photos.map((p) => (
                    <Image
                      key={p.id}
                      src={p.url}
                      className="photo-item"
                      style={{
                        width: '100%',
                        maxHeight: 480,
                        objectFit: 'cover',
                        borderRadius: '6px',
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div
                  style={{
                    color: '#999',
                    textAlign: 'center',
                    padding: '40px 0',
                    background: '#fafafa',
                    borderRadius: '6px',
                  }}
                >
                  暂无图片
                </div>
              )}
            </Card>

            <Card
              title={
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <span>关联信息 ({relations.length})</span>
                  {relationsLoading && <Spin size="small" />}
                </div>
              }
              className="detail-card relation-info"
              styles={{ body: { padding: '16px' } }}
            >
              {relationsLoading ? (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <Spin />
                  <div style={{ marginTop: 8 }}>加载关联信息中...</div>
                </div>
              ) : relations.length === 0 ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无关联信息"
                  className="empty-state"
                />
              ) : (
                <div>
                  {Object.entries(groupRelationsByType(relations)).map(
                    ([relationName, relationList]) => (
                      <div key={relationName} style={{ marginBottom: 24 }}>
                        <div
                          style={{
                            padding: '8px 12px',
                            background: '#f0f6ff',
                            borderRadius: '6px',
                            borderLeft: '3px solid #1890ff',
                            marginBottom: 12,
                          }}
                        >
                          <Typography.Text
                            strong
                            style={{ color: '#1890ff', fontSize: 14 }}
                          >
                            {relationName} ({relationList.length})
                          </Typography.Text>
                        </div>
                        <div style={{ paddingLeft: 8 }}>
                          {relationList.map((relation: any, index: number) => {
                            const sourceElement = relation.sourceElement;
                            const targetElement = relation.targetElement;

                            return (
                              <div
                                key={index}
                                style={{
                                  padding: '12px 16px',
                                  marginBottom: 8,
                                  background: '#fafafa',
                                  borderRadius: '8px',
                                  border: '1px solid #f0f0f0',
                                  transition: 'all 0.2s',
                                  cursor: 'pointer',
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.background = '#f0f6ff';
                                  e.currentTarget.style.borderColor = '#d6e4ff';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.background = '#fafafa';
                                  e.currentTarget.style.borderColor = '#f0f0f0';
                                }}
                              >
                                {/* 关系方向显示 - 完全复用首页样式 */}
                                <div
                                  style={{
                                    fontSize: 14,
                                    fontWeight: 500,
                                    color: '#333',
                                    marginBottom: 8,
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 8,
                                    flexWrap: 'wrap',
                                  }}
                                >
                                  <span style={{ color: '#1890ff' }}>
                                    {sourceElement?.name ||
                                      sourceElement?.cityName ||
                                      '未知要素'}
                                  </span>
                                  <div
                                    style={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 4,
                                    }}
                                  >
                                    <span
                                      style={{
                                        color: '#666',
                                        fontSize: 16,
                                        fontWeight: 'bold',
                                      }}
                                    >
                                      →
                                    </span>
                                    {/* 显示方向信息 */}
                                    {relation.direction && (
                                      <span
                                        style={{
                                          color: '#ff7a00',
                                          fontSize: 12,
                                          fontWeight: 500,
                                          background: '#fff7e6',
                                          padding: '2px 6px',
                                          borderRadius: '4px',
                                          border: '1px solid #ffd591',
                                        }}
                                      >
                                        {relation.direction}
                                      </span>
                                    )}
                                  </div>
                                  <span style={{ color: '#52c41a' }}>
                                    {targetElement?.name || '未知要素'}
                                  </span>
                                </div>

                                {/* 词条信息 - 复用首页样式 */}
                                {relation.term && (
                                  <div
                                    style={{
                                      color: '#666',
                                      fontSize: 13,
                                      marginBottom: 6,
                                      padding: '4px 8px',
                                      background: '#f9f9f9',
                                      borderRadius: '4px',
                                      borderLeft: '2px solid #d9d9d9',
                                    }}
                                  >
                                    <Typography.Text
                                      strong
                                      style={{ color: '#595959' }}
                                    >
                                      词条：
                                    </Typography.Text>
                                    {relation.term}
                                  </div>
                                )}

                                {/* 描述信息 - 复用首页样式 */}
                                {relation.description && (
                                  <div
                                    style={{
                                      color: '#666',
                                      fontSize: 13,
                                      padding: '4px 8px',
                                      background: '#f9f9f9',
                                      borderRadius: '4px',
                                      borderLeft: '2px solid #d9d9d9',
                                      lineHeight: 1.5,
                                      marginBottom: 6,
                                    }}
                                  >
                                    <Typography.Text
                                      strong
                                      style={{ color: '#595959' }}
                                    >
                                      描述：
                                    </Typography.Text>
                                    {relation.description}
                                  </div>
                                )}

                                {/* 记载信息 - 复用首页样式 */}
                                {relation.historicalRecord && (
                                  <div
                                    style={{
                                      color: '#666',
                                      fontSize: 13,
                                      padding: '4px 8px',
                                      background: '#f9f9f9',
                                      borderRadius: '4px',
                                      borderLeft: '2px solid #d9d9d9',
                                      lineHeight: 1.5,
                                    }}
                                  >
                                    <Typography.Text
                                      strong
                                      style={{ color: '#595959' }}
                                    >
                                      记载：
                                    </Typography.Text>
                                    {relation.historicalRecord}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    ),
                  )}
                </div>
              )}
            </Card>

            {/* 关系网络图 - 复用首页的实现 */}
            {detail && (
              <div
                className="detail-card network-graph"
                style={{ marginTop: '16px' }}
              >
                <NetworkGraph
                  data={networkData}
                  loading={networkLoading}
                  height={500}
                  title="关系网络图"
                  onNodeClick={(nodeData: any) => {
                    console.log('节点点击:', nodeData);
                    // 可以在这里实现节点点击跳转到对应要素详情
                  }}
                  onLinkClick={(linkData: any) => {
                    console.log('连线点击:', linkData);
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </PublicLayout>
    </div>
  );
};

export default connect(({ dictionary }: { dictionary: DictionaryState }) => ({
  typeList: dictionary.typeList,
}))(DetailPage);
