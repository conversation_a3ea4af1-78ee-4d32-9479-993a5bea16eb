/**
 * @file 字典数据dva model
 * @description 使用dva管理字典数据的全局缓存，方便其他页面引用
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import {
  RelationshipDict,
  TypeDict,
} from '@/pages/Admin/Dictionary/dict-types';
import {
  getPublicTypeDictTree,
  getRelationshipDictTree,
} from '@/services/dictionary';
import { AnyAction, Reducer } from '@umijs/max';
import { message } from 'antd';

// 注意：不在这里过滤禁用项，让组件自己决定是否显示禁用项

// 字典状态接口
export interface DictionaryState {
  typeList: TypeDict[];
  relationList: RelationshipDict[];
  loading: boolean;
}

// Model接口
interface IDictionaryModel {
  namespace: 'dictionary';
  state: DictionaryState;
  effects: {
    fetchTypeList: (
      action: { payload: Record<string, any> },
      effects: { call: Call; put: Put },
    ) => void;
    fetchRelationList: (
      action: { payload: Record<string, any> },
      effects: { call: Call; put: Put },
    ) => void;
    fetchAllLists: (
      action: { payload: Record<string, any> },
      effects: { call: Call; put: Put },
    ) => void;
  };
  reducers: {
    saveTypeList: Reducer<DictionaryState, { payload: TypeDict[] } & AnyAction>;
    saveRelationList: Reducer<
      DictionaryState,
      { payload: RelationshipDict[] } & AnyAction
    >;
    updateLoading: Reducer<DictionaryState, { payload: boolean } & AnyAction>;
  };
}

const DictionaryModel: IDictionaryModel = {
  namespace: 'dictionary',

  state: {
    typeList: [],
    relationList: [],
    loading: false,
  } as DictionaryState,

  effects: {
    /** 查询列表 **/
    *fetchTypeList(
      _: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'updateLoading', payload: true });
      try {
        // 使用公开的树型API获取类型字典数据
        const { errCode, msg, data } = yield call(getPublicTypeDictTree);
        if (errCode) {
          message.error(msg || '获取类型字典失败');
        } else {
          yield put({ type: 'saveTypeList', payload: data });
        }
      } catch (error) {
        console.error('获取类型字典失败:', error);
      } finally {
        yield put({ type: 'updateLoading', payload: false });
      }
    },

    *fetchRelationList(
      _: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'updateLoading', payload: true });
      try {
        // 使用公开的关系字典树型API
        const { errCode, msg, data } = yield call(getRelationshipDictTree);
        if (errCode) {
          message.error(msg || '获取关系字典失败');
        } else {
          yield put({ type: 'saveRelationList', payload: data });
        }
      } catch (error) {
        console.error('获取关系字典失败:', error);
      } finally {
        yield put({ type: 'updateLoading', payload: false });
      }
    },

    *fetchAllLists(
      _: { payload: Record<string, any> },
      { put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'fetchTypeList' });
      yield put({ type: 'fetchRelationList' });
    },
  },

  reducers: {
    saveTypeList(state: DictionaryState, action: { payload: TypeDict[] }) {
      return {
        ...state,
        typeList: action.payload, // 保留所有数据，让组件自己决定是否显示禁用项
      };
    },

    saveRelationList(
      state: DictionaryState,
      action: { payload: RelationshipDict[] },
    ) {
      return {
        ...state,
        relationList: action.payload, // 保留所有数据，让组件自己决定是否显示禁用项
      };
    },

    updateLoading(state: DictionaryState, action: { payload: boolean }) {
      return {
        ...state,
        loading: action.payload,
      };
    },
  },
};

export default DictionaryModel;
