import EnhancedStatistics from '@/components/NetworkGraph/EnhancedStatistics';
import NetworkGraphWithControls from '@/components/NetworkGraph/NetworkGraphWithControls';
import {
  createElementRelation,
  updateElementRelation,
} from '@/services/elementRelation';
import { getRelationshipStatistics } from '@/services/relationship';
import {
  BarChartOutlined,
  EditOutlined,
  EyeOutlined,
  ImportOutlined,
  ShareAltOutlined,
  TableOutlined,
} from '@ant-design/icons';
import { message, Modal, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import ElementRelationDetailModal from './components/ElementRelationDetailModal';
import ElementRelationForm from './components/ElementRelationForm';
import ElementRelationTable from './components/ElementRelationTable';
import RelationshipImport from './components/RelationshipImport';

export interface ElementRelationManagementProps {
  selectedCityId?: number;
}

const ElementRelationManagement: React.FC<ElementRelationManagementProps> = ({
  selectedCityId,
}) => {
  const [activeTab, setActiveTab] = useState('list');
  const [modalVisible, setModalVisible] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>(
    'create',
  );
  const [currentRecord, setCurrentRecord] =
    useState<API.ElementRelation | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [statistics, setStatistics] =
    useState<API.RelationshipStatisticsDTO | null>(null);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [formRef, setFormRef] = useState<any>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [detailRecord, setDetailRecord] = useState<API.ElementRelation | null>(
    null,
  );

  // 加载统计数据
  const loadStatistics = async () => {
    setStatisticsLoading(true);
    try {
      const response = await getRelationshipStatistics();
      if (response.errCode === 0) {
        setStatistics(response.data || null);
      } else {
        console.error('获取统计数据失败:', response.msg);
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    } finally {
      setStatisticsLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (activeTab === 'statistics') {
      loadStatistics();
    }
  }, [activeTab]);

  // 监听古城选择变化，触发数据刷新
  useEffect(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, [selectedCityId]);

  // 处理创建
  const handleCreate = () => {
    setModalMode('create');
    setCurrentRecord(null);
    setModalVisible(true);
  };

  // 处理编辑
  const handleEdit = (record: API.ElementRelation) => {
    setModalMode('edit');
    setCurrentRecord(record);
    setModalVisible(true);
  };

  // 处理查看
  const handleView = (record: API.ElementRelation) => {
    setModalMode('view');
    setCurrentRecord(record);
    setModalVisible(true);
  };

  // 处理保存
  const handleSave = async () => {
    if (!formRef) return;

    try {
      setConfirmLoading(true);
      const values = await formRef.validateFields();

      let response;
      if (modalMode === 'create') {
        response = await createElementRelation(values);
      } else {
        response = await updateElementRelation(currentRecord!.id, values);
      }

      if (response.errCode === 0) {
        message.success(modalMode === 'create' ? '创建成功' : '更新成功');
        setModalVisible(false);
        setRefreshTrigger((prev) => prev + 1);
        // 刷新统计数据
        if (activeTab === 'statistics') {
          loadStatistics();
        }
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setConfirmLoading(false);
    }
  };

  // 处理导出网络图
  const handleExportNetwork = () => {
    message.info('导出功能开发中...');
  };

  // 处理节点点击
  const handleNodeClick = (nodeData: any) => {
    console.log('节点点击:', nodeData);
    // 可以在这里处理节点点击事件，比如显示节点详情
  };

  // 处理连线点击
  const handleLinkClick = (linkData: any) => {
    console.log('连线点击:', linkData);
    // 可以在这里处理连线点击事件，比如显示关系详情
  };

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    if (key === 'statistics' && !statistics) {
      loadStatistics();
    }
  };

  // 标签页配置
  const tabItems = [
    {
      key: 'list',
      label: (
        <span>
          <TableOutlined />
          关系列表
        </span>
      ),
      children: (
        <ElementRelationTable
          refreshTrigger={refreshTrigger}
          onCreate={handleCreate}
          onEdit={handleEdit}
          onView={handleView}
          selectedCityId={selectedCityId}
        />
      ),
    },
    {
      key: 'import',
      label: (
        <span>
          <ImportOutlined />
          批量导入
        </span>
      ),
      children: (
        <RelationshipImport
          onImportSuccess={() => {
            setRefreshTrigger((prev) => prev + 1);
            message.success('导入成功');
          }}
        />
      ),
    },
    {
      key: 'network',
      label: (
        <span>
          <ShareAltOutlined />
          关系网络图
        </span>
      ),
      children: (
        <NetworkGraphWithControls
          title="要素关系网络图"
          isPublic={false}
          onExport={handleExportNetwork}
          onNodeClick={handleNodeClick}
          onLinkClick={handleLinkClick}
        />
      ),
    },
    {
      key: 'statistics',
      label: (
        <span>
          <BarChartOutlined />
          统计分析
        </span>
      ),
      children: (
        <EnhancedStatistics
          data={statistics}
          loading={statisticsLoading}
          onRefresh={loadStatistics}
        />
      ),
    },
  ];

  console.log('ElementRelationManagement render:', { activeTab, selectedCityId, tabItems });

  return (
    <>
      <div style={{ padding: '16px' }}>
        <p>Debug: activeTab = {activeTab}, selectedCityId = {selectedCityId}</p>
      </div>
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        size="large"
      />

      {/* 关系表单弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {modalMode === 'create' && (
              <ShareAltOutlined style={{ color: '#1890ff' }} />
            )}
            {modalMode === 'edit' && (
              <EditOutlined style={{ color: '#1890ff' }} />
            )}
            {modalMode === 'view' && (
              <EyeOutlined style={{ color: '#1890ff' }} />
            )}
            {modalMode === 'create'
              ? '新建要素关联'
              : modalMode === 'edit'
              ? '编辑要素关联'
              : '查看要素关联'}
          </div>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setFormRef(null);
        }}
        onOk={modalMode === 'view' ? () => setModalVisible(false) : handleSave}
        confirmLoading={confirmLoading}
        width={800}
      >
        <ElementRelationForm
          initialValues={currentRecord || { status: 1 }}
          onFormRef={setFormRef}
          readonly={modalMode === 'view'}
        />
      </Modal>

      {/* 详情弹窗 */}
      <ElementRelationDetailModal
        visible={detailModalVisible}
        record={detailRecord}
        onClose={() => {
          setDetailModalVisible(false);
          setDetailRecord(null);
        }}
      />
    </>
  );
};

export default ElementRelationManagement;
