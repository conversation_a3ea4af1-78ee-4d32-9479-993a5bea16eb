.cultural-element-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px 0;

  .content-card {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
  }

  // 顶部古城筛选区域
  .filter-section {
    margin-bottom: 24px;

    .filter-header {
      margin-bottom: 16px;
    }

    .ancient-city-filter {
      .city-tag {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 13px;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid #d9d9d9;
        margin-right: 8px;
        margin-bottom: 8px;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.ant-tag-checkable-checked {
          background: #1890ff;
          border-color: #1890ff;
          color: white;
        }
      }
    }
  }

  .filter-card {
    .filter-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  .list-container {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      .list-info {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    // 文化要素列表样式
    .cultural-element-list {
      .ant-list-grid {
        .ant-col {
          height: 100%;

          .ant-list-item {
            height: 100%;
          }
        }
      }
    }

    .element-card {
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 15%);
      }

      .ant-card-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0;
      }

      .element-cover {
        height: 200px;
        overflow: hidden;
        position: relative;

        .ant-image {
          width: 100%;
          height: 100%;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }
        }

        .cover-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 30%);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          color: white;
          font-size: 24px;
        }

        &:hover {
          img {
            transform: scale(1.05);
          }

          .cover-overlay {
            opacity: 1;
          }
        }
      }

      .card-content {
        padding: 16px;
        flex: 1;
        display: flex;
        flex-direction: column;

        .card-title {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;
          gap: 8px;

          h5 {
            flex: 1;
            min-width: 0;
          }
        }

        .card-description {
          flex: 1;

          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;
          }

          .description-text {
            margin-top: 8px;
            line-height: 1.4;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px 0;

    .content-card {
      padding: 0 16px;
    }

    .filter-section {
      .ancient-city-filter {
        .city-tag {
          margin-bottom: 8px;
        }
      }
    }

    .list-container {
      .list-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .element-card {
        .element-cover {
          height: 160px;
        }
      }
    }
  }
}

// 分页样式优化
.ant-pagination {
  margin-top: 32px;
  text-align: center;

  .ant-pagination-item {
    border-radius: 6px;
  }

  .ant-pagination-item-active {
    background: #1890ff;
    border-color: #1890ff;

    a {
      color: white;
    }
  }
}

// 筛选器样式
.ant-select {
  width: 100%;

  .ant-select-selector {
    border-radius: 6px;
  }
}

// 卡片网格间距调整
.ant-list-grid .ant-col > .ant-list-item {
  margin-bottom: 24px;
}

// 空状态样式
.ant-empty {
  padding: 60px 0;

  .ant-empty-description {
    color: #999;
  }
}
