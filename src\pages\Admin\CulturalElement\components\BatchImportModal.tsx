/**
 * @file 文化要素批量导入弹窗组件
 * @description 实现Excel文件的预览、验证和导入功能
 * <AUTHOR> Assistant
 * @date 2025-10-13
 */

import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  InfoCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import {
  Alert,
  Button,
  Card,
  Col,
  Descriptions,
  Modal,
  Row,
  Space,
  Steps,
  Table,
  Typography,
  Upload,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Dragger } = Upload;
const { Paragraph, Text } = Typography;

export interface BatchImportModalProps {
  visible: boolean;
  loading: boolean;
  onDownloadTemplate: () => Promise<void>;
  onPreview: (file: File) => Promise<{
    success: boolean;
    data?: API.CulturalElementImportPreviewResponse;
    message?: string;
  }>;
  onImport: (file: File) => Promise<{
    success: boolean;
    data?: API.CulturalElementImportResponse;
    message?: string;
  }>;
  onCancel: () => void;
}

const BatchImportModal: React.FC<BatchImportModalProps> = ({
  visible,
  loading,
  onDownloadTemplate,
  onPreview,
  onImport,
  onCancel,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewData, setPreviewData] =
    useState<API.CulturalElementImportPreviewResponse | null>(null);
  const [importResult, setImportResult] =
    useState<API.CulturalElementImportResponse | null>(null);

  // 重置状态
  const resetState = () => {
    setCurrentStep(0);
    setUploadFile(null);
    setFileList([]);
    setPreviewData(null);
    setImportResult(null);
  };

  // 弹窗关闭时重置状态
  useEffect(() => {
    if (!visible) {
      resetState();
    }
  }, [visible]);

  // 处理弹窗取消
  const handleCancel = () => {
    resetState();
    onCancel();
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      await onDownloadTemplate();
    } catch (error) {
      console.error('下载模板失败:', error);
    }
  };

  // 文件上传配置
  const uploadProps: UploadProps = {
    accept: '.xlsx,.xls',
    maxCount: 1,
    beforeUpload: (file) => {
      // 检查文件类型
      const isExcel =
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel' ||
        file.name.endsWith('.xlsx') ||
        file.name.endsWith('.xls');

      if (!isExcel) {
        message.error('只能上传Excel文件(.xlsx或.xls格式)');
        return false;
      }

      // 检查文件大小 (10MB)
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过10MB');
        return false;
      }

      setUploadFile(file);
      return false; // 阻止自动上传
    },
    onRemove: () => {
      setUploadFile(null);
      setPreviewData(null);
      setCurrentStep(0);
    },
    fileList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
  };

  // 预览数据
  const handlePreview = async () => {
    if (!uploadFile) {
      message.error('请先选择文件');
      return;
    }

    try {
      const result = await onPreview(uploadFile);
      if (result.success && result.data) {
        setPreviewData(result.data);
        setCurrentStep(1);
      } else {
        message.error(result.message || '预览失败');
      }
    } catch (error) {
      console.error('预览失败:', error);
      message.error('预览失败，请重试');
    }
  };

  // 执行导入
  const handleImport = async () => {
    if (!uploadFile) {
      message.error('请先选择文件');
      return;
    }

    try {
      const result = await onImport(uploadFile);
      if (result.success && result.data) {
        setImportResult(result.data);
        setCurrentStep(2);
        message.success('导入完成');
      } else {
        message.error(result.message || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    }
  };

  // 错误表格列定义
  const errorColumns = [
    {
      title: '行号',
      dataIndex: 'row',
      key: 'row',
      width: 80,
    },
    {
      title: '字段',
      dataIndex: 'field',
      key: 'field',
      width: 120,
    },
    {
      title: '错误值',
      dataIndex: 'value',
      key: 'value',
      width: 150,
      render: (value: any) => String(value || ''),
    },
    {
      title: '错误信息',
      dataIndex: 'message',
      key: 'message',
    },
  ];

  // 预览数据表格列定义
  const previewColumns = [
    { title: '名称', dataIndex: 'name', key: 'name', width: 150 },
    { title: '编号', dataIndex: 'code', key: 'code', width: 120 },
    { title: '类型名称', dataIndex: 'typeName', key: 'typeName', width: 100 },
    {
      title: '古城名称',
      dataIndex: 'cityName',
      key: 'cityName',
      width: 100,
    },
    { title: '经度', dataIndex: 'longitude', key: 'longitude', width: 100 },
    { title: '纬度', dataIndex: 'latitude', key: 'latitude', width: 100 },
    {
      title: '建造年份',
      dataIndex: 'constructionYear',
      key: 'constructionYear',
      width: 100,
    },
  ];

  // 步骤配置
  const steps = [
    {
      title: '上传文件',
      description: '选择Excel文件并预览数据',
    },
    {
      title: '数据验证',
      description: '检查数据格式和内容',
    },
    {
      title: '导入完成',
      description: '查看导入结果',
    },
  ];

  return (
    <Modal
      title="批量导入文化要素"
      open={visible}
      onCancel={handleCancel}
      width={1000}
      footer={null}
      destroyOnClose
    >
      <Steps current={currentStep} items={steps} style={{ marginBottom: 24 }} />

      {/* 步骤0: 文件上传 */}
      {currentStep === 0 && (
        <div>
          {/* 使用说明 */}
          <Alert
            message="导入说明"
            description={
              <div>
                <Paragraph>
                  1. 请先下载导入模板，按照模板格式填写数据
                  <br />
                  2. 支持.xlsx和.xls格式，文件大小不超过10MB
                  <br />
                  3. 必填字段：名称；可选字段：编号、类型名称、古城名称、坐标等
                  <br />
                  4. 类型名称和古城名称会自动转换为对应的ID
                </Paragraph>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          {/* 下载模板 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col span={18}>
                <Space direction="vertical" size="small">
                  <Text strong>
                    <DownloadOutlined /> 第一步：下载导入模板
                  </Text>
                  <Text type="secondary">
                    下载包含字段说明和示例数据的Excel模板
                  </Text>
                </Space>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={handleDownloadTemplate}
                  loading={loading}
                  block
                >
                  下载模板
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 文件上传 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col span={18}>
                <Space direction="vertical" size="small">
                  <Text strong>
                    <FileExcelOutlined /> 第二步：上传Excel文件
                  </Text>
                  <Text type="secondary">选择填写好的Excel文件进行上传</Text>
                </Space>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  onClick={handlePreview}
                  disabled={!uploadFile}
                  loading={loading}
                  block
                >
                  预览数据
                </Button>
              </Col>
            </Row>
          </Card>

          <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
            <p className="ant-upload-drag-icon">
              <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持.xlsx和.xls格式，文件大小不超过10MB
            </p>
          </Dragger>
        </div>
      )}

      {/* 步骤1: 数据预览和验证 */}
      {currentStep === 1 && previewData && (
        <div>
          {/* 预览结果统计 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Descriptions title="数据预览结果" column={4} size="small">
              <Descriptions.Item label="总行数">
                {previewData.totalRows}
              </Descriptions.Item>
              <Descriptions.Item label="有效行数">
                <Text
                  type={
                    previewData.validRows === previewData.totalRows
                      ? 'success'
                      : 'warning'
                  }
                >
                  {previewData.validRows}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="错误行数">
                <Text
                  type={previewData.errors.length > 0 ? 'danger' : 'success'}
                >
                  {previewData.errors.length}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {previewData.errors.length === 0 ? (
                  <Text type="success">
                    <CheckCircleOutlined /> 验证通过
                  </Text>
                ) : (
                  <Text type="warning">
                    <WarningOutlined /> 存在错误
                  </Text>
                )}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 错误信息 */}
          {previewData.errors.length > 0 && (
            <Card
              title={
                <Text type="danger">
                  <CloseCircleOutlined /> 数据验证错误 (
                  {previewData.errors.length} 个)
                </Text>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Table
                columns={errorColumns}
                dataSource={previewData.errors}
                rowKey={(record, index) =>
                  `${record.row}-${record.field}-${index}`
                }
                pagination={{ pageSize: 5 }}
                size="small"
              />
            </Card>
          )}

          {/* 预览数据 */}
          {previewData.previewData && previewData.previewData.length > 0 && (
            <Card
              title={
                <Text>
                  <InfoCircleOutlined /> 数据预览 (前5条)
                </Text>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Table
                columns={previewColumns}
                dataSource={previewData.previewData}
                rowKey="name"
                pagination={false}
                size="small"
                scroll={{ x: 800 }}
              />
            </Card>
          )}

          {/* 操作按钮 */}
          <Row justify="space-between">
            <Col>
              <Button onClick={() => setCurrentStep(0)}>返回上传</Button>
            </Col>
            <Col>
              <Space>
                <Button onClick={handleCancel}>取消</Button>
                <Button
                  type="primary"
                  onClick={handleImport}
                  disabled={previewData.errors.length > 0}
                  loading={loading}
                >
                  {previewData.errors.length > 0
                    ? '存在错误，无法导入'
                    : '确认导入'}
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      )}

      {/* 步骤2: 导入结果 */}
      {currentStep === 2 && importResult && (
        <div>
          {/* 导入结果统计 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Descriptions title="导入结果" column={3} size="small">
              <Descriptions.Item label="总行数">
                {importResult.totalRows}
              </Descriptions.Item>
              <Descriptions.Item label="成功导入">
                <Text type="success">{importResult.importedCount || 0}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="导入失败">
                <Text
                  type={
                    importResult.failedCount && importResult.failedCount > 0
                      ? 'danger'
                      : 'success'
                  }
                >
                  {importResult.failedCount || 0}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 导入状态 */}
          <Alert
            message={importResult.success ? '导入成功' : '导入完成（部分失败）'}
            description={importResult.message}
            type={importResult.success ? 'success' : 'warning'}
            showIcon
            style={{ marginBottom: 16 }}
          />

          {/* 失败记录 */}
          {importResult.errors && importResult.errors.length > 0 && (
            <Card
              title={
                <Text type="danger">
                  <CloseCircleOutlined /> 导入失败记录 (
                  {importResult.errors.length} 个)
                </Text>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Table
                columns={[
                  { title: '行号', dataIndex: 'row', key: 'row', width: 80 },
                  { title: '名称', dataIndex: 'name', key: 'name', width: 150 },
                  { title: '错误信息', dataIndex: 'error', key: 'error' },
                ]}
                dataSource={importResult.errors}
                rowKey={(record, index) => `${record.row}-${index}`}
                pagination={{ pageSize: 5 }}
                size="small"
              />
            </Card>
          )}

          {/* 操作按钮 */}
          <Row justify="end">
            <Col>
              <Button type="primary" onClick={handleCancel}>
                完成
              </Button>
            </Col>
          </Row>
        </div>
      )}
    </Modal>
  );
};

export default BatchImportModal;
