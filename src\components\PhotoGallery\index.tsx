import {
  batchDeletePhotos,
  deletePhotoFile,
  disassociatePhotos,
  setCulturalElementPrimaryPhoto,
  uploadFile,
  uploadUtils,
} from '@/services/upload';
import {
  DeleteOutlined,
  EyeOutlined,
  PlusOutlined,
  StarFilled,
  StarOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Empty,
  Image,
  Modal,
  Popconfirm,
  Space,
  Spin,
  Upload,
  message,
} from 'antd';
import React, { useState } from 'react';
import './index.less';

export interface PhotoGalleryProps {
  photos: API.PhotoRecord[];
  loading?: boolean;
  entityType: string;
  entityId: number;
  onUpdate?: () => void;
  editable?: boolean;
  maxCount?: number;
  primaryPhotoId?: number; // 主图ID
  enableBatchOperations?: boolean; // 是否启用批量操作
}

const PhotoGallery: React.FC<PhotoGalleryProps> = ({
  photos,
  loading = false,
  entityType,
  entityId,
  onUpdate,
  editable = true,
  maxCount = 10,
  primaryPhotoId,
  enableBatchOperations = false,
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [uploading, setUploading] = useState(false);
  const [selectedPhotoIds, setSelectedPhotoIds] = useState<number[]>([]);
  const [batchMode, setBatchMode] = useState(false);

  // 预览图片
  const handlePreview = (photo: API.PhotoRecord) => {
    setPreviewImage(photo.url);
    // setPreviewTitle(photo.name || '图片预览');
    setPreviewVisible(true);
  };

  // 删除图片
  const handleDelete = async (photoId: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这张图片吗？删除后将无法恢复。',
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await deletePhotoFile(photoId);
          if (response.errCode === 0) {
            message.success('删除成功');
            onUpdate?.();
          } else {
            message.error(response.msg || '删除失败');
          }
        } catch (error: any) {
          console.error('删除照片失败:', error);
          message.error(error?.message || '删除失败');
        }
      },
    });
  };

  // 上传图片
  const handleUpload = async (file: File) => {
    // 验证文件
    const validation = uploadUtils.validateFile(file);
    if (!validation.valid) {
      message.error(validation.message);
      return;
    }

    setUploading(true);
    try {
      const response = await uploadFile(file, {
        photoName: file.name,
        entityType: entityType as 'culturalElement',
        entityId: entityId,
      });

      if (response.errCode === 0) {
        message.success('上传成功');
        onUpdate?.();
      } else {
        message.error(response.msg || '上传失败');
      }
    } catch (error: any) {
      console.error('上传照片失败:', error);
      message.error(error?.message || '上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 设置主图
  const handleSetPrimary = async (photoId: number) => {
    if (entityType !== 'culturalElement') {
      message.error('只有文化要素支持设置主图');
      return;
    }

    try {
      const response = await setCulturalElementPrimaryPhoto(entityId, photoId);
      if (response.errCode === 0) {
        message.success('设置主图成功');
        onUpdate?.();
      } else {
        message.error(response.msg || '设置主图失败');
      }
    } catch (error: any) {
      console.error('设置主图失败:', error);
      message.error(error?.message || '设置主图失败');
    }
  };

  // 批量选择处理
  const handleSelectPhoto = (photoId: number, checked: boolean) => {
    if (checked) {
      setSelectedPhotoIds((prev) => [...prev, photoId]);
    } else {
      setSelectedPhotoIds((prev) => prev.filter((id) => id !== photoId));
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPhotoIds(photos.map((photo) => photo.id));
    } else {
      setSelectedPhotoIds([]);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedPhotoIds.length === 0) {
      message.warning('请先选择要删除的照片');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedPhotoIds.length} 张照片吗？删除后将无法恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await batchDeletePhotos(selectedPhotoIds);
          if (response.errCode === 0) {
            message.success(
              `成功删除 ${
                response.data?.count || selectedPhotoIds.length
              } 张照片`,
            );
            setSelectedPhotoIds([]);
            setBatchMode(false);
            onUpdate?.();
          } else {
            message.error(response.msg || '批量删除失败');
          }
        } catch (error: any) {
          console.error('批量删除失败:', error);
          message.error(error?.message || '批量删除失败');
        }
      },
    });
  };

  // 批量取消关联
  const handleBatchDisassociate = async () => {
    if (selectedPhotoIds.length === 0) {
      message.warning('请先选择要取消关联的照片');
      return;
    }

    try {
      const response = await disassociatePhotos(selectedPhotoIds);
      if (response.errCode === 0) {
        message.success(
          `成功取消 ${
            response.data?.count || selectedPhotoIds.length
          } 张照片的关联`,
        );
        setSelectedPhotoIds([]);
        setBatchMode(false);
        onUpdate?.();
      } else {
        message.error(response.msg || '批量取消关联失败');
      }
    } catch (error: any) {
      console.error('批量取消关联失败:', error);
      message.error(error?.message || '批量取消关联失败');
    }
  };

  if (loading) {
    return (
      <div className="photo-gallery-loading">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="photo-gallery">
      {/* 批量操作工具栏 */}
      {enableBatchOperations && editable && photos.length > 0 && (
        <div className="batch-toolbar">
          <div className="batch-controls">
            <Button
              type={batchMode ? 'primary' : 'default'}
              onClick={() => {
                setBatchMode(!batchMode);
                setSelectedPhotoIds([]);
              }}
            >
              {batchMode ? '退出批量操作' : '批量操作'}
            </Button>

            {batchMode && (
              <>
                <Checkbox
                  checked={selectedPhotoIds.length === photos.length}
                  indeterminate={
                    selectedPhotoIds.length > 0 &&
                    selectedPhotoIds.length < photos.length
                  }
                  onChange={(e) => handleSelectAll(e.target.checked)}
                >
                  全选 ({selectedPhotoIds.length}/{photos.length})
                </Checkbox>

                <Space>
                  <Popconfirm
                    title="确定要批量删除选中的照片吗？"
                    onConfirm={handleBatchDelete}
                    disabled={selectedPhotoIds.length === 0}
                  >
                    <Button
                      danger
                      disabled={selectedPhotoIds.length === 0}
                      icon={<DeleteOutlined />}
                    >
                      删除选中 ({selectedPhotoIds.length})
                    </Button>
                  </Popconfirm>

                  <Popconfirm
                    title="确定要取消选中照片的关联吗？"
                    onConfirm={handleBatchDisassociate}
                    disabled={selectedPhotoIds.length === 0}
                  >
                    <Button disabled={selectedPhotoIds.length === 0}>
                      取消关联 ({selectedPhotoIds.length})
                    </Button>
                  </Popconfirm>
                </Space>
              </>
            )}
          </div>
        </div>
      )}

      <div className="photo-grid">
        {photos.map((photo) => {
          const isPrimary = primaryPhotoId === photo.id;
          const isSelected = selectedPhotoIds.includes(photo.id);
          return (
            <div
              key={photo.id}
              className={`photo-item ${isPrimary ? 'primary' : ''} ${
                isSelected ? 'selected' : ''
              }`}
            >
              <div className="photo-wrapper">
                <img src={photo.url} alt={photo.name} className="photo-image" />

                {/* 批量选择复选框 */}
                {batchMode && (
                  <div className="batch-checkbox">
                    <Checkbox
                      checked={isSelected}
                      onChange={(e) =>
                        handleSelectPhoto(photo.id, e.target.checked)
                      }
                    />
                  </div>
                )}

                {isPrimary && (
                  <div className="primary-badge">
                    <StarFilled style={{ color: '#faad14' }} />
                    <span>主图</span>
                  </div>
                )}

                {!batchMode && (
                  <div className="photo-overlay">
                    <Space>
                      <Button
                        type="text"
                        icon={<EyeOutlined />}
                        onClick={() => handlePreview(photo)}
                        title="预览"
                      />
                      {editable &&
                        entityType === 'culturalElement' &&
                        !isPrimary && (
                          <Button
                            type="text"
                            icon={<StarOutlined />}
                            onClick={() => handleSetPrimary(photo.id)}
                            title="设为主图"
                          />
                        )}
                      {editable && (
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDelete(photo.id)}
                          title="删除"
                        />
                      )}
                    </Space>
                  </div>
                )}
              </div>
              {photo.name && <div className="photo-name">{photo.name}</div>}
            </div>
          );
        })}

        {/* 上传按钮 */}
        {editable && photos.length < maxCount && (
          <div className="photo-item upload-item">
            <Upload
              accept="image/*"
              showUploadList={false}
              disabled={uploading}
              beforeUpload={(file) => {
                handleUpload(file);
                return false;
              }}
              style={{ width: '100%' }}
            >
              <div className="upload-wrapper">
                {uploading ? (
                  <>
                    <Spin size="small" />
                    <div>上传中...</div>
                  </>
                ) : (
                  <>
                    <PlusOutlined />
                    <div>上传图片</div>
                  </>
                )}
              </div>
            </Upload>
          </div>
        )}
      </div>

      {photos.length === 0 && !editable && <Empty description="暂无图片" />}

      {/* 图片预览 */}
      <Image
        style={{ display: 'none' }}
        preview={{
          visible: previewVisible,
          onVisibleChange: setPreviewVisible,
          src: previewImage,
        }}
      />
    </div>
  );
};

export default PhotoGallery;
