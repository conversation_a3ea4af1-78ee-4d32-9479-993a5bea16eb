import {
  BarChartOutlined,
  NodeIndexOutlined,
  ReloadOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import { Button, Card, Col, Row, Statistic, Typography } from 'antd';
import ReactECharts from 'echarts-for-react';
import React, { useMemo } from 'react';

const { Title } = Typography;

// 辅助函数：获取源要素类型显示名称
const getSourceTypeDisplayName = (sourceType: string): string => {
  const typeMap: Record<string, string> = {
    ancient_city: '古城',
    cultural_element: '文化要素',
    mountain: '山塬',
    water_system: '水系',
    historical_element: '历史要素',
  };
  return typeMap[sourceType] || sourceType;
};

// 辅助函数：获取目标要素类型显示名称
const getTargetTypeDisplayName = (targetType: string): string => {
  const typeMap: Record<string, string> = {
    element: '具体要素',
    category: '类别',
  };
  return typeMap[targetType] || targetType;
};

export interface NetworkGraphStatisticsProps {
  data: API.ElementRelationStatistics | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const NetworkGraphStatistics: React.FC<NetworkGraphStatisticsProps> = ({
  data: statistics,
  loading = false,
  onRefresh,
}) => {
  // 生成图表配置
  const chartOptions = useMemo(() => {
    if (!statistics)
      return { pieOption: null, barOption: null, directionOption: null };

    // 饼图配置 - 按源要素类型分布
    const pieOption = {
      title: {
        text: '源要素类型分布',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      series: [
        {
          name: '源要素类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: statistics.bySourceType.map((item) => ({
            value: item.count,
            name: getSourceTypeDisplayName(item.sourceType),
          })),
        },
      ],
    };

    // 柱状图配置 - 按关系类型分布
    const barOption = {
      title: {
        text: '关系类型分布',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: statistics.byRelationType.map((item) => item.relationName),
        axisLabel: {
          rotate: 45,
          fontSize: 10,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '关系数量',
          type: 'bar',
          data: statistics.byRelationType.map((item) => item.count),
          itemStyle: {
            color: '#1890ff',
          },
        },
      ],
    };

    // 方向分布图
    const directionOption = {
      title: {
        text: '关联方向分布',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}',
      },
      xAxis: {
        type: 'category',
        data: statistics.byDirection.map((item) => item.direction),
        axisLabel: {
          fontSize: 10,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '方向数量',
          type: 'bar',
          data: statistics.byDirection.map((item) => item.count),
          itemStyle: {
            color: '#52c41a',
          },
        },
      ],
    };

    return { pieOption, barOption, directionOption };
  }, [statistics]);

  // 渲染统计卡片
  const renderStatisticCards = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总关联数"
              value={statistics.total}
              prefix={<ShareAltOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="源要素类型"
              value={statistics.bySourceType.length}
              prefix={<NodeIndexOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="目标类型"
              value={statistics.byTargetType.length}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="关系类型"
              value={statistics.byRelationType.length}
              prefix={<ShareAltOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染图表区域
  const renderCharts = () => {
    if (!statistics || !chartOptions.pieOption) return null;

    return (
      <Row gutter={16}>
        <Col span={8}>
          <Card loading={loading} style={{ height: 400 }}>
            <ReactECharts
              option={chartOptions.pieOption}
              style={{ height: '350px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card loading={loading} style={{ height: 400 }}>
            <ReactECharts
              option={chartOptions.barOption}
              style={{ height: '350px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card loading={loading} style={{ height: 400 }}>
            <ReactECharts
              option={chartOptions.directionOption}
              style={{ height: '350px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染详细统计表格
  const renderDetailedStats = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={8}>
          <Card title="源要素类型详情" size="small">
            {statistics.bySourceType.map((item, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}
              >
                <span>{getSourceTypeDisplayName(item.sourceType)}</span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
        <Col span={8}>
          <Card title="目标类型详情" size="small">
            {statistics.byTargetType.map((item, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}
              >
                <span>{getTargetTypeDisplayName(item.targetType)}</span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
        <Col span={8}>
          <Card
            title="关联方向详情"
            size="small"
            style={{ maxHeight: 300, overflow: 'auto' }}
          >
            {statistics.byDirection.map((item, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}
              >
                <span>{item.direction}</span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
      </Row>
    );
  };

  return (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <Title level={4} style={{ margin: 0 }}>
          关系统计分析
        </Title>
        {onRefresh && (
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
          >
            刷新数据
          </Button>
        )}
      </div>
      {renderStatisticCards()}
      {renderCharts()}
      {renderDetailedStats()}
    </div>
  );
};

export default NetworkGraphStatistics;
