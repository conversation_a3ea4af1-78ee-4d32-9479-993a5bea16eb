import { <PERSON><PERSON>, <PERSON>, Space } from 'antd';
import React, { useState } from 'react';
import EnhancedStatistics from './EnhancedStatistics';

// 模拟统计数据
const mockStatisticsData: API.ElementRelationStatistics = {
  total: 156,
  bySourceType: [
    { sourceType: 'ancient_city', count: 45 },
    { sourceType: 'cultural_element', count: 111 },
  ],
  byTargetType: [
    { targetType: 'element', count: 140 },
    { targetType: 'category', count: 16 },
  ],
  byRelationType: [
    { relationId: 1, relationName: '选址关联', count: 67 },
    { relationId: 2, relationName: '视线关联', count: 23 },
    { relationId: 3, relationName: '历史关联', count: 34 },
    { relationId: 4, relationName: '功能关联', count: 32 },
  ],
  byDirection: [
    { direction: '北有', count: 28 },
    { direction: '南临', count: 22 },
    { direction: '东接', count: 19 },
    { direction: '西邻', count: 15 },
    { direction: '遥望', count: 31 },
    { direction: '依托', count: 41 },
  ],
};

const StatisticsTest: React.FC = () => {
  const [data, setData] = useState<API.ElementRelationStatistics | null>(null);
  const [loading, setLoading] = useState(false);

  // 模拟加载数据
  const handleLoadData = () => {
    setLoading(true);
    setTimeout(() => {
      setData(mockStatisticsData);
      setLoading(false);
    }, 1000);
  };

  // 模拟清空数据
  const handleClearData = () => {
    setData(null);
  };

  // 模拟刷新数据
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      // 模拟数据变化
      const newData = {
        ...mockStatisticsData,
        total: mockStatisticsData.total + Math.floor(Math.random() * 10),
      };
      setData(newData);
      setLoading(false);
    }, 800);
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="统计组件测试" style={{ marginBottom: 24 }}>
        <Space>
          <Button type="primary" onClick={handleLoadData} loading={loading}>
            加载模拟数据
          </Button>
          <Button onClick={handleClearData}>清空数据</Button>
          <Button onClick={handleRefresh} disabled={!data}>
            刷新数据
          </Button>
        </Space>
        <div style={{ marginTop: 16 }}>
          <p>当前数据状态: {data ? '有数据' : '无数据'}</p>
          <p>Loading状态: {loading ? '加载中' : '空闲'}</p>
        </div>
      </Card>

      <EnhancedStatistics
        data={data}
        loading={loading}
        onRefresh={handleRefresh}
      />
    </div>
  );
};

export default StatisticsTest;
