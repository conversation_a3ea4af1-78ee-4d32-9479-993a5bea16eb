import DictSelect from '@/components/DictSelect';
import {
  deleteElementRelation,
  getElementRelationList,
} from '@/services/elementRelation';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

export interface ElementRelationTableProps {
  onEdit?: (record: API.ElementRelation) => void;
  onView?: (record: API.ElementRelation) => void;
  onCreate?: () => void;
  refreshTrigger?: number;
}

const ElementRelationTable: React.FC<ElementRelationTableProps> = ({
  onEdit,
  onView,
  onCreate,
  refreshTrigger,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<API.ElementRelation[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 要素类型选项
  const elementTypeOptions = [
    { label: '古城', value: 'ancient_city' },
    { label: '文化要素', value: 'cultural_element' },
  ];

  // 目标要素类型选项
  const targetEntityTypeOptions = [
    { label: '古城', value: 'ancient_city' },
    { label: '文化要素', value: 'cultural_element' },
    { label: '类型字典', value: 'type_dict' },
  ];

  // 状态选项
  const statusOptions = [
    { label: '启用', value: 1 },
    { label: '禁用', value: 0 },
  ];

  // 获取要素类型显示名称
  const getElementTypeName = (type: string) => {
    switch (type) {
      case 'ancient_city':
        return '古城';
      case 'element':
      case 'cultural_element':
        return '文化要素';
      case 'type_dict':
        return '要素类别';
      default:
        return type;
    }
  };

  // 获取目标要素类型显示名称
  const getTargetEntityTypeName = (type: string) => {
    switch (type) {
      case 'ancient_city':
        return '古城';
      case 'cultural_element':
        return '文化要素';
      case 'type_dict':
        return '类型字典';
      default:
        return type;
    }
  };

  // 加载数据
  const loadData = async (params?: any) => {
    setLoading(true);
    try {
      const searchParams = {
        page: current,
        pageSize,
        ...form.getFieldsValue(),
        ...params,
      };

      const response = await getElementRelationList(searchParams);
      if (response.errCode === 0 && response.data) {
        setDataSource(response.data.list || []);
        setTotal(response.data.total || 0);
        setCurrent(response.data.page || 1);
        setPageSize(response.data.pageSize || 10);
      } else {
        message.error(response.msg || '获取数据失败');
      }
    } catch (error) {
      message.error('获取数据失败');
      console.error('获取要素关联列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除记录
  const handleDelete = async (id: number) => {
    try {
      const response = await deleteElementRelation(id);
      if (response.errCode === 0) {
        message.success('删除成功');
        loadData();
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
      console.error('删除要素关联失败:', error);
    }
  };

  // 搜索
  const handleSearch = () => {
    setCurrent(1);
    loadData({ page: 1 });
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setCurrent(1);
    loadData({ page: 1 });
  };

  // 表格列定义
  const columns: ColumnsType<API.ElementRelation> = [
    {
      title: '源要素',
      key: 'source',
      width: 150,
      render: (_, record) => (
        <div>
          <Tag color="blue">{getElementTypeName(record.sourceType)}</Tag>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.sourceElement?.cityName ||
              record.sourceElement?.name ||
              record.sourceElement?.typeName ||
              '-'}
          </div>
        </div>
      ),
    },
    {
      title: '目标要素',
      key: 'target',
      width: 150,
      render: (_, record) => (
        <div>
          {record.targetEntityType && (
            <Tag color="blue">
              {getTargetEntityTypeName(record.targetEntityType)}
            </Tag>
          )}
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.targetElement?.cityName ||
              record.targetElement?.name ||
              record.targetElement?.typeName ||
              '-'}
          </div>
        </div>
      ),
    },
    {
      title: '关系类型',
      key: 'relation',
      width: 120,
      render: (_, record) => (
        <Tag color="purple">{record.relationDict?.relationName}</Tag>
      ),
    },
    {
      title: '父级关系',
      key: 'parent',
      width: 100,
      render: (_, record) =>
        record.parentRelationshipId ? (
          <Tag color="cyan">#{record.parentRelationshipId}</Tag>
        ) : (
          <span style={{ color: '#ccc' }}>-</span>
        ),
    },
    {
      title: '方向/词条',
      key: 'direction',
      width: 120,
      render: (_, record) => (
        <div>
          {record.direction && (
            <div style={{ fontSize: '12px' }}>{record.direction}</div>
          )}
          {record.term && (
            <div style={{ fontSize: '12px', color: '#666' }}>{record.term}</div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: number) => (
        <Tag color={status === 1 ? 'success' : 'default'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (text: string) =>
        text ? new Date(text).toLocaleDateString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => onView?.(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 监听刷新触发器
  useEffect(() => {
    if (refreshTrigger) {
      loadData();
    }
  }, [refreshTrigger]);

  return (
    <Card>
      {/* 搜索表单 */}
      <Form form={form} layout="inline" style={{ marginBottom: 16 }}>
        <Row gutter={16} style={{ width: '100%' }}>
          <Col span={6}>
            <Form.Item name="keyword" label="关键词">
              <Input placeholder="词条、记载" allowClear />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name="sourceType" label="源要素类型">
              <Select
                placeholder="请选择"
                allowClear
                options={elementTypeOptions}
              />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name="targetEntityType" label="目标要素类型">
              <Select
                placeholder="请选择"
                allowClear
                options={targetEntityTypeOptions}
              />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name="relationDictId" label="关系类型">
              <DictSelect.DictTreeSelect
                type="relation"
                placeholder="请选择"
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name="status" label="状态">
              <Select placeholder="请选择" allowClear options={statusOptions} />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Col>
        </Row>
      </Form>

      {/* 工具栏 */}
      <div
        style={{
          marginBottom: 16,
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={onCreate}>
            新建关联
          </Button>
        </Space>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => loadData()}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 数据表格 */}
      <Table
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        loading={loading}
        scroll={{ x: 1200 }}
        pagination={{
          current,
          pageSize,
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, size) => {
            setCurrent(page);
            setPageSize(size || 10);
            loadData({ page, pageSize: size });
          },
        }}
      />
    </Card>
  );
};

export default ElementRelationTable;
