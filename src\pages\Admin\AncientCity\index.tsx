/**
 * @file 古城管理页面
 * @description 管理古城字典数据，包括增删改查等操作
 * <AUTHOR> Assistant
 * @date 2025-10-12
 */
import PermissionWrapper from '@/components/PermissionWrapper';
import type { AncientCityState } from '@/models/ancientCity';
import {
  createAncientCityDict,
  deleteAncientCityDict,
  getAncientCityDictList,
  updateAncientCityDict,
} from '@/services/dictionary';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Option } = Select;

interface AncientCityManagementProps {
  ancientCity: AncientCityState;
  dispatch: any;
}

const AncientCityManagement: React.FC<AncientCityManagementProps> = ({
  ancientCity,
  dispatch,
}) => {
  // 从dva store获取古城列表用于下拉选择
  const cities = ancientCity?.ancientCityList || [];
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.AncientCityDict[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] =
    useState<API.AncientCityDict | null>(null);

  // 加载数据
  const fetchData = async (
    page = 1,
    pageSize = 20,
    keyword?: string,
    region?: string,
    parentId?: number,
  ) => {
    setLoading(true);
    try {
      const params: API.GetAncientCityDictListParams = {
        page,
        pageSize,
        keyword,
        region,
        parentId,
      };
      const response = await getAncientCityDictList(params);
      if (response.errCode === 0 && response.data) {
        setData(response.data.list);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.msg || '加载数据失败');
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchData();
    // 加载古城字典到全局状态
    dispatch({ type: 'ancientCity/fetchAncientCityList' });
  }, [dispatch]);

  // 搜索处理
  const handleSearch = async (values: any) => {
    await fetchData(
      1,
      pagination.pageSize,
      values.keyword,
      values.region,
      values.parentId,
    );
  };

  // 重置搜索
  const handleReset = async () => {
    form.resetFields();
    await fetchData(1, pagination.pageSize);
  };

  // 新增古城
  const handleAdd = () => {
    setEditingRecord(null);
    setEditModalVisible(true);
    editForm.resetFields();
  };

  // 编辑古城
  const handleEdit = (record: API.AncientCityDict) => {
    setEditingRecord(record);
    setEditModalVisible(true);
    editForm.setFieldsValue(record);
  };

  // 删除古城
  const handleDelete = async (id: number) => {
    try {
      const response = await deleteAncientCityDict(id);
      if (response.errCode === 0) {
        message.success('删除成功');
        await fetchData(pagination.current, pagination.pageSize);
        // 刷新全局缓存
        dispatch({ type: 'ancientCity/fetchAncientCityList' });
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 保存古城
  const handleSave = async (values: any) => {
    try {
      let response;
      if (editingRecord) {
        response = await updateAncientCityDict(editingRecord.id, values);
      } else {
        response = await createAncientCityDict(values);
      }

      if (response.errCode === 0) {
        message.success(editingRecord ? '更新成功' : '创建成功');
        setEditModalVisible(false);
        await fetchData(pagination.current, pagination.pageSize);
        // 刷新全局缓存
        dispatch({ type: 'ancientCity/fetchAncientCityList' });
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<API.AncientCityDict> = [
    {
      title: '古城名称',
      dataIndex: 'cityName',
      key: 'cityName',
      width: 150,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '古城编码',
      dataIndex: 'cityCode',
      key: 'cityCode',
      width: 140,
    },
    {
      title: '所属区域',
      dataIndex: 'region',
      key: 'region',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '父级古城',
      dataIndex: 'parentId',
      key: 'parentId',
      width: 120,
      render: (parentId: number) => {
        if (!parentId) return '-';
        const parentCity = data.find((city) => city.id === parentId);
        return parentCity ? parentCity.cityName : `ID: ${parentId}`;
      },
    },
    {
      title: '建立年份',
      dataIndex: 'establishedYear',
      key: 'establishedYear',
      width: 100,
      render: (year: number) => {
        if (!year) return '-';
        return year < 0 ? `公元前${Math.abs(year)}年` : `公元${year}年`;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: number) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      width: 80,
    },
    {
      title: '描述',
      dataIndex: 'cityDesc',
      key: 'cityDesc',
      width: 200,
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <PermissionWrapper permission="canEdit">
            <Tooltip title="编辑">
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          </PermissionWrapper>
          <PermissionWrapper permission="canDelete">
            <Popconfirm
              title="确定要删除这个古城吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button type="link" danger icon={<DeleteOutlined />} />
              </Tooltip>
            </Popconfirm>
          </PermissionWrapper>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      title="古城管理"
      content="管理古城字典数据，包括古城名称、编码、区域等信息"
      className="ancient-city-management"
    >
      {/* 搜索表单 */}
      <Card className="search-card" style={{ marginBottom: 16 }}>
        <Form form={form} onFinish={handleSearch} layout="vertical">
          <Row gutter={16}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="关键词" name="keyword">
                <Input placeholder="请输入古城名称或编码" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="区域" name="region">
                <Input placeholder="请输入区域名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="父级古城" name="parentId">
                <Select placeholder="请选择父级古城" allowClear>
                  {cities.map((city) => (
                    <Option key={city.id} value={city.id}>
                      {city.cityName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label=" " style={{ marginBottom: 0 }}>
                <Space>
                  <Button type="primary" htmlType="submit">
                    搜索
                  </Button>
                  <Button onClick={handleReset}>重置</Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作工具栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <PermissionWrapper permission="canEdit">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                >
                  新增古城
                </Button>
              </PermissionWrapper>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={data}
          loading={loading}
          rowKey="id"
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => fetchData(page, pageSize),
            onShowSizeChange: (current, size) => fetchData(current, size),
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title={editingRecord ? '编辑古城' : '新增古城'}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => editForm.submit()}
        width={800}
      >
        <Form
          form={editForm}
          onFinish={handleSave}
          layout="vertical"
          initialValues={{ status: 1, sort: 0 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="古城名称"
                name="cityName"
                rules={[{ required: true, message: '请输入古城名称' }]}
              >
                <Input placeholder="请输入古城名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="古城编码"
                name="cityCode"
                rules={[{ required: true, message: '请输入古城编码' }]}
              >
                <Input placeholder="请输入古城编码，如：CITY_CHANGAN" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="所属区域" name="region">
                <Input placeholder="请输入所属区域，如：西安市" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="父级古城" name="parentId">
                <Select placeholder="请选择父级古城" allowClear>
                  {cities.map((city) => (
                    <Option key={city.id} value={city.id}>
                      {city.cityName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="状态" name="status">
                <Select>
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="排序" name="sort">
                <Input type="number" placeholder="请输入排序值" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="建立年份" name="establishedYear">
                <Input
                  type="number"
                  placeholder="建立年份，负数表示公元前"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Typography.Paragraph>
            经纬度信息可以通过
            <Typography.Link
              href="https://www.bchrt.com/tools/latitude-longitude-query/"
              target="_blank"
              rel="noopener noreferrer"
            >
              在线工具
            </Typography.Link>
            获取。
          </Typography.Paragraph>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="经度"
                name="longitude"
                rules={[
                  {
                    type: 'number',
                    min: -180,
                    max: 180,
                    message: '经度范围为-180到180',
                    transform: (value) => Number(value),
                  },
                  {
                    required: true,
                    message: '请输入经度',
                  },
                ]}
              >
                <Input
                  type="number"
                  placeholder="经度，范围-180到180"
                  step="0.000001"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="纬度"
                name="latitude"
                rules={[
                  {
                    type: 'number',
                    min: -90,
                    max: 90,
                    message: '纬度范围为-90到90',
                    transform: (value) => Number(value),
                  },
                  {
                    required: true,
                    message: '请输入纬度',
                  },
                ]}
              >
                <Input
                  type="number"
                  placeholder="纬度，范围-90到90"
                  step="0.000001"
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="地理位置描述" name="locationDesc">
            <Input.TextArea rows={2} placeholder="请输入地理位置描述" />
          </Form.Item>
          <Form.Item label="古城描述" name="cityDesc">
            <Input.TextArea rows={3} placeholder="请输入古城描述" />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default connect(
  ({ ancientCity }: { ancientCity: AncientCityState }) => ({
    ancientCity,
  }),
)(AncientCityManagement);
