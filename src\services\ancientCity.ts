/**
 * @file 古城相关API服务
 * @description 提供古城列表、详情、地图数据等API接口
 * <AUTHOR> Assistant
 * @date 2025-10-15
 */

import { request } from '@umijs/max';

// ==================== 公开接口（门户使用） ====================

/**
 * 获取古城列表（公开接口）
 */
export async function getPublicAncientCityList(
  params?: API.GetAncientCityDictListParams,
): Promise<API.ResType<API.AncientCityDictListResponse>> {
  return request('/openapi/ancient-city/list', {
    method: 'GET',
    params,
  });
}

/**
 * 获取古城详情（公开接口）
 */
export async function getPublicAncientCityDetail(
  id: number,
): Promise<API.ResType<API.AncientCityDict>> {
  return request(`/openapi/ancient-city/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取古城地图数据（公开接口）
 */
export async function getPublicAncientCityMapData(params?: {
  region?: string;
}): Promise<API.ResType<API.MapMarkerData[]>> {
  return request('/openapi/map/ancient-cities', {
    method: 'GET',
    params,
  });
}

/**
 * 获取古城统计数据（公开接口）
 */
export async function getPublicAncientCityStatistics(): Promise<
  API.ResType<API.AncientCityDictStatistics>
> {
  return request('/openapi/ancient-city/statistics', {
    method: 'GET',
  });
}

// ==================== 管理端接口 ====================

/**
 * 获取古城列表（管理端）
 */
export async function getAncientCityList(
  params?: API.GetAncientCityDictListParams,
): Promise<API.ResType<API.AncientCityDictListResponse>> {
  return request('/admin/ancient-city-dict/', {
    method: 'GET',
    params,
  });
}

/**
 * 获取古城详情（管理端）
 */
export async function getAncientCityDetail(
  id: number,
): Promise<API.ResType<API.AncientCityDict>> {
  return request(`/admin/ancient-city-dict/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建古城
 */
export async function createAncientCity(
  params: API.CreateAncientCityDictParams,
): Promise<API.ResType<API.AncientCityDict>> {
  return request('/admin/ancient-city-dict', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新古城
 */
export async function updateAncientCity(
  id: number,
  params: API.UpdateAncientCityDictParams,
): Promise<API.ResType<API.AncientCityDict>> {
  return request(`/admin/ancient-city-dict/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除古城
 */
export async function deleteAncientCity(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/ancient-city-dict/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取古城树形结构
 */
export async function getAncientCityTree(): Promise<
  API.ResType<API.AncientCityDict[]>
> {
  return request('/openapi/ancient-city-dict/tree', {
    method: 'GET',
  });
}

/**
 * 获取所有古城（用于选择器）
 */
export async function getAllAncientCities(): Promise<
  API.ResType<API.AncientCityDict[]>
> {
  return request('/openapi/ancient-city-dict/all', {
    method: 'GET',
  });
}

// ==================== 门户专用接口 ====================

/**
 * 门户古城概览数据
 */
export interface PortalAncientCityOverview {
  total: number;
  byRegion: Array<{
    region: string;
    count: number;
    cities: Array<{
      id: number;
      name: string;
      longitude?: number;
      latitude?: number;
    }>;
  }>;
  featured: Array<{
    id: number;
    name: string;
    region: string;
    description?: string;
    longitude?: number;
    latitude?: number;
    culturalElementCount?: number;
  }>;
}

/**
 * 获取门户古城概览数据
 */
export async function getPortalAncientCityOverview(): Promise<
  API.ResType<PortalAncientCityOverview>
> {
  return request('/openapi/portal/ancient-city-overview', {
    method: 'GET',
  });
}

/**
 * 门户古城地图标记点
 */
export interface PortalAncientCityMarker {
  id: number;
  name: string;
  longitude: number;
  latitude: number;
  region: string;
  culturalElementCount: number;
  description?: string;
  thumbnailUrl?: string;
}

/**
 * 获取门户古城地图标记点
 */
export async function getPortalAncientCityMarkers(params?: {
  region?: string;
}): Promise<API.ResType<PortalAncientCityMarker[]>> {
  return request('/openapi/portal/ancient-city-markers', {
    method: 'GET',
    params,
  });
}
