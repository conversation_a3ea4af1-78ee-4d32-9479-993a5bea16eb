import DictSelect from '@/components/DictSelect';
import HierarchicalElementSelector from '@/components/HierarchicalElementSelector';
import TypeTreeSelect from '@/components/TypeTreeSelect';
import {
  ArrowRightOutlined,
  CompassOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  NodeIndexOutlined,
  ShareAltOutlined,
  TagOutlined,
} from '@ant-design/icons';
import {
  AutoComplete,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  Tooltip,
} from 'antd';

import React, { useEffect, useState } from 'react';
import styles from './ElementRelationForm.module.less';

const { TextArea } = Input;

export interface ElementRelationFormProps {
  initialValues?: Partial<API.ElementRelation>;
  onValuesChange?: (changedValues: any, allValues: any) => void;
  onFormRef?: (form: any) => void;
  readonly?: boolean;
}

const ElementRelationForm: React.FC<ElementRelationFormProps> = ({
  initialValues,
  onValuesChange,
  onFormRef,
  readonly = false,
}) => {
  const [form] = Form.useForm();
  const [sourceType, setSourceType] = useState<string>('');
  const [targetEntityType, setTargetEntityType] = useState<string>('');

  // 要素类型选项 - 更新为新的统一结构
  const elementTypeOptions = [
    { label: '🏛️ 古城', value: 'ancient_city' },
    { label: '🎭 文化要素', value: 'cultural_element' },
  ];

  // 目标要素类型选项 - 更新为新的统一结构
  const targetEntityTypeOptions = [
    { label: '🏛️ 古城', value: 'ancient_city' },
    { label: '🎭 文化要素', value: 'cultural_element' },
    { label: '📚 类型字典', value: 'type_dict' },
  ];

  // 常见方向选项
  const directionOptions = [
    { label: '前有', value: '前有' },
    { label: '后有', value: '后有' },
    { label: '上有', value: '上有' },
    { label: '下有', value: '下有' },
    { label: '左有', value: '左有' },
    { label: '右有', value: '右有' },
    { label: '东连', value: '东连' },
    { label: '西连', value: '西连' },
    { label: '南连', value: '南连' },
    { label: '北连', value: '北连' },
    { label: '相连', value: '相连' },
    { label: '绕其后', value: '绕其后' },
    { label: '绕其前', value: '绕其前' },
    { label: '面向', value: '面向' },
    { label: '背向', value: '背向' },
  ];

  // 加载源要素数据
  // 注意：源要素数据加载现在由 HierarchicalElementSelector 组件内部处理

  // 注意：目标要素数据加载现在由 HierarchicalElementSelector 组件内部处理

  // 监听源要素类型变化
  const handleSourceTypeChange = (value: string) => {
    setSourceType(value);
    form.setFieldsValue({ sourceId: undefined });
    // 注意：数据加载现在由 HierarchicalElementSelector 组件内部处理
  };

  // 监听目标要素类型变化
  const handleTargetEntityTypeChange = (value: string) => {
    setTargetEntityType(value);
    form.setFieldsValue({ targetId: undefined });
    // 注意：数据加载现在由 HierarchicalElementSelector 组件内部处理
  };

  // 初始化表单
  useEffect(() => {
    if (onFormRef) {
      onFormRef(form);
    }
  }, [form, onFormRef]);

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
      if (initialValues.sourceType) {
        setSourceType(initialValues.sourceType);
        // 注意：数据加载现在由 HierarchicalElementSelector 组件内部处理
      }
      if (initialValues.targetEntityType) {
        setTargetEntityType(initialValues.targetEntityType);
        if (
          ['ancient_city', 'cultural_element'].includes(
            initialValues.targetEntityType,
          )
        ) {
          // 注意：数据加载现在由 HierarchicalElementSelector 组件内部处理
        }
      }
    }
  }, [initialValues, form]);

  // 渲染目标要素选择器
  const renderTargetElementSelect = () => {
    // 根据目标要素类型显示对应的选择器
    if (targetEntityType === 'type_dict') {
      return (
        <TypeTreeSelect
          placeholder="请选择类型字典"
          allowClear
          disabled={readonly}
        />
      );
    }

    if (
      targetEntityType === 'ancient_city' ||
      targetEntityType === 'cultural_element'
    ) {
      return (
        <HierarchicalElementSelector
          placeholder="请选择目标要素"
          allowClear
          entityType={targetEntityType as 'ancient_city' | 'cultural_element'}
          disabled={!targetEntityType || readonly}
        />
      );
    }

    return null;
  };

  return (
    <div className={styles.formContainer}>
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={onValuesChange}
      >
        {/* 源要素信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <NodeIndexOutlined />
              源要素信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="sourceType"
                label={
                  <Space className={styles.fieldLabel}>
                    <TagOutlined />
                    要素类型
                  </Space>
                }
                rules={[{ required: true, message: '请选择源要素类型' }]}
                className={styles.formItem}
              >
                <Select
                  placeholder="请选择源要素类型"
                  options={elementTypeOptions}
                  onChange={handleSourceTypeChange}
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sourceId"
                label={
                  <Space className={styles.fieldLabel}>
                    <InfoCircleOutlined />
                    具体要素
                  </Space>
                }
                rules={[{ required: true, message: '请选择源要素' }]}
                className={styles.formItem}
              >
                <HierarchicalElementSelector
                  placeholder="请选择源要素"
                  allowClear
                  entityType={sourceType as 'ancient_city' | 'cultural_element'}
                  disabled={!sourceType || readonly}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 关联关系流向 */}
        <div className={styles.relationFlow}>
          <span>源要素</span>
          <ArrowRightOutlined className={styles.flowArrow} />
          <span>目标要素</span>
        </div>

        {/* 目标要素信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <ShareAltOutlined />
              目标要素信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="targetEntityType"
                label={
                  <Space className={styles.fieldLabel}>
                    <InfoCircleOutlined />
                    目标要素类型
                  </Space>
                }
                rules={[{ required: true, message: '请选择目标要素类型' }]}
                className={styles.formItem}
              >
                <Select
                  placeholder="请选择目标要素类型"
                  options={targetEntityTypeOptions}
                  onChange={handleTargetEntityTypeChange}
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="targetId"
                label={
                  <Space className={styles.fieldLabel}>
                    <LinkOutlined />
                    具体要素
                  </Space>
                }
                rules={[
                  {
                    required: true,
                    message:
                      targetEntityType === 'type_dict'
                        ? '请选择类型字典'
                        : '请选择目标要素',
                  },
                ]}
                className={styles.formItem}
              >
                {renderTargetElementSelect()}
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 关系信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <LinkOutlined />
              关系信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="relationDictId"
                label={
                  <Space className={styles.fieldLabel}>
                    <ShareAltOutlined />
                    关系类型
                    <Tooltip title="选择要素之间的关系类型">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                className={styles.formItem}
              >
                <DictSelect.DictTreeSelect
                  type="relation"
                  placeholder="请选择关系类型"
                  allowClear
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            {/* TODO: 父级关系待定
            <Col span={12}>
              <Form.Item
                name="parentRelationshipId"
                label={
                  <Space className={styles.fieldLabel}>
                    <NodeIndexOutlined />
                    父级关系
                    <Tooltip title="选择父级关系，用于建立层级关系">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                className={styles.formItem}
              >
                <InputNumber
                  placeholder="请输入父级关系ID"
                  min={1}
                  style={{ width: '100%' }}
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            */}
            <Col span={12}>
              <Form.Item
                name="direction"
                label={
                  <Space className={styles.fieldLabel}>
                    <CompassOutlined />
                    关联方向
                    <Tooltip title="描述要素之间的空间或逻辑方向关系，支持选择或手动输入">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                className={styles.formItem}
              >
                <AutoComplete
                  placeholder="请选择或输入关联方向"
                  allowClear
                  options={directionOptions}
                  disabled={readonly}
                  filterOption={(inputValue, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(inputValue.toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 详细信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <FileTextOutlined />
              详细信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Form.Item
            name="term"
            label={
              <Space className={styles.fieldLabel}>
                <TagOutlined />
                词条描述
                <Tooltip title="简短的词条或标签描述">
                  <InfoCircleOutlined className={styles.tooltipIcon} />
                </Tooltip>
              </Space>
            }
            className={styles.formItem}
          >
            <Input
              placeholder="请输入词条描述"
              maxLength={255}
              disabled={readonly}
            />
          </Form.Item>

          <Form.Item
            name="record"
            label={
              <Space className={styles.fieldLabel}>
                <FileTextOutlined />
                记载内容
                <Tooltip title="详细的文字记载或描述">
                  <InfoCircleOutlined className={styles.tooltipIcon} />
                </Tooltip>
              </Space>
            }
            className={styles.formItem}
          >
            <TextArea
              placeholder="请输入记载内容"
              rows={4}
              maxLength={1000}
              showCount
              disabled={readonly}
              className={styles.textArea}
            />
          </Form.Item>

          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="sort"
                label={
                  <Space className={styles.fieldLabel}>
                    <InfoCircleOutlined />
                    排序号
                    <Tooltip title="用于控制显示顺序，数字越小越靠前">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                className={styles.formItem}
              >
                <InputNumber
                  placeholder="请输入排序号"
                  min={0}
                  style={{ width: '100%' }}
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label={
                  <Space className={styles.fieldLabel}>
                    <InfoCircleOutlined />
                    状态
                  </Space>
                }
                rules={[{ required: true, message: '请选择状态' }]}
                className={styles.formItem}
              >
                <Select
                  placeholder="请选择状态"
                  disabled={readonly}
                  options={[
                    { label: '启用', value: 1 },
                    { label: '禁用', value: 0 },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default ElementRelationForm;
