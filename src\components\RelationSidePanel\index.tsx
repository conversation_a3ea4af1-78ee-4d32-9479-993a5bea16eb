import ElementNetworkGraph from '@/components/ElementNetworkGraph';
import { getPublicElementRelationsByElement } from '@/services/elementRelation';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Card, Collapse, Empty, Spin, Tag, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

export interface RelationSidePanelProps {
  visible: boolean;
  elementData: {
    id: number;
    name: string;
    type: string;
  } | null;
  onClose: () => void;
}

interface RelationGroup {
  relationName: string;
  relationCode: string;
  relations: API.ElementRelation[];
}

const RelationSidePanel: React.FC<RelationSidePanelProps> = ({
  visible,
  elementData,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [relations, setRelations] = useState<API.ElementRelation[]>([]);

  // 获取类型名称
  const getTypeName = (type?: string) => {
    switch (type) {
      case 'mountain':
      case 'water_system':
      case 'waterSystem':
      case 'historical_element':
      case 'historicalElement':
      case 'cultural_element':
      case 'culturalElement':
        return '文化要素';
      case 'ancient_city':
      case 'ancientCity':
        return '古城';
      default:
        return '文化要素';
    }
  };

  // 获取类型颜色
  const getTypeColor = (type?: string) => {
    switch (type) {
      case 'mountain':
      case 'water_system':
      case 'waterSystem':
      case 'historical_element':
      case 'historicalElement':
      case 'cultural_element':
      case 'culturalElement':
        return '#1890ff';
      case 'ancient_city':
      case 'ancientCity':
        return '#722ed1';
      default:
        return '#1890ff';
    }
  };

  // 获取关联信息
  const fetchRelations = async () => {
    if (!elementData) return;

    console.log('🔍 开始获取关联信息:', elementData);
    setLoading(true);
    try {
      // 转换类型名称
      const elementType =
        elementData.type === 'waterSystem'
          ? 'water_system'
          : elementData.type === 'historicalElement'
          ? 'historical_element'
          : elementData.type;

      console.log('📡 请求参数:', { elementType, elementId: elementData.id });

      const response = await getPublicElementRelationsByElement(
        elementType,
        elementData.id,
      );

      console.log('📥 API响应:', response);

      if (response.errCode === 0 && response.data) {
        console.log('✅ 关联数据获取成功:', response.data);
        setRelations(response.data);
      } else {
        console.error('❌ 获取关联信息失败:', response);
        setRelations([]);
      }
    } catch (error) {
      console.error('💥 获取关联信息异常:', error);
      setRelations([]);
    } finally {
      setLoading(false);
    }
  };

  // 按关系类型分组关联信息
  const groupRelationsByType = (relations: API.ElementRelation[]) => {
    const groupMap = new Map<string, RelationGroup>();

    relations.forEach((relation) => {
      const key = relation.relationDict?.relationCode || 'unknown';
      if (!groupMap.has(key)) {
        groupMap.set(key, {
          relationName: relation.relationDict?.relationName || '未知关系',
          relationCode: key,
          relations: [],
        });
      }
      groupMap.get(key)!.relations.push(relation);
    });

    return Array.from(groupMap.values());
  };

  useEffect(() => {
    if (visible && elementData) {
      fetchRelations();
    } else {
      setRelations([]);
    }
  }, [visible, elementData]);

  if (!visible) return null;

  const relationGroups = groupRelationsByType(relations);

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        right: visible ? 0 : '-600px',
        width: '600px',
        height: '100vh',
        background: 'white',
        boxShadow: '-2px 0 8px rgba(0,0,0,0.15)',
        zIndex: 1000,
        transition: 'right 0.3s ease-in-out',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* 头部 */}
      <div
        style={{
          padding: '16px 20px',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <div>
          <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
            {elementData?.name} - 关联详情
          </h3>
          <div style={{ marginTop: '4px' }}>
            <Tag color={getTypeColor(elementData?.type)}>
              {getTypeName(elementData?.type)}
            </Tag>
          </div>
        </div>
        <Button
          type="text"
          icon={<CloseOutlined />}
          onClick={onClose}
          style={{ padding: '4px' }}
        />
      </div>

      {/* 内容区域 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '16px 20px' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>加载关联信息中...</div>
          </div>
        ) : (
          <>
            {/* 关联信息列表 */}
            <Card
              title={`关联信息 (${relations.length})`}
              size="small"
              style={{ marginBottom: 16 }}
            >
              {relations.length === 0 ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无关联信息"
                />
              ) : (
                <Collapse ghost>
                  {relationGroups.map((group, index) => (
                    <Panel
                      header={
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8,
                          }}
                        >
                          <Tag color="blue">{group.relationName}</Tag>
                          <Text type="secondary">
                            ({group.relations.length} 个关联)
                          </Text>
                        </div>
                      }
                      key={index}
                    >
                      {group.relations.map((relation, relIndex) => {
                        // 确定显示的目标要素
                        const isSource = relation.sourceId === elementData?.id;
                        const targetElement = isSource
                          ? relation.targetElement
                          : relation.sourceElement;
                        const targetType = isSource
                          ? relation.targetEntityType
                          : relation.sourceType;

                        return (
                          <div
                            key={relIndex}
                            style={{
                              padding: '12px',
                              border: '1px solid #f0f0f0',
                              borderRadius: '6px',
                              marginBottom: '8px',
                              backgroundColor: '#fafafa',
                            }}
                          >
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 8,
                                marginBottom: 8,
                              }}
                            >
                              <Tag color={getTypeColor(targetType)}>
                                {getTypeName(targetType)}
                              </Tag>
                              <Text strong>
                                {targetElement?.name || '未知要素'}
                              </Text>
                              {relation.direction && (
                                <Tag color="orange">{relation.direction}</Tag>
                              )}
                            </div>

                            {relation.term && (
                              <div style={{ marginBottom: 4 }}>
                                <Text type="secondary">词条：</Text>
                                <Text>{relation.term}</Text>
                              </div>
                            )}

                            {relation.record && (
                              <div>
                                <Text type="secondary">记载：</Text>
                                <Paragraph
                                  style={{ margin: 0 }}
                                  ellipsis={{
                                    rows: 3,
                                    expandable: true,
                                    symbol: '展开',
                                  }}
                                >
                                  {relation.record}
                                </Paragraph>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </Panel>
                  ))}
                </Collapse>
              )}
            </Card>

            {/* 关系网络图 */}
            {relations.length > 0 && elementData && (
              <ElementNetworkGraph
                relations={relations}
                currentElement={{
                  id: elementData.id,
                  name: elementData.name,
                  type: elementData.type,
                }}
                loading={loading}
                height={400}
                title="关系网络图"
                onNodeClick={(nodeData) => {
                  console.log('节点点击:', nodeData);
                  // 可以在这里实现节点点击跳转到对应要素详情
                }}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default RelationSidePanel;
