/**
 * @file 类型选择器组件
 * @description 简化的类型选择组件，用于门户页面的类型筛选
 * <AUTHOR> Assistant
 * @date 2025-10-15
 */
import { getAllTypeDict } from '@/services/dictionary';
import { Select } from 'antd';
import React, { useEffect, useState } from 'react';

const { Option } = Select;

export interface TypeSelectorProps {
  /** 当前选中的类型ID */
  value?: number;
  /** 选择变化回调 */
  onChange?: (value?: number) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否允许清空 */
  allowClear?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
}

const TypeSelector: React.FC<TypeSelectorProps> = ({
  value,
  onChange,
  placeholder = '请选择类型',
  allowClear = true,
  disabled = false,
  style,
  className,
}) => {
  const [types, setTypes] = useState<API.TypeDict[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取类型字典数据
  const fetchTypes = async () => {
    setLoading(true);
    try {
      const response = await getAllTypeDict();
      if (response.errCode === 0 && response.data) {
        // 只显示启用的类型
        const enabledTypes = response.data.filter((type) => type.status === 1);
        setTypes(enabledTypes);
      }
    } catch (error) {
      console.error('获取类型字典失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTypes();
  }, []);

  const handleChange = (val: number | undefined) => {
    onChange?.(val);
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      loading={loading}
      style={style}
      className={className}
      showSearch
      filterOption={(input, option) =>
        (option?.children as string)
          ?.toLowerCase()
          .includes(input.toLowerCase())
      }
    >
      {types.map((type) => (
        <Option key={type.id} value={type.id}>
          {type.typeName}
        </Option>
      ))}
    </Select>
  );
};

export default TypeSelector;
