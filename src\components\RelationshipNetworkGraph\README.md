# RelationshipNetworkGraph 关系网络图组件

## 概述

`RelationshipNetworkGraph` 是一个基于 ECharts 的关系网络图可视化组件，支持在管理端和门户端复用。组件提供了丰富的配置选项和交互功能，可以展示要素之间的关联关系。

## 特性

- 🎯 **双模式支持**: 支持管理端（需认证）和门户端（公开访问）
- 🎨 **高度可定制**: 支持自定义样式、筛选器、标题等
- 🔄 **动态数据**: 支持实时数据更新和筛选
- 📱 **响应式设计**: 自适应不同屏幕尺寸
- 🖱️ **交互功能**: 支持拖拽、缩放、节点高亮等
- 🔧 **灵活配置**: 支持自定义数据获取函数

## 安装依赖

```bash
npm install echarts antd
```

## 基础用法

### 管理端使用（需认证）

```tsx
import RelationshipNetworkGraph from '@/components/RelationshipNetworkGraph';

const AdminPage = () => {
  return (
    <RelationshipNetworkGraph
      title="管理端关系网络图"
      height={600}
      showFilters={true}
      isPublic={false}
    />
  );
};
```

### 门户端使用（公开访问）

```tsx
import RelationshipNetworkGraph from '@/components/RelationshipNetworkGraph';

const PublicPage = () => {
  return (
    <RelationshipNetworkGraph
      title="公开关系网络图"
      height={500}
      showFilters={true}
      isPublic={true}
    />
  );
};
```

### 自定义数据获取

```tsx
import RelationshipNetworkGraph from '@/components/RelationshipNetworkGraph';
import type {
  NetworkGraphFilters,
  NetworkGraphData,
} from '@/components/RelationshipNetworkGraph/types';

const CustomPage = () => {
  const customDataFetch = async (
    filters: NetworkGraphFilters,
  ): Promise<NetworkGraphData | null> => {
    try {
      // 自定义数据获取逻辑
      const response = await fetch('/api/custom-network-data', {
        method: 'POST',
        body: JSON.stringify(filters),
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('获取数据失败:', error);
      return null;
    }
  };

  return (
    <RelationshipNetworkGraph
      title="自定义数据源网络图"
      onDataFetch={customDataFetch}
      initialFilters={{ status: 1 }}
    />
  );
};
```

### 无卡片包装模式

```tsx
import RelationshipNetworkGraph from '@/components/RelationshipNetworkGraph';

const EmbeddedPage = () => {
  return (
    <div style={{ padding: 20 }}>
      <h2>嵌入式网络图</h2>
      <RelationshipNetworkGraph
        showCard={false}
        showFilters={false}
        height={400}
        isPublic={true}
      />
    </div>
  );
};
```

## API 参数

| 参数           | 类型     | 默认值           | 说明                     |
| -------------- | -------- | ---------------- | ------------------------ |
| height         | number   | 600              | 图表高度（像素）         |
| title          | string   | '要素关联网络图' | 图表标题                 |
| showFilters    | boolean  | true             | 是否显示筛选器           |
| showCard       | boolean  | true             | 是否使用 Card 包装       |
| isPublic       | boolean  | false            | 是否为公开模式（门户端） |
| onDataFetch    | function | -                | 自定义数据获取函数       |
| initialFilters | object   | {}               | 初始筛选条件             |
| className      | string   | -                | 自定义 CSS 类名          |
| style          | object   | -                | 自定义样式               |

## 筛选器参数

| 参数 | 类型 | 说明 |
| --- | --- | --- |
| sourceEntityType | 'ancient_city' \| 'cultural_element' | 源要素类型 |
| targetEntityType | 'ancient_city' \| 'cultural_element' | 目标要素类型 |
| relationDictId | number | 关系类型 ID |
| status | number | 状态（1=启用，0=禁用） |

## 数据格式

### NetworkGraphData

```typescript
interface NetworkGraphData {
  nodes: NetworkNode[];
  links: NetworkLink[];
  categories: NetworkCategory[];
}

interface NetworkNode {
  id: string;
  name: string;
  category: number;
  symbolSize: number;
  itemStyle: { color: string };
  label: { show: boolean };
}

interface NetworkLink {
  source: string;
  target: string;
  name: string;
  lineStyle: { width: number; curveness: number };
  label: { show: boolean; formatter: string };
  value: number;
}

interface NetworkCategory {
  name: string;
  itemStyle: { color: string };
}
```

## 高级用法

### 自定义样式

```tsx
<RelationshipNetworkGraph
  className="custom-network-graph"
  style={{
    border: '1px solid #d9d9d9',
    borderRadius: '6px',
    backgroundColor: '#fafafa',
  }}
  height={500}
/>
```

### 预设筛选条件

```tsx
<RelationshipNetworkGraph
  initialFilters={{
    sourceEntityType: 'ancient_city',
    status: 1,
  }}
  isPublic={true}
/>
```

### 事件处理

```tsx
const handleDataFetch = async (filters: NetworkGraphFilters) => {
  console.log('筛选条件变化:', filters);
  // 自定义数据获取逻辑
  return await fetchNetworkData(filters);
};

<RelationshipNetworkGraph onDataFetch={handleDataFetch} />;
```

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```css
.custom-network-graph {
  --network-card-background: #f5f5f5;
  --network-border-color: #d9d9d9;
  --network-title-color: #262626;
}

.custom-network-graph .ant-card {
  background: var(--network-card-background);
  border-color: var(--network-border-color);
}

.custom-network-graph .ant-card-head-title {
  color: var(--network-title-color);
}
```

## 注意事项

1. **权限控制**: 管理端模式需要用户已登录并有相应权限
2. **数据量**: 大量节点时建议使用筛选器减少数据量
3. **性能**: 超过 1000 个节点时可能影响渲染性能
4. **浏览器兼容**: 需要支持 ES6+的现代浏览器

## 故障排除

### 图表不显示

- 检查容器高度是否设置
- 确认数据格式是否正确
- 查看控制台是否有错误信息

### 数据加载失败

- 检查网络连接
- 确认 API 接口是否正常
- 验证用户权限（管理端模式）

### 性能问题

- 减少节点数量
- 使用筛选器
- 考虑数据分页

## 更新日志

- **v1.0.0**: 初始版本，支持基础网络图功能
- **v1.1.0**: 新增公开模式支持
- **v1.2.0**: 新增自定义数据获取功能
- **v1.3.0**: 优化性能和用户体验
