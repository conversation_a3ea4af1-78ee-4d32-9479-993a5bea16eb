import {
  executeRelationshipImport,
  getRelationshipImportTemplate,
  previewRelationshipImport,
} from '@/services/elementRelation';
import {
  CloudDownloadOutlined,
  CloudUploadOutlined,
  EyeOutlined,
  FileExcelOutlined,
  ImportOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Button,
  Card,
  Col,
  Divider,
  message,
  Progress,
  Row,
  Space,
  Steps,
  Table,
  Typography,
  Upload,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';
import React, { useState } from 'react';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

export interface RelationshipImportProps {
  onImportSuccess?: () => void;
}

const RelationshipImport: React.FC<RelationshipImportProps> = ({
  onImportSuccess,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewData, setPreviewData] =
    useState<API.RelationshipImportPreviewResponse | null>(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [importResult, setImportResult] =
    useState<API.RelationshipImportExecuteResponse | null>(null);

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      const response = await getRelationshipImportTemplate();
      if (response.errCode === 0 && response.data) {
        const { downloadUrl, filename, buffer } = response.data;

        if (buffer) {
          // 如果有base64数据，直接下载
          const byteCharacters = atob(buffer);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });

          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(link.href);
        } else if (downloadUrl) {
          // 使用下载链接
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }

        message.success('模板下载成功');
      } else {
        message.error(response.msg || '模板下载失败');
      }
    } catch (error) {
      message.error('模板下载失败');
      console.error('下载模板失败:', error);
    }
  };

  // 文件上传前的验证
  const beforeUpload = (file: File) => {
    const isExcel =
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
      message.error('只能上传Excel文件！');
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB！');
      return false;
    }

    return false; // 阻止自动上传
  };

  // 文件选择变化
  const handleFileChange = (info: any) => {
    setFileList(info.fileList.slice(-1)); // 只保留最后一个文件
    if (info.fileList.length > 0) {
      setCurrentStep(1);
      setPreviewData(null);
      setImportResult(null);
    } else {
      setCurrentStep(0);
    }
  };

  // 预览数据
  const handlePreview = async () => {
    if (fileList.length === 0) {
      message.error('请先选择文件');
      return;
    }

    const file = fileList[0].originFileObj as File;
    setPreviewLoading(true);

    try {
      const response = await previewRelationshipImport(file);
      if (response.errCode === 0 && response.data) {
        setPreviewData(response.data);
        setCurrentStep(2);

        // 检查预览结果
        if (response.data.success) {
          message.success('预览成功');
        } else {
          // 预览失败，但仍然显示错误信息
          message.warning(response.data.message || '预览发现问题，请查看详情');
        }
      } else {
        // API调用失败
        message.error(response.msg || '预览失败');
      }
    } catch (error) {
      message.error('预览失败');
      console.error('预览失败:', error);
    } finally {
      setPreviewLoading(false);
    }
  };

  // 执行导入
  const handleImport = async () => {
    if (fileList.length === 0) {
      message.error('请先选择文件');
      return;
    }

    const file = fileList[0].originFileObj as File;
    setImportLoading(true);

    try {
      const response = await executeRelationshipImport(file);
      if (response.errCode === 0 && response.data) {
        setImportResult(response.data);
        setCurrentStep(3);

        if (response.data.success) {
          message.success(
            `导入完成！成功导入 ${response.data.successCount} 条记录`,
          );
          onImportSuccess?.();
        } else {
          // 导入失败，显示具体错误信息
          const errorMsg =
            response.data.message ||
            `导入失败，有 ${response.data.failureCount || 0} 条记录失败`;
          message.error(errorMsg);
        }
      } else {
        // API调用失败
        message.error(response.msg || '导入失败');
      }
    } catch (error) {
      message.error('导入失败');
      console.error('导入失败:', error);
    } finally {
      setImportLoading(false);
    }
  };

  // 重新开始
  const handleReset = () => {
    setCurrentStep(0);
    setFileList([]);
    setPreviewData(null);
    setImportResult(null);
  };

  // 辅助函数：获取源要素类型显示名称
  const getSourceTypeDisplayName = (sourceType: string): string => {
    const typeMap: Record<string, string> = {
      ancient_city: '古城',
      cultural_element: '文化要素',
    };
    return typeMap[sourceType] || sourceType;
  };

  // 辅助函数：获取目标要素类型显示名称
  const getTargetEntityTypeDisplayName = (targetEntityType: string): string => {
    const typeMap: Record<string, string> = {
      ancient_city: '古城',
      cultural_element: '文化要素',
      type_dict: '类型字典',
    };
    return typeMap[targetEntityType] || targetEntityType;
  };

  // 预览数据表格列定义
  const previewColumns: ColumnsType<API.RelationshipImportPreviewItem> = [
    {
      title: '关系类型',
      dataIndex: 'relationName',
      width: 100,
      render: (value) => value || '-',
    },
    {
      title: '源要素类型',
      dataIndex: 'sourceType',
      width: 100,
      render: (value) => getSourceTypeDisplayName(value),
    },
    {
      title: '源要素名称',
      dataIndex: 'sourceElementName',
      width: 120,
      render: (value) => value || '-',
    },
    {
      title: '目标要素类型',
      dataIndex: 'targetEntityType',
      width: 120,
      render: (value) => getTargetEntityTypeDisplayName(value),
    },
    {
      title: '目标要素名称',
      dataIndex: 'targetElementName',
      width: 120,
      render: (value) => value || '-',
    },
    {
      title: '关联方向',
      dataIndex: 'direction',
      width: 100,
      render: (value) => value || '-',
    },
    {
      title: '词条描述',
      dataIndex: 'term',
      width: 150,
      ellipsis: true,
      render: (value) => value || '-',
    },
    {
      title: '记载内容',
      dataIndex: 'record',
      width: 200,
      ellipsis: true,
      render: (value) => value || '-',
    },
    {
      title: '排序号',
      dataIndex: 'sort',
      width: 80,
      render: (value) => value || '-',
    },
  ];

  // 错误信息表格列定义
  const errorColumns: ColumnsType<API.RelationshipImportError> = [
    {
      title: '行号',
      dataIndex: 'row',
      width: 80,
    },
    {
      title: '字段',
      dataIndex: 'field',
      width: 120,
    },
    {
      title: '值',
      dataIndex: 'value',
      width: 150,
      render: (value) => value?.toString() || '-',
    },
    {
      title: '错误信息',
      dataIndex: 'message',
      ellipsis: true,
    },
  ];

  return (
    <div>
      <Card>
        <Title level={4}>
          <ImportOutlined /> 要素关联批量导入
        </Title>

        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          <Step title="下载模板" description="下载Excel导入模板" />
          <Step title="选择文件" description="上传Excel文件" />
          <Step title="预览数据" description="验证数据格式" />
          <Step title="执行导入" description="完成数据导入" />
        </Steps>

        {/* 步骤1: 下载模板 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={18}>
              <Space direction="vertical" size="small">
                <Text strong>
                  <FileExcelOutlined /> 第一步：下载导入模板
                </Text>
                <Text type="secondary">
                  请先下载Excel模板，按照模板格式填写要素关联数据
                </Text>
              </Space>
            </Col>
            <Col span={6}>
              <Button
                type="primary"
                icon={<CloudDownloadOutlined />}
                onClick={handleDownloadTemplate}
                block
              >
                下载模板
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 步骤2: 上传文件 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={18}>
              <Space direction="vertical" size="small">
                <Text strong>
                  <CloudUploadOutlined /> 第二步：上传Excel文件
                </Text>
                <Text type="secondary">选择填写好的Excel文件进行上传</Text>
              </Space>
            </Col>
            <Col span={6}>
              <Upload
                fileList={fileList}
                beforeUpload={beforeUpload}
                onChange={handleFileChange}
                accept=".xlsx,.xls"
                maxCount={1}
              >
                <Button icon={<CloudUploadOutlined />} block>
                  选择文件
                </Button>
              </Upload>
            </Col>
          </Row>
        </Card>

        {/* 步骤3: 预览数据 */}
        {currentStep >= 1 && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col span={18}>
                <Space direction="vertical" size="small">
                  <Text strong>
                    <EyeOutlined /> 第三步：预览数据
                  </Text>
                  <Text type="secondary">
                    预览Excel中的数据，检查格式是否正确
                  </Text>
                  {previewLoading && (
                    <Text type="secondary" style={{ color: '#1890ff' }}>
                      正在解析Excel文件，请稍候...
                    </Text>
                  )}
                </Space>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  icon={<EyeOutlined />}
                  onClick={handlePreview}
                  loading={previewLoading}
                  disabled={fileList.length === 0}
                  block
                >
                  {previewLoading ? '解析中...' : '预览数据'}
                </Button>
              </Col>
            </Row>
          </Card>
        )}

        {/* 预览结果 */}
        {previewData && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Text strong>预览结果：</Text>
                <Space style={{ marginLeft: 16 }}>
                  <Text>总行数: {previewData.totalRows}</Text>
                  <Text>有效行数: {previewData.validRows}</Text>
                  {previewData.previewCount && (
                    <Text>预览条数: {previewData.previewCount}</Text>
                  )}
                </Space>
              </div>

              {/* 显示预览状态 */}
              {!previewData.success && (
                <Alert
                  type="error"
                  message="预览失败"
                  description={
                    previewData.message || '文件解析失败，请检查文件格式和内容'
                  }
                  showIcon
                />
              )}

              {previewData.success &&
                previewData.errors &&
                previewData.errors.length > 0 && (
                  <Alert
                    type="warning"
                    message={`发现 ${previewData.errors.length} 个错误`}
                    description="请修正错误后重新上传"
                    showIcon
                  />
                )}

              {previewData.success &&
                ((previewData.preview && previewData.preview.length > 0) ||
                  (previewData.data && previewData.data.length > 0)) && (
                  <div>
                    <Text strong style={{ color: '#52c41a' }}>
                      预览数据：
                    </Text>
                    <Table
                      columns={previewColumns}
                      dataSource={previewData.preview || previewData.data || []}
                      pagination={{ pageSize: 5 }}
                      scroll={{ x: 1200 }}
                      size="small"
                      rowKey={(_, index) => index?.toString() || '0'}
                    />
                  </div>
                )}

              {previewData.errors && previewData.errors.length > 0 && (
                <div>
                  <Text strong style={{ color: '#ff4d4f' }}>
                    错误详情：
                  </Text>
                  <Table
                    columns={errorColumns}
                    dataSource={previewData.errors}
                    pagination={{ pageSize: 5 }}
                    size="small"
                    rowKey={(_, index) => index?.toString() || '0'}
                  />
                </div>
              )}
            </Space>
          </Card>
        )}

        {/* 步骤4: 执行导入 */}
        {currentStep >= 2 && previewData && previewData.success && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col span={18}>
                <Space direction="vertical" size="small">
                  <Text strong>
                    <ImportOutlined /> 第四步：执行导入
                  </Text>
                  <Text type="secondary">确认数据无误后，点击执行导入</Text>
                  {importLoading && (
                    <Text type="secondary" style={{ color: '#1890ff' }}>
                      正在导入数据，请稍候...
                    </Text>
                  )}
                </Space>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  icon={<ImportOutlined />}
                  onClick={handleImport}
                  loading={importLoading}
                  disabled={
                    !previewData ||
                    !previewData.success ||
                    (previewData.errors && previewData.errors.length > 0) ||
                    ((!previewData.preview ||
                      previewData.preview.length === 0) &&
                      (!previewData.data || previewData.data.length === 0))
                  }
                  block
                >
                  {importLoading ? '导入中...' : '执行导入'}
                </Button>
              </Col>
            </Row>
          </Card>
        )}

        {/* 导入结果 */}
        {importResult && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Text strong>导入结果：</Text>
                <Space style={{ marginLeft: 16 }}>
                  <Text>总行数: {importResult.totalRows}</Text>
                  <Text>有效行数: {importResult.validRows}</Text>
                  <Text style={{ color: '#52c41a' }}>
                    成功: {importResult.successCount}
                  </Text>
                  <Text style={{ color: '#ff4d4f' }}>
                    失败: {importResult.failureCount}
                  </Text>
                </Space>
              </div>

              {importResult.successCount > 0 && (
                <Progress
                  percent={Math.round(
                    (importResult.successCount / importResult.validRows) * 100,
                  )}
                  status={importResult.failureCount > 0 ? 'active' : 'success'}
                  strokeColor={
                    importResult.failureCount > 0 ? '#faad14' : '#52c41a'
                  }
                />
              )}

              {importResult.success ? (
                <Alert
                  type="success"
                  message="导入成功"
                  description={`成功导入 ${importResult.successCount} 条要素关联记录`}
                  showIcon
                />
              ) : (
                <Alert
                  type="error"
                  message="导入失败"
                  description={importResult.message || '导入过程中发生错误'}
                  showIcon
                />
              )}

              {importResult.errors && importResult.errors.length > 0 && (
                <div>
                  <Text strong style={{ color: '#ff4d4f' }}>
                    失败详情：
                  </Text>
                  <Table
                    columns={[
                      {
                        title: '行号',
                        dataIndex: 'index',
                        width: 80,
                      },
                      {
                        title: '错误信息',
                        dataIndex: 'error',
                        ellipsis: true,
                      },
                      {
                        title: '数据',
                        dataIndex: 'data',
                        width: 200,
                        render: (data) => (
                          <Text code style={{ fontSize: '12px' }}>
                            {JSON.stringify(data, null, 2)}
                          </Text>
                        ),
                      },
                    ]}
                    dataSource={importResult.errors}
                    pagination={{ pageSize: 5 }}
                    size="small"
                    rowKey={(_, index) => index?.toString() || '0'}
                  />
                </div>
              )}
            </Space>
          </Card>
        )}

        {/* 操作按钮 */}
        {currentStep > 0 && (
          <Card size="small">
            <Row justify="end">
              <Space>
                <Button onClick={handleReset}>重新开始</Button>
                {importResult && importResult.success && (
                  <Button
                    type="primary"
                    onClick={() => window.location.reload()}
                  >
                    刷新页面
                  </Button>
                )}
              </Space>
            </Row>
          </Card>
        )}

        {/* 使用说明 */}
        <Divider />
        <Card size="small" style={{ backgroundColor: '#fafafa' }}>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Text strong>
              <InfoCircleOutlined /> 使用说明：
            </Text>
            <Paragraph style={{ margin: 0, fontSize: '12px', color: '#666' }}>
              1. 先下载Excel模板，模板包含字段说明和示例数据
              <br />
              2. 按照模板格式填写要素关联数据，注意必填字段不能为空
              <br />
              3. 上传填写好的Excel文件，系统会自动验证数据格式
              <br />
              4. 预览数据无误后，点击执行导入完成批量导入
              <br />
              5. 建议单次导入不超过1000条记录，确保导入效率
            </Paragraph>

            <Text strong style={{ fontSize: '12px' }}>
              字段说明：
            </Text>
            <Paragraph style={{ margin: 0, fontSize: '12px', color: '#666' }}>
              • 关系类型：关系类型名称（可选）
              <br />
              • 源要素类型：古城、文化要素（必填）
              <br />
              • 源要素名称：源要素的名称（必填）
              <br />
              • 目标要素类型：古城、文化要素、类型字典（必填）
              <br />
              • 目标要素名称：目标要素的名称（必填）
              <br />
              • 关联方向：关联的方向描述（可选）
              <br />
              • 词条描述：关联的词条描述（可选）
              <br />
              • 记载内容：详细的记载内容（可选）
              <br />• 排序号：显示排序号（可选）
            </Paragraph>
          </Space>
        </Card>
      </Card>
    </div>
  );
};

export default RelationshipImport;
