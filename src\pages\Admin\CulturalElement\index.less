.cultural-element-management {
  background: #f5f5f5;
  min-height: 100vh;

  // 左右布局样式
  .data-management-layout {
    .content-card {
      height: 100%;

      .ant-card-body {
        height: calc(100% - 57px);
        overflow-y: auto;
      }
    }
  }

  // 统计卡片样式
  .stat-card {
    text-align: center;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 15%);
    }

    .stat-content {
      .stat-number {
        font-size: 28px;
        font-weight: 600;
        color: #1890ff;
        line-height: 1;
        margin-bottom: 8px;
      }

      .stat-title {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }

    .ant-card-body {
      padding: 20px 16px;
    }
  }

  .search-card,
  .ant-card {
    .ant-card-body {
      padding: 20px;
    }
  }

  .search-card {
    .ant-form-item {
      margin-bottom: 16px;
    }

    // 在左右布局中的搜索表单样式
    .ant-row {
      .ant-col:last-child {
        .ant-form-item {
          margin-top: 8px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .cultural-element-management {
    padding: 8px;

    .stat-card {
      .stat-content .stat-number {
        font-size: 24px;
      }
    }

    .search-card .ant-card-body {
      padding: 16px;
    }
  }
}
