/**
 * @file 统计图表组件
 * @description 数字化统计页面的图表组件集合
 * <AUTHOR> Assistant
 * @date 2025-09-09
 */

import type {
  BasicStatisticsData,
  TimelineStatisticsData,
} from '@/services/portal';
import { Card, Col, Empty, Row } from 'antd';
import ReactECharts from 'echarts-for-react';
import React from 'react';

export interface StatisticsChartsProps {
  basicData?: BasicStatisticsData | null;
  timelineData?: TimelineStatisticsData[] | null;
  loading?: boolean;
}

/**
 * 统计图表组件
 */
export const StatisticsCharts: React.FC<StatisticsChartsProps> = ({
  basicData,
  timelineData,
  loading = false,
}) => {
  // 饼图配置 - 要素类型分布
  const getPieOption = () => {
    const counts = basicData?.counts;
    if (!counts) return null;

    return {
      title: {
        text: '要素类型分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
      },
      series: [
        {
          name: '要素数量',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          data: [
            {
              name: '古城',
              value: counts.ancientCity || 0,
              itemStyle: { color: '#52c41a' },
            },
            {
              name: '文化要素',
              value: counts.culturalElement || 0,
              itemStyle: { color: '#1890ff' },
            },
            {
              name: '关系',
              value: counts.relationship || 0,
              itemStyle: { color: '#fa8c16' },
            },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          label: {
            show: true,
            formatter: '{b}: {c}',
          },
        },
      ],
    };
  };

  // 区域分布柱状图配置
  const getRegionDistributionOption = () => {
    const regionStats = basicData?.regionStats || [];
    if (!regionStats.length) return null;

    return {
      title: {
        text: '区域分布统计',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          const region = regionStats[data.dataIndex];
          return `
            <div>
              <strong>${region.region}</strong><br/>
              总计: ${region.total}<br/>
              古城: ${region.ancientCityCount}<br/>
              文化要素: ${region.culturalElementCount}<br/>
              关系: ${region.relationshipCount}
            </div>
          `;
        },
      },
      xAxis: {
        type: 'category',
        data: regionStats.map((r) => r.region),
        name: '区域',
        axisLabel: {
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
        name: '数量',
      },
      series: [
        {
          name: '总数量',
          type: 'bar',
          data: regionStats.map((r) => r.total),
          itemStyle: {
            color: '#1890ff',
          },
        },
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
    };
  };

  // 时间轴配置
  const getTimelineOption = () => {
    const timelineStats = basicData?.timelineData || timelineData || [];
    if (!timelineStats.length) return null;

    // 转换数据格式
    const chartData = timelineStats.map((item) => ({
      year: item.year,
      count: item.elements?.length || 0,
      elements: item.elements || [],
    }));

    return {
      title: {
        text: '古城与文化要素时间分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          const timelineItem = chartData[data.dataIndex];
          const elementsText = timelineItem.elements
            .slice(0, 5)
            .map((el: any) => `• ${el.name}`)
            .join('<br/>');
          const moreText =
            timelineItem.elements.length > 5
              ? `<br/>... 还有 ${timelineItem.elements.length - 5} 个要素`
              : '';

          return `
            <div>
              <strong>${timelineItem.year}年</strong><br/>
              建造数量: ${timelineItem.count}<br/>
              <br/>
              ${elementsText}
              ${moreText}
            </div>
          `;
        },
      },
      xAxis: {
        type: 'category',
        data: chartData.map((t) => String(t.year)),
        name: '年份',
      },
      yAxis: {
        type: 'value',
        name: '建造数量',
      },
      series: [
        {
          name: '建造数量',
          type: 'line',
          data: chartData.map((t) => t.count),
          itemStyle: {
            color: '#52c41a',
          },
          lineStyle: {
            color: '#52c41a',
            width: 3,
          },
          symbol: 'circle',
          symbolSize: 8,
          smooth: true,
        },
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
    };
  };

  const pieOption = getPieOption();
  const regionOption = getRegionDistributionOption();
  const timelineOption = getTimelineOption();

  return (
    <>
      {/* 第一行图表 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="要素类型分布" loading={loading}>
            {pieOption ? (
              <ReactECharts
                option={pieOption}
                style={{ height: '400px' }}
                opts={{ renderer: 'svg' }}
              />
            ) : (
              <Empty
                description="暂无数据"
                style={{
                  height: '400px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                }}
              />
            )}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="区域分布统计" loading={loading}>
            {regionOption ? (
              <ReactECharts
                option={regionOption}
                style={{ height: '400px' }}
                opts={{ renderer: 'svg' }}
              />
            ) : (
              <Empty
                description="暂无数据"
                style={{
                  height: '400px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                }}
              />
            )}
          </Card>
        </Col>
      </Row>

      {/* 第二行图表 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24}>
          <Card title="历史要素时间分布" loading={loading}>
            {timelineOption ? (
              <ReactECharts
                option={timelineOption}
                style={{ height: '400px' }}
                opts={{ renderer: 'svg' }}
              />
            ) : (
              <Empty
                description="暂无数据"
                style={{
                  height: '400px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                }}
              />
            )}
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default StatisticsCharts;
