import AncientCityListSelector from '@/components/AncientCityListSelector';
import DataManagementLayout from '@/components/DataManagementLayout';
import PermissionWrapper from '@/components/PermissionWrapper';
import TypeTreeSelect from '@/components/TypeTreeSelect';

import YearRangePicker from '@/components/YearRangePicker';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { history, useDispatch } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  Row,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import BatchImportModal from './components/BatchImportModal';
import useCulturalElementData from './hooks/useCulturalElementData';
import useCulturalElementOperations from './hooks/useCulturalElementOperations';
import './index.less';

const CulturalElementManagement: React.FC = () => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedCityId, setSelectedCityId] = useState<number | undefined>();
  const [importModalVisible, setImportModalVisible] = useState(false);

  // 使用自定义Hook管理数据
  const {
    data,
    statistics,
    loading,
    pagination,
    fetchData,
    fetchStatistics,
    setPagination,
  } = useCulturalElementData();

  // 使用自定义Hook管理操作
  const {
    deleteLoading,
    batchDeleteLoading,
    importLoading,
    exportLoading,
    handleDelete,
    handleBatchDelete,
    handleImport,
    handlePreviewImport,
    handleExport,
    handleDownloadTemplate,
  } = useCulturalElementOperations();

  // 搜索表单提交
  const handleSearch = async (values: any) => {
    const params = {
      ...values,
      page: 1,
      pageSize: pagination.pageSize,
    };
    await fetchData(
      params.page,
      params.pageSize,
      params.keyword,
      params.typeDictId,
      params.ancientCityId,
      params.constructionYear,
    );
  };

  // 重置搜索
  const handleReset = async () => {
    form.resetFields();
    setSelectedCityId(undefined);
    await fetchData(1, pagination.pageSize);
  };

  // 处理古城选择变化
  const handleCityChange = async (cityId?: number) => {
    setSelectedCityId(cityId);
    // 更新表单中的古城字段
    form.setFieldValue('ancientCityId', cityId);
    // 重新搜索数据
    const formValues = form.getFieldsValue();
    await fetchData(
      1,
      pagination.pageSize,
      formValues.keyword,
      formValues.typeDictId,
      cityId,
      formValues.constructionYear,
    );
  };

  // 表格列定义
  const columns: ColumnsType<API.CulturalElement> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true,
      render: (text: string, record: API.CulturalElement) => (
        <Tooltip title={text}>
          <Button
            type="link"
            onClick={() =>
              history.push(`/admin/cultural-element/detail/${record.id}`)
            }
          >
            {text}
          </Button>
        </Tooltip>
      ),
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'typeName',
      key: 'typeName',
      width: 100,
      render: (text: string, record: API.CulturalElement) => (
        <Tag color="blue">{text || record.typeDict?.typeName || '-'}</Tag>
      ),
    },
    {
      title: '古城',
      dataIndex: 'cityName',
      key: 'cityName',
      width: 120,
      render: (text: string, record: API.CulturalElement) => {
        const cityName =
          text || record.ancientCity?.name || record.ancientCityName;
        return <Tag color="orange">{cityName || '-'}</Tag>;
      },
    },
    {
      title: '位置',
      key: 'location',
      width: 150,
      render: (_, record: API.CulturalElement) => {
        if (record.longitude && record.latitude) {
          return `${record.longitude}, ${record.latitude}`;
        }
        return '-';
      },
    },
    {
      title: '建造年份',
      dataIndex: 'constructionYear',
      key: 'constructionYear',
      width: 100,
      render: (year: number) => year || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: Date) => {
        if (!date) return '-';
        return new Date(date).toLocaleDateString();
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: API.CulturalElement) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() =>
                history.push(`/admin/cultural-element/detail/${record.id}`)
              }
            />
          </Tooltip>
          <PermissionWrapper permission="canEdit">
            <Tooltip title="编辑">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() =>
                  history.push(`/admin/cultural-element/edit/${record.id}`)
                }
              />
            </Tooltip>
          </PermissionWrapper>
          <PermissionWrapper permission="canDelete">
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                loading={deleteLoading}
                onClick={() => handleDelete(record.id, record.name, fetchData)}
              />
            </Tooltip>
          </PermissionWrapper>
        </Space>
      ),
    },
  ];

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 分页变化处理
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination((prev) => ({ ...prev, current: page, pageSize }));
    const formValues = form.getFieldsValue();
    fetchData(
      page,
      pageSize,
      formValues.keyword,
      formValues.typeDictId,
      formValues.ancientCityId,
      formValues.constructionYear,
    );
  };

  // 导入相关处理函数
  const handleImportModalOpen = () => {
    setImportModalVisible(true);
  };

  const handleImportModalClose = () => {
    setImportModalVisible(false);
  };

  const handleImportSuccess = () => {
    fetchData();
    fetchStatistics();
    handleImportModalClose();
  };

  // 初始化数据
  useEffect(() => {
    fetchData();
    fetchStatistics();
    // 预加载古城数据以确保古城名称正确显示
    dispatch({ type: 'ancientCity/fetchAncientCityList' });
  }, [fetchData, fetchStatistics, dispatch]);

  return (
    <PageContainer
      title="文化要素管理"
      content="统一管理关中地区的山塬、水系、历史要素等文化要素信息"
      className="cultural-element-management"
    >
      {/* 统计信息 */}
      {statistics && (
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={6} sm={6} md={6} lg={6}>
            <Card size="small" className="stat-card">
              <div className="stat-content">
                <div className="stat-number">{statistics.total}</div>
                <div className="stat-title">总数</div>
              </div>
            </Card>
          </Col>
          {statistics.byType &&
            statistics.byType.slice(0, 3).map((item, index) => (
              <Col key={index} xs={6} sm={6} md={6} lg={6}>
                <Card size="small" className="stat-card">
                  <div className="stat-content">
                    <div className="stat-number">{item.count}</div>
                    <div className="stat-title">{item.typeName}</div>
                  </div>
                </Card>
              </Col>
            ))}
        </Row>
      )}
      {/* 左右布局 */}
      <DataManagementLayout
        selectorTitle="古城选择"
        selectorContent={
          <AncientCityListSelector
            selectedCityId={selectedCityId}
            onCityChange={handleCityChange}
          />
        }
        style={{ height: 'calc(100vh - 200px)' }}
      >
        {/* 数据表格 */}
        <Card>
          {/* 搜索表单 */}
          <Form
            form={form}
            onFinish={handleSearch}
            layout="vertical"
            style={{ marginBottom: 16 }}
          >
            {/* 隐藏的古城字段，与左侧选择器同步 */}
            <Form.Item name="ancientCityId" style={{ display: 'none' }}>
              <Input />
            </Form.Item>
            <Row gutter={16}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="关键词" name="keyword">
                  <Input placeholder="请输入名称或编号" allowClear />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="类型" name="typeDictId">
                  <TypeTreeSelect placeholder="请选择类型" allowClear />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="建造年份" name="constructionYear">
                  <YearRangePicker placeholder={['起始年份', '结束年份']} />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={24} lg={6}>
                <Form.Item label=" " style={{ marginBottom: 0 }}>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<SearchOutlined />}
                    >
                      搜索
                    </Button>
                    <Button onClick={handleReset}>重置</Button>
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          </Form>

          {/* 操作工具栏 */}
          <Row
            justify="space-between"
            align="middle"
            style={{ marginBottom: 16 }}
          >
            <Col>
              <Space>
                <PermissionWrapper permission="canEdit">
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() =>
                      history.push('/admin/cultural-element/create')
                    }
                  >
                    新增文化要素
                  </Button>
                </PermissionWrapper>
                <PermissionWrapper permission="canDelete">
                  <Button
                    danger
                    disabled={selectedRowKeys.length === 0}
                    loading={batchDeleteLoading}
                    onClick={() =>
                      handleBatchDelete(selectedRowKeys, fetchData, () =>
                        setSelectedRowKeys([]),
                      )
                    }
                  >
                    批量删除 ({selectedRowKeys.length})
                  </Button>
                </PermissionWrapper>
              </Space>
            </Col>
            <Col>
              <Space>
                <PermissionWrapper permission="canImport">
                  <Button onClick={handleImportModalOpen}>导入数据</Button>
                </PermissionWrapper>
                <PermissionWrapper permission="canExport">
                  <Button
                    loading={exportLoading}
                    onClick={() => handleExport(form.getFieldsValue())}
                  >
                    导出数据
                  </Button>
                </PermissionWrapper>
              </Space>
            </Col>
          </Row>
          <Table
            columns={columns}
            dataSource={data}
            rowKey="id"
            loading={loading}
            rowSelection={rowSelection}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              onChange: handleTableChange,
              onShowSizeChange: handleTableChange,
            }}
            scroll={{ x: 1200 }}
          />
        </Card>

        {/* 批量导入弹窗 */}
        <BatchImportModal
          visible={importModalVisible}
          loading={importLoading}
          onDownloadTemplate={handleDownloadTemplate}
          onPreview={handlePreviewImport}
          onImport={async (file) => {
            const result = await handleImport(file, handleImportSuccess);
            return result;
          }}
          onCancel={handleImportModalClose}
        />
      </DataManagementLayout>
    </PageContainer>
  );
};

export default CulturalElementManagement;
