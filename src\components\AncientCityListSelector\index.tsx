/**
 * @file 古城列表选择器组件
 * @description 用于左侧布局的古城选择器，显示古城列表并支持选择
 * <AUTHOR> Assistant
 * @date 2025-10-12
 */
import type { AncientCityState } from '@/models/ancientCity';
import { SearchOutlined } from '@ant-design/icons';
import { connect } from '@umijs/max';
import { Input, List, Spin, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Text } = Typography;

export interface AncientCityListSelectorProps {
  /** 当前选中的古城ID */
  selectedCityId?: number;
  /** 选择变化回调 */
  onCityChange?: (cityId?: number, cityInfo?: API.AncientCityDict) => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** dva状态 */
  ancientCity?: AncientCityState;
  /** dva dispatch */
  dispatch?: any;
}

const AncientCityListSelector: React.FC<AncientCityListSelectorProps> = ({
  selectedCityId,
  onCityChange,
  style,
  className,
  ancientCity,
  dispatch,
}) => {
  const cities = ancientCity?.ancientCityList || [];
  const loading = ancientCity?.loading || false;
  const [filteredCities, setFilteredCities] = useState<API.AncientCityDict[]>(
    [],
  );
  const [searchKeyword, setSearchKeyword] = useState('');

  // 初始化加载数据
  useEffect(() => {
    if (dispatch && (!cities || cities.length === 0)) {
      dispatch({ type: 'ancientCity/fetchAncientCityList' });
    }
  }, [dispatch, cities]);

  // 搜索过滤
  useEffect(() => {
    if (!searchKeyword.trim()) {
      setFilteredCities(cities);
    } else {
      const keyword = searchKeyword.toLowerCase();
      const filtered = cities.filter(
        (city) =>
          city.cityName.toLowerCase().includes(keyword) ||
          (city.region && city.region.toLowerCase().includes(keyword)),
      );
      setFilteredCities(filtered);
    }
  }, [searchKeyword, cities]);

  // 处理古城选择
  const handleCitySelect = (city: API.AncientCityDict) => {
    onCityChange?.(city.id, city);
  };

  // 处理清除选择
  const handleClearSelection = () => {
    onCityChange?.(undefined, undefined);
  };

  return (
    <div
      className={`ancient-city-list-selector ${className || ''}`}
      style={style}
    >
      {/* 搜索框 */}
      <div className="search-section">
        <Input
          placeholder="搜索古城名称或区域"
          prefix={<SearchOutlined />}
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          allowClear
        />
      </div>

      {/* 全部选项 */}
      <div className="all-option">
        <div
          className={`city-item ${!selectedCityId ? 'selected' : ''}`}
          onClick={handleClearSelection}
        >
          <div className="city-info">
            <Text strong>全部古城</Text>
            <Text type="secondary" className="city-desc">
              显示所有文化要素
            </Text>
          </div>
        </div>
      </div>

      {/* 古城列表 */}
      <div className="city-list">
        <Spin spinning={loading}>
          <List
            dataSource={filteredCities}
            renderItem={(city) => (
              <List.Item
                className={`city-item ${
                  selectedCityId === city.id ? 'selected' : ''
                }`}
                onClick={() => handleCitySelect(city)}
              >
                <div className="city-info">
                  <Text strong>{city.cityName}</Text>
                  {city.region && (
                    <Text type="secondary" className="city-region">
                      {city.region}
                    </Text>
                  )}
                  {city.cityDesc && (
                    <Text type="secondary" className="city-desc">
                      {city.cityDesc}
                    </Text>
                  )}
                </div>
              </List.Item>
            )}
            locale={{ emptyText: loading ? '加载中...' : '暂无数据' }}
          />
        </Spin>
      </div>
    </div>
  );
};

export default connect(
  ({ ancientCity }: { ancientCity: AncientCityState }) => ({
    ancientCity,
  }),
)(AncientCityListSelector);
