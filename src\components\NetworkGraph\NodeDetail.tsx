import { Card, Descriptions, Tag, Typography } from 'antd';
import React from 'react';

const { Title } = Typography;

export interface NodeDetailProps {
  nodeData: any;
  visible: boolean;
}

const NodeDetail: React.FC<NodeDetailProps> = ({ nodeData, visible }) => {
  if (!visible || !nodeData) {
    return null;
  }

  // 根据节点类型获取标题颜色
  const getTitleColor = () => {
    const colorMap: Record<string, string> = {
      mountain: '#1890ff', // 蓝色 - 文化要素
      water_system: '#1890ff', // 蓝色 - 文化要素
      historical_element: '#1890ff', // 蓝色 - 文化要素
      cultural_element: '#1890ff', // 蓝色 - 文化要素
      ancient_city: '#722ed1', // 紫色 - 古城
      type_dict: '#52c41a', // 绿色 - 类型
    };

    return colorMap[nodeData.type] || '#1890ff';
  };

  // 获取节点类型的中文名称
  const getTypeName = () => {
    const typeMap: Record<string, string> = {
      mountain: '文化要素',
      water_system: '文化要素',
      historical_element: '文化要素',
      cultural_element: '文化要素',
      ancient_city: '古城',
      type_dict: '类型',
    };

    return typeMap[nodeData.type] || '文化要素';
  };

  return (
    <Card
      title={
        <Title level={4} style={{ color: getTitleColor(), margin: 0 }}>
          节点详情
        </Title>
      }
      style={{ height: '100%' }}
    >
      <Descriptions column={1} bordered size="small">
        <Descriptions.Item label="名称">{nodeData.name}</Descriptions.Item>
        <Descriptions.Item label="类型">
          <Tag color={getTitleColor()}>{getTypeName()}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="分类">{nodeData.category}</Descriptions.Item>
        <Descriptions.Item label="ID">{nodeData.id}</Descriptions.Item>
      </Descriptions>

      {/* 可以根据不同类型的节点显示不同的额外信息 */}
      {nodeData.type === 'historical_element' && (
        <div style={{ marginTop: 16 }}>
          <Title level={5}>相关关系：</Title>
          <div>
            <Tag color="blue">陕西</Tag>
            <Tag color="purple">秦朝</Tag>
          </div>
        </div>
      )}
    </Card>
  );
};

export default NodeDetail;
