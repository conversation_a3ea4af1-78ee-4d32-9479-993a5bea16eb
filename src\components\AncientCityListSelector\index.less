.ancient-city-list-selector {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-section {
    margin-bottom: 12px;
  }

  .all-option {
    margin-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }

  .city-list {
    flex: 1;
    overflow-y: auto;

    .ant-list {
      height: 100%;
    }

    .ant-list-item {
      padding: 0;
      border: none;
    }
  }

  .city-item {
    padding: 12px;
    cursor: pointer;
    border-radius: 6px;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    border: 1px solid transparent;

    &:hover {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
    }

    &.selected {
      background-color: #e6f7ff;
      border-color: #1890ff;

      .city-info {
        .ant-typography {
          color: #1890ff;
        }
      }
    }

    .city-info {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .city-region {
        font-size: 12px;
        color: #8c8c8c;
      }

      .city-desc {
        font-size: 12px;
        color: #bfbfbf;
        line-height: 1.4;
        display: box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  // 滚动条样式
  .city-list::-webkit-scrollbar {
    width: 6px;
  }

  .city-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .city-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .city-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ancient-city-list-selector {
    .city-item {
      padding: 8px;

      .city-info {
        gap: 2px;
      }
    }
  }
}
