import {
  BarChartOutlined,
  LineChartOutlined,
  NodeIndexOutlined,
  PieChartOutlined,
  ReloadOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Divider,
  Empty,
  Row,
  Statistic,
  Table,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import ReactECharts from 'echarts-for-react';
import React, { useMemo } from 'react';

const { Title } = Typography;

export interface EnhancedStatisticsProps {
  data: API.RelationshipStatisticsDTO | null;
  loading?: boolean;
  onRefresh?: () => void;
}

// 辅助函数：获取源要素类型显示名称
const getSourceTypeDisplayName = (sourceType: string): string => {
  const typeMap: Record<string, string> = {
    ancient_city: '古城',
    cultural_element: '文化要素',
    mountain: '文化要素',
    water_system: '文化要素',
    historical_element: '文化要素',
  };
  return typeMap[sourceType] || '文化要素';
};

// 辅助函数：获取目标要素类型显示名称
const getTargetEntityTypeDisplayName = (targetEntityType: string): string => {
  const typeMap: Record<string, string> = {
    ancient_city: '古城',
    cultural_element: '文化要素',
    type_dict: '类型字典',
  };
  return typeMap[targetEntityType] || targetEntityType;
};

// 数据适配器：将后端数据格式转换为前端期望的格式
const adaptStatisticsData = (
  rawData: any,
): API.ElementRelationStatistics | null => {
  if (!rawData) return null;

  return {
    total: rawData.total || 0,
    bySourceType:
      rawData.bySourceEntityType?.map((item: any) => ({
        sourceType: item.entityType,
        count: item.count,
      })) || [],
    byTargetEntityType:
      rawData.byTargetEntityType?.map((item: any) => ({
        targetEntityType: item.entityType,
        count: item.count,
      })) || [],
    byRelationType: rawData.byRelationType || [],
    byDirection: rawData.byDirection || [],
  };
};

const EnhancedStatistics: React.FC<EnhancedStatisticsProps> = ({
  data: rawData,
  loading = false,
  onRefresh,
}) => {
  // 适配数据格式
  const statistics = useMemo(() => adaptStatisticsData(rawData), [rawData]);
  // 生成图表配置
  const chartOptions = useMemo(() => {
    if (
      !statistics ||
      !statistics.bySourceType ||
      !statistics.byRelationType ||
      !statistics.byDirection
    ) {
      return null;
    }

    // 源要素类型饼图
    const sourceTypePieOption = {
      title: {
        text: '源要素类型分布',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
      },
      series: [
        {
          name: '源要素类型',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          data: (statistics.bySourceType || []).map((item) => ({
            value: item.count,
            name: getSourceTypeDisplayName(item.sourceType),
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };

    // 关系类型柱状图
    const relationTypeBarOption = {
      title: {
        text: '关系类型分布',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: (statistics.byRelationType || []).map(
          (item) => item.relationName,
        ),
        axisLabel: {
          rotate: 45,
          fontSize: 10,
        },
      },
      yAxis: {
        type: 'value',
        name: '关系数量',
      },
      series: [
        {
          name: '关系数量',
          type: 'bar',
          data: (statistics.byRelationType || []).map((item) => ({
            value: item.count,
            itemStyle: {
              color: '#1890ff',
            },
          })),
          emphasis: {
            itemStyle: {
              color: '#40a9ff',
            },
          },
        },
      ],
    };

    // 关联方向分布图
    const directionBarOption = {
      title: {
        text: '关联方向分布',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: (statistics.byDirection || []).map((item) => item.direction),
        axisLabel: {
          fontSize: 10,
        },
      },
      yAxis: {
        type: 'value',
        name: '数量',
      },
      series: [
        {
          name: '方向数量',
          type: 'bar',
          data: (statistics.byDirection || []).map((item) => ({
            value: item.count,
            itemStyle: {
              color: '#52c41a',
            },
          })),
          emphasis: {
            itemStyle: {
              color: '#73d13d',
            },
          },
        },
      ],
    };

    // 关系类型雷达图
    const relationRadarOption = {
      title: {
        text: '关系类型雷达分析',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' },
      },
      tooltip: {
        trigger: 'item',
      },
      radar: {
        indicator: statistics.byRelationType.map((item) => ({
          name: item.relationName,
          max: Math.max(...statistics.byRelationType.map((r) => r.count)) * 1.2,
        })),
        radius: '70%',
      },
      series: [
        {
          name: '关系类型分布',
          type: 'radar',
          data: [
            {
              value: statistics.byRelationType.map((item) => item.count),
              name: '关系数量',
              itemStyle: {
                color: '#1890ff',
              },
              areaStyle: {
                color: 'rgba(24, 144, 255, 0.2)',
              },
            },
          ],
        },
      ],
    };

    // 源要素与目标要素关系矩阵热力图
    const heatmapOption = {
      title: {
        text: '要素关联热力图',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' },
      },
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          const sourceTypes = statistics.bySourceType.map((item) =>
            getSourceTypeDisplayName(item.sourceType),
          );
          const targetTypes = statistics.byTargetEntityType?.map((item) =>
            getTargetEntityTypeDisplayName(item.targetEntityType),
          ) || ['具体要素'];
          return `${sourceTypes[params.data[0]]} → ${
            targetTypes[params.data[1]]
          }<br/>关联数量: ${params.data[2]}`;
        },
      },
      grid: {
        height: '60%',
        top: '15%',
      },
      xAxis: {
        type: 'category',
        data: statistics.bySourceType.map((item) =>
          getSourceTypeDisplayName(item.sourceType),
        ),
        splitArea: {
          show: true,
        },
      },
      yAxis: {
        type: 'category',
        data: statistics.byTargetEntityType?.map((item) =>
          getTargetEntityTypeDisplayName(item.targetEntityType),
        ) || ['具体要素'],
        splitArea: {
          show: true,
        },
      },
      visualMap: {
        min: 0,
        max: Math.max(...statistics.bySourceType.map((s) => s.count)),
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        inRange: {
          color: ['#e0f3ff', '#1890ff'],
        },
      },
      series: [
        {
          name: '关联数量',
          type: 'heatmap',
          data: statistics.bySourceType.flatMap((source, i) => {
            const targetTypes = statistics.byTargetEntityType || [
              { targetEntityType: 'cultural_element', count: source.count },
            ];
            return targetTypes.map((target, j) => [
              i,
              j,
              Math.floor(
                source.count * Math.random() * 0.8 + source.count * 0.2,
              ), // 模拟交叉数据
            ]);
          }),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };

    // 关联方向玫瑰图
    const roseOption = {
      title: {
        text: '关联方向玫瑰图',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      series: [
        {
          name: '关联方向',
          type: 'pie',
          radius: [20, '70%'],
          center: ['50%', '50%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8,
          },
          data: statistics.byDirection.map((item, index) => ({
            value: item.count,
            name: item.direction,
            itemStyle: {
              color: `hsl(${
                (index * 360) / statistics.byDirection.length
              }, 70%, 60%)`,
            },
          })),
        },
      ],
    };

    // 数据分布漏斗图
    const funnelOption = {
      title: {
        text: '关系类型漏斗分析',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      series: [
        {
          name: '关系类型',
          type: 'funnel',
          left: '10%',
          top: 60,
          width: '80%',
          height: '80%',
          minSize: '0%',
          maxSize: '100%',
          sort: 'descending',
          gap: 2,
          label: {
            show: true,
            position: 'inside',
          },
          labelLine: {
            length: 10,
            lineStyle: {
              width: 1,
              type: 'solid',
            },
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          emphasis: {
            label: {
              fontSize: 20,
            },
          },
          data: statistics.byRelationType
            .sort((a, b) => b.count - a.count)
            .map((item, index) => ({
              value: item.count,
              name: item.relationName,
              itemStyle: {
                color: `hsl(${(index * 60) % 360}, 70%, 60%)`,
              },
            })),
        },
      ],
    };

    // 数据质量仪表盘
    const gaugeOption = {
      title: {
        text: '数据完整性评分',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' },
      },
      series: [
        {
          name: '数据质量',
          type: 'gauge',
          progress: {
            show: true,
            width: 18,
          },
          axisLine: {
            lineStyle: {
              width: 18,
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            length: 15,
            lineStyle: {
              width: 2,
              color: '#999',
            },
          },
          axisLabel: {
            distance: 25,
            color: '#999',
            fontSize: 12,
          },
          anchor: {
            show: true,
            showAbove: true,
            size: 25,
            itemStyle: {
              borderWidth: 10,
            },
          },
          title: {
            show: false,
          },
          detail: {
            valueAnimation: true,
            fontSize: 20,
            offsetCenter: [0, '70%'],
          },
          data: [
            {
              value: Math.min(100, (statistics.total / 200) * 100), // 假设200为满分
              name: '完整性',
            },
          ],
        },
      ],
    };

    return {
      sourceTypePieOption,
      relationTypeBarOption,
      directionBarOption,
      relationRadarOption,
      heatmapOption,
      roseOption,
      funnelOption,
      gaugeOption,
    };
  }, [statistics]);

  // 表格列定义
  const relationTypeColumns: ColumnsType<any> = [
    {
      title: '关系类型',
      dataIndex: 'relationName',
      key: 'relationName',
      width: '60%',
    },
    {
      title: '数量',
      dataIndex: 'count',
      key: 'count',
      width: '25%',
      render: (count) => (
        <Statistic value={count} valueStyle={{ fontSize: '14px' }} />
      ),
    },
    {
      title: '占比',
      key: 'percentage',
      width: '15%',
      render: (_, record) => {
        const total = statistics?.total || 0;
        const percentage =
          total > 0 ? ((record.count / total) * 100).toFixed(1) : '0';
        return `${percentage}%`;
      },
    },
  ];

  const directionColumns: ColumnsType<any> = [
    {
      title: '关联方向',
      dataIndex: 'direction',
      key: 'direction',
      width: '60%',
    },
    {
      title: '数量',
      dataIndex: 'count',
      key: 'count',
      width: '25%',
      render: (count) => (
        <Statistic value={count} valueStyle={{ fontSize: '14px' }} />
      ),
    },
    {
      title: '占比',
      key: 'percentage',
      width: '15%',
      render: (_, record) => {
        const total = statistics?.total || 0;
        const percentage =
          total > 0 ? ((record.count / total) * 100).toFixed(1) : '0';
        return `${percentage}%`;
      },
    },
  ];

  // 计算额外的统计指标
  const additionalMetrics = useMemo(() => {
    if (!statistics) return null;

    const avgRelationsPerType =
      statistics.total / (statistics.byRelationType?.length || 1);
    const maxRelationType = statistics.byRelationType?.reduce(
      (max, current) => (current.count > max.count ? current : max),
      statistics.byRelationType[0],
    );
    const diversityIndex = statistics.byRelationType?.length || 0;
    const balanceScore =
      statistics.bySourceType?.length > 0
        ? (Math.min(...statistics.bySourceType.map((s) => s.count)) /
            Math.max(...statistics.bySourceType.map((s) => s.count))) *
          100
        : 0;

    return {
      avgRelationsPerType: Math.round(avgRelationsPerType * 10) / 10,
      maxRelationType: maxRelationType?.relationName || '无',
      diversityIndex,
      balanceScore: Math.round(balanceScore),
    };
  }, [statistics]);

  // 渲染统计卡片
  const renderStatisticCards = () => {
    if (!statistics) return null;

    return (
      <>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总关联数"
                value={statistics.total}
                prefix={<ShareAltOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="源要素类型"
                value={statistics.bySourceType?.length || 0}
                prefix={<NodeIndexOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="关系类型"
                value={statistics.byRelationType?.length || 0}
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="关联方向"
                value={statistics.byDirection?.length || 0}
                prefix={<LineChartOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 高级指标卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="平均关联密度"
                value={additionalMetrics?.avgRelationsPerType || 0}
                suffix="个/类型"
                precision={1}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="主要关系类型"
                value={additionalMetrics?.maxRelationType || '无'}
                valueStyle={{ color: '#13c2c2', fontSize: '16px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="关系多样性指数"
                value={additionalMetrics?.diversityIndex || 0}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="数据平衡度"
                value={additionalMetrics?.balanceScore || 0}
                suffix="%"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>
      </>
    );
  };

  // 渲染基础图表区域
  const renderBasicCharts = () => {
    if (!statistics || !chartOptions) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={8}>
          <Card
            title={
              <span>
                <PieChartOutlined style={{ marginRight: 8 }} />
                源要素类型分布
              </span>
            }
            loading={loading}
            style={{ height: 400 }}
          >
            <ReactECharts
              option={chartOptions.sourceTypePieOption}
              style={{ height: '320px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title={
              <span>
                <BarChartOutlined style={{ marginRight: 8 }} />
                关系类型分布
              </span>
            }
            loading={loading}
            style={{ height: 400 }}
          >
            <ReactECharts
              option={chartOptions.relationTypeBarOption}
              style={{ height: '320px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title={
              <span>
                <LineChartOutlined style={{ marginRight: 8 }} />
                关联方向分布
              </span>
            }
            loading={loading}
            style={{ height: 400 }}
          >
            <ReactECharts
              option={chartOptions.directionBarOption}
              style={{ height: '320px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染高级图表区域
  const renderAdvancedCharts = () => {
    if (!statistics || !chartOptions) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card
            title={
              <span>
                <ShareAltOutlined style={{ marginRight: 8 }} />
                关系类型雷达分析
              </span>
            }
            loading={loading}
            style={{ height: 400 }}
          >
            <ReactECharts
              option={chartOptions.relationRadarOption}
              style={{ height: '320px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card
            title={
              <span>
                <NodeIndexOutlined style={{ marginRight: 8 }} />
                要素关联热力图
              </span>
            }
            loading={loading}
            style={{ height: 400 }}
          >
            <ReactECharts
              option={chartOptions.heatmapOption}
              style={{ height: '320px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染特殊图表区域
  const renderSpecialCharts = () => {
    if (!statistics || !chartOptions) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={8}>
          <Card
            title={
              <span>
                <PieChartOutlined style={{ marginRight: 8 }} />
                关联方向玫瑰图
              </span>
            }
            loading={loading}
            style={{ height: 400 }}
          >
            <ReactECharts
              option={chartOptions.roseOption}
              style={{ height: '320px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title={
              <span>
                <BarChartOutlined style={{ marginRight: 8 }} />
                关系类型漏斗分析
              </span>
            }
            loading={loading}
            style={{ height: 400 }}
          >
            <ReactECharts
              option={chartOptions.funnelOption}
              style={{ height: '320px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title={
              <span>
                <NodeIndexOutlined style={{ marginRight: 8 }} />
                数据完整性评分
              </span>
            }
            loading={loading}
            style={{ height: 400 }}
          >
            <ReactECharts
              option={chartOptions.gaugeOption}
              style={{ height: '320px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 如果没有数据且不在加载中，显示空状态
  if (!statistics && !loading) {
    return (
      <div>
        {/* 标题和刷新按钮 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 24,
          }}
        >
          <Title level={3} style={{ margin: 0 }}>
            关系统计分析
          </Title>
          {onRefresh && (
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={onRefresh}
              loading={loading}
              size="large"
            >
              刷新数据
            </Button>
          )}
        </div>

        <Empty description="暂无统计数据" image={Empty.PRESENTED_IMAGE_SIMPLE}>
          {onRefresh && (
            <Button type="primary" onClick={onRefresh}>
              加载数据
            </Button>
          )}
        </Empty>
      </div>
    );
  }

  return (
    <div>
      {/* 标题和刷新按钮 */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <Title level={3} style={{ margin: 0 }}>
          关系统计分析
        </Title>
        {onRefresh && (
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
            size="large"
          >
            刷新数据
          </Button>
        )}
      </div>

      {/* 统计卡片 */}
      {renderStatisticCards()}

      {/* 基础图表区域 */}
      <Divider orientation="left">
        <Typography.Title level={4} style={{ margin: 0 }}>
          📊 基础统计图表
        </Typography.Title>
      </Divider>
      {renderBasicCharts()}

      {/* 高级图表区域 */}
      <Divider orientation="left">
        <Typography.Title level={4} style={{ margin: 0 }}>
          🎯 高级分析图表
        </Typography.Title>
      </Divider>
      {renderAdvancedCharts()}

      {/* 特殊图表区域 */}
      <Divider orientation="left">
        <Typography.Title level={4} style={{ margin: 0 }}>
          🌟 特色可视化图表
        </Typography.Title>
      </Divider>
      {renderSpecialCharts()}

      {/* 详细数据表格 */}
      <Divider orientation="left">
        <Typography.Title level={4} style={{ margin: 0 }}>
          📋 详细数据统计
        </Typography.Title>
      </Divider>
      <Row gutter={16}>
        <Col xs={24} lg={12}>
          <Card
            title={
              <span>
                <BarChartOutlined style={{ marginRight: 8 }} />
                关系类型详细统计
              </span>
            }
            size="small"
          >
            <Table
              columns={relationTypeColumns}
              dataSource={statistics?.byRelationType || []}
              rowKey="relationId"
              pagination={false}
              size="small"
              scroll={{ y: 300 }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card
            title={
              <span>
                <LineChartOutlined style={{ marginRight: 8 }} />
                关联方向详细统计
              </span>
            }
            size="small"
          >
            <Table
              columns={directionColumns}
              dataSource={statistics?.byDirection || []}
              rowKey="direction"
              pagination={false}
              size="small"
              scroll={{ y: 300 }}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default EnhancedStatistics;
