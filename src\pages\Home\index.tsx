import FullPageScroll from '@/components/FullPageScroll';
import GaoDeMap from '@/components/GaoDeMap';
import NetworkGraph from '@/components/NetworkGraph';
import PageHeader from '@/components/PageHeader';
import PublicLayout from '@/components/PublicLayout';
import RelationSidePanel from '@/components/RelationSidePanel';
import RecentUpdates from './components/RecentUpdates';

import { getMarkerIcon } from '@/services/mapConfig';
import { getAncientCityMapMarkersAdapter } from '@/utils/culturalElementAdapter';
import { history } from '@umijs/max';
import { Card, Col, Divider, Drawer, Row, Typography } from 'antd';
import * as echarts from 'echarts';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';

const { Title, Paragraph, Text } = Typography;

// 关联信息分组函数
const groupRelationsByType = (relations: any[]) => {
  const groups: { [key: string]: any[] } = {};
  relations.forEach((relation) => {
    // 优先使用relationName字段作为分组依据
    const relationName = relation.relationName || '其他关系';

    if (!groups[relationName]) {
      groups[relationName] = [];
    }
    groups[relationName].push(relation);
  });

  return groups;
};

// 获取要素类型名称 - 通用化处理
const getTypeName = (type?: string): string => {
  if (!type) return '未知类型';

  // 通用的类型名称映射
  const typeNameMap: Record<string, string> = {
    cultural_element: '文化要素',
    ancient_city: '古城',
    type_dict: '类型字典',
  };

  return typeNameMap[type] || type; // 如果没有映射，直接返回原类型名
};

// 获取要素类型颜色 - 通用化处理
const getTypeColor = (type?: string): string => {
  if (!type) return '#666';

  // 通用的类型颜色映射
  const typeColorMap: Record<string, string> = {
    cultural_element: '#1890ff', // 蓝色
    ancient_city: '#722ed1', // 紫色
    type_dict: '#52c41a', // 绿色
  };

  // 如果没有预定义颜色，根据类型名生成一个稳定的颜色
  if (typeColorMap[type]) {
    return typeColorMap[type];
  }

  // 基于字符串哈希生成颜色
  let hash = 0;
  for (let i = 0; i < type.length; i++) {
    hash = type.charCodeAt(i) + ((hash << 5) - hash);
  }
  const hue = Math.abs(hash) % 360;
  return `hsl(${hue}, 60%, 50%)`;
};

// 获取连线颜色
const getLinkColor = (relationName?: string): string => {
  switch (relationName) {
    case '地理关联':
      return '#52c41a';
    case '历史关联':
      return '#1890ff';
    case '文化关联':
      return '#722ed1';
    case '行政关联':
      return '#fa8c16';
    default:
      return '#999';
  }
};

// 转换关联数据为网络图数据 - 复用详情页的实现
const convertRelationsToNetworkData = (
  relations: API.ElementRelation[],
  currentElementInfo: any,
): API.NetworkGraphData => {
  const nodes: API.NetworkGraphNode[] = [];
  const links: API.NetworkGraphLink[] = [];
  const nodeMap = new Map<string, API.NetworkGraphNode>();

  // 获取当前要素信息
  const [currentType] = (currentElementInfo?.id || '').split('_');

  // 添加当前要素作为中心节点
  const currentNodeId = currentElementInfo?.id || '';
  const currentNode: API.NetworkGraphNode = {
    id: currentNodeId,
    name: currentElementInfo?.name || '当前要素',
    type: currentType || 'unknown',
    category: getTypeName(currentType),
    size: 20, // 中心节点较大
    color: getTypeColor(currentType),
  };
  nodes.push(currentNode);
  nodeMap.set(currentNodeId, currentNode);

  // 处理关联关系
  relations.forEach((relation) => {
    // 添加源要素节点
    const sourceId = `${relation.sourceType}_${relation.sourceId}`;
    if (!nodeMap.has(sourceId)) {
      const sourceNode: API.NetworkGraphNode = {
        id: sourceId,
        name: relation.sourceElement?.name || '未知要素',
        type: relation.sourceType || 'unknown',
        category: getTypeName(relation.sourceType),
        size: 15,
        color: getTypeColor(relation.sourceType),
      };
      nodes.push(sourceNode);
      nodeMap.set(sourceId, sourceNode);
    }

    // 添加目标要素节点
    const targetType = relation.targetEntityType || 'unknown';
    const targetId = `${targetType}_${relation.targetId}`;
    if (!nodeMap.has(targetId)) {
      const targetNode: API.NetworkGraphNode = {
        id: targetId,
        name: relation.targetElement?.name || '未知要素',
        type: targetType || 'unknown',
        category: getTypeName(targetType),
        size: 15,
        color: getTypeColor(targetType),
      };
      nodes.push(targetNode);
      nodeMap.set(targetId, targetNode);
    }

    // 添加连线
    const relationName = relation.relationDict?.relationName || '关联';
    const link: API.NetworkGraphLink = {
      source: sourceId,
      target: targetId,
      relation: relationName,
      relationName: relationName, // 后端API返回的关系名称字段，用于分组
      relationshipData: relation.relationDict, // 保留完整的关系数据，包含relationName
      direction:
        (relation.direction as 'unidirectional' | 'bidirectional') ||
        'unidirectional',
      term: relation.record || '暂无记载',
      weight: 1,
      color: getLinkColor(relationName),
    };
    links.push(link);
  });

  // 生成分类数据 - 转换为正确的格式
  const categoryNames = Array.from(new Set(nodes.map((node) => node.category)));
  const categories = categoryNames.map((name) => ({
    name: String(name),
    itemStyle: {
      color: getTypeColor(String(name)),
    },
  }));

  // 创建类型名称到索引的映射
  const categoryIndexMap = new Map<string, number>();
  categoryNames.forEach((name, index) => {
    categoryIndexMap.set(String(name), index);
  });

  // 转换节点数据为正确的格式
  const formattedNodes = nodes.map((node) => ({
    id: node.id,
    name: node.name,
    category: categoryIndexMap.get(String(node.category)) || 0, // 使用数字索引
    symbolSize: node.size || 15,
    itemStyle: {
      color: node.color || '#666',
    },
    label: {
      show: true,
    },
  }));

  // 转换连线数据为正确的格式
  const formattedLinks = links.map((link) => ({
    source: link.source,
    target: link.target,
    name: link.relation || '关联',
    lineStyle: {
      width: 2,
      curveness: 0.1,
      color: link.color || '#999',
    },
    label: {
      show: true,
      formatter: link.relation || '关联',
    },
    value: link.weight || 1,
  }));

  return {
    nodes: formattedNodes,
    links: formattedLinks,
    categories,
  };
};

const HomePage: React.FC = () => {
  const [culturalElementStatistics, setCulturalElementStatistics] = useState<{
    totalCulturalElements: number;
    ancientCityCount: number;
    totalRelationships: number;
  } | null>(null);
  const [markers, setMarkers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidePanelVisible, setSidePanelVisible] = useState(false);
  const [selectedElement, setSelectedElement] = useState<{
    id: number;
    name: string;
    type: string;
  } | null>(null);

  // 图表实例引用
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<echarts.ECharts | null>(null);

  // 统计面板容器引用
  const statisticsPanelRef = useRef<HTMLDivElement>(null);

  // 预留图表显示状态
  const [showExtendedChart, setShowExtendedChart] = useState(false);

  // 关联信息Drawer状态
  const [relationDrawerVisible, setRelationDrawerVisible] = useState(false);
  const [currentRelations, setCurrentRelations] = useState<any[]>([]);
  const [currentNetworkData, setCurrentNetworkData] = useState<any>(null);
  const [currentElementInfo, setCurrentElementInfo] = useState<{
    id: string;
    name: string;
    type: string;
  } | null>(null);

  useEffect(() => {
    // 添加全局处理函数
    (window as any).handleMarkerDetail = (
      markerId: string,
      type: string,
      encodedName: string,
    ) => {
      const name = decodeURIComponent(encodedName);
      console.log('🔍 查看详情点击:', { markerId, type, name });

      // 查找对应的标记点数据 (现在使用组合ID)
      const markerData = markers.find((m) => m.id === markerId);
      console.log('🔍 找到的标记点数据:', markerData);

      // 从组合ID中提取原始ID
      const originalId = markerId.split('_')[1];

      // 根据类型跳转到对应的详情页面
      let detailPath = '';
      switch (type) {
        case 'ancientCity':
          // 古城类型跳转到文化要素页面，并带上古城参数
          detailPath = `/cultural-element?ancientCityId=${originalId}&ancientCityName=${encodeURIComponent(
            name,
          )}`;
          break;
        case 'mountain':
        case 'waterSystem':
        case 'historicalElement':
        case 'culturalElement':
        default:
          // 统一跳转到文化要素详情页面
          detailPath = `/detail/culturalElement/${originalId}`;
          break;
      }

      console.log('🔗 跳转到详情页面:', detailPath);

      // 跳转到详情页面
      history.push(detailPath);
    };

    // 添加关联信息展开处理函数 - 支持古城关系网络图
    (window as any).expandRelations = (markerId: string) => {
      console.log('🔗 展开古城关系信息:', markerId);
      const [type] = markerId.split('_');

      // 从信息窗口中获取古城名称
      const titleElement = document.querySelector(
        `[data-marker-id="${markerId}"] h3`,
      );
      const elementName = titleElement?.textContent || '未知古城';

      // 从全局变量中获取网络图数据
      const networkData = (window as any).relationsData?.[markerId] || null;
      console.log('🔍 获取到的网络图数据:', networkData);

      setCurrentElementInfo({
        id: markerId,
        name: elementName,
        type: type,
      });

      // 设置网络图数据和关系列表数据
      if (type === 'ancientCity' && networkData) {
        setCurrentNetworkData(networkData);

        // 从网络图数据中提取关系信息用于关联信息列表
        // 适配新的API数据格式：{ source, target, relation, direction, term, weight, color }
        const relations: any[] = [];
        if (networkData.links && networkData.links.length > 0) {
          networkData.links.forEach((link: any) => {
            const sourceNode = networkData.nodes?.find(
              (n: any) => n.id === link.source,
            );
            const targetNode = networkData.nodes?.find(
              (n: any) => n.id === link.target,
            );

            if (sourceNode && targetNode) {
              // 从正确的路径获取关系类型名称：links[].relationshipData.relationName
              const groupName =
                link.relationshipData?.relationName ||
                link.relationName ||
                '其他关系';

              relations.push({
                relationName: groupName, // 使用relationshipData.relationName作为分组依据
                name: link.name,
                sourceElement: {
                  name: sourceNode.name,
                  type: sourceNode.type || 'ancientCity',
                },
                targetElement: {
                  name: targetNode.name,
                  type: targetNode.type || 'ancientCity',
                },
                record: link.relationshipData?.record || '暂无记载',
                direction:
                  (link.direction as 'unidirectional' | 'bidirectional') ||
                  'unidirectional',
              });
            }
          });
        }
        setCurrentRelations(relations);
      } else {
        setCurrentRelations(networkData || []);
        setCurrentNetworkData(null);
      }
      setRelationDrawerVisible(true);
    };

    // 添加关联信息加载函数 - 针对古城的关系网络图
    (window as any).loadRelations = async (markerId: string) => {
      console.log('🔄 开始加载古城关联信息:', markerId);

      const [type] = markerId.split('_');
      const loadingEl = document.getElementById(
        `relations-loading-${markerId}`,
      );
      const countEl = document.getElementById(`relations-count-${markerId}`);

      if (!loadingEl) {
        console.warn('❌ 未找到必要的DOM元素');
        return;
      }

      try {
        if (type === 'ancientCity') {
          // 对于古城，获取该古城的关系网络图数据
          // 优先从DOM中获取古城名称，因为markers数组可能还未加载完成
          const titleElement = document.querySelector(
            `[data-marker-id="${markerId}"] h3`,
          );
          let ancientCityName = titleElement?.textContent?.trim();

          // 如果DOM中没有找到，再尝试从markers数组中获取
          if (!ancientCityName) {
            const marker = markers.find((m) => m.id === markerId);
            ancientCityName = marker?.title || marker?.extData?.name;
          }

          // 使用新的关系网络图接口，按古城名称筛选
          const { getPublicNetworkGraphData } = await import(
            '@/services/relationship'
          );
          const responseData = await getPublicNetworkGraphData({
            ancientCityName,
            status: 1,
          });

          if (responseData.errCode === 0 && responseData.data) {
            const networkData = responseData.data;
            const relationCount = networkData.links?.length || 0;

            // 将网络图数据存储到全局变量中，供Drawer使用
            (window as any).relationsData = (window as any).relationsData || {};
            (window as any).relationsData[markerId] = networkData;

            loadingEl.style.display = 'none';

            if (relationCount === 0) {
              if (countEl) {
                countEl.textContent = '(暂无)';
                countEl.style.display = 'inline';
              }
              console.log('ℹ️ 无关系数据');
            } else {
              console.log(`✅ 找到 ${relationCount} 个关系连接`);
              if (countEl) {
                countEl.textContent = `(${relationCount}个关系)`;
                countEl.style.display = 'inline';
              }
            }
          } else {
            throw new Error(responseData.msg || '获取关系数据失败');
          }
        } else {
          // 兼容旧的文化要素类型（如果还有的话）
          console.log('⚠️ 不支持的标记点类型:', type);
          loadingEl.textContent = '不支持';
        }
      } catch (error) {
        console.error('❌ 加载关联信息异常:', error);
        loadingEl.style.display = 'none';
        if (countEl) {
          countEl.textContent = '(网络错误)';
          countEl.style.display = 'inline';
        }
      }
    };

    const load = async () => {
      setLoading(true);
      setError(null);

      try {
        // 从后端获取数据 - 使用古城地图标记点适配器和统一统计接口
        const [mkRes, ceRes] = await Promise.all([
          getAncientCityMapMarkersAdapter(),
          (async () => {
            try {
              const { getPublicCulturalElementStatistics } = await import(
                '@/services/culturalElement'
              );
              return await getPublicCulturalElementStatistics();
            } catch (error) {
              console.warn('关系统计API调用失败:', error);
              return { errCode: 1, data: null, msg: '获取失败' };
            }
          })(),
        ]);

        // 设置统计数据（从统一接口获取）
        if (ceRes.errCode === 0 && ceRes.data) {
          const data = ceRes.data as any; // 使用any类型处理API数据结构差异
          setCulturalElementStatistics({
            totalCulturalElements: data.summary.totalCulturalElements || 0,
            ancientCityCount: data.summary.typeCount || 0,
            totalRelationships: data.summary.totalRelationships || 0,
          });
        } else {
          console.error('❌ 统计数据获取失败:', ceRes);
          setCulturalElementStatistics({
            totalCulturalElements: 0,
            ancientCityCount: 0,
            totalRelationships: 0,
          });
        }

        // 设置地图标记点数据
        if (mkRes.errCode === 0 && mkRes.data) {
          console.log('✅ 获取到标记点数据:', mkRes.data.length, '个');

          // 过滤有效的标记点数据
          const validMarkers = mkRes.data.filter((m: any) => {
            const hasLng =
              m.longitude !== null &&
              m.longitude !== undefined &&
              !isNaN(Number(m.longitude));
            const hasLat =
              m.latitude !== null &&
              m.latitude !== undefined &&
              !isNaN(Number(m.latitude));
            return hasLng && hasLat;
          });

          // 转换为地图标记点格式
          const ms = validMarkers.map((m: any) => {
            const lng = Number(m.longitude);
            const lat = Number(m.latitude);

            // 预处理名称以避免模板字符串中的特殊字符问题
            const encodedName = encodeURIComponent(m.name);

            const markerData = {
              id: `${m.type}_${m.id}`, // 使用类型+ID作为唯一标识
              title: m.name,
              position: [lng, lat] as [number, number],
              // 不使用icon配置，改用自定义渲染和offset来解决点击区域问题
              extData: { type: m.type, ...m },
              content: `
                <div data-marker-id="${m.type}_${m.id}" style="
                  padding: 0;
                  min-width: 280px;
                  max-width: 320px;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  line-height: 1.4;
                  background: white;
                  border-radius: 8px;
                  overflow: hidden;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                ">                  
                  <div style="padding: 16px;">
                    <h3 style="
                      margin: 0 0 8px 0;
                      font-size: 18px;
                      font-weight: 600;
                      color: #333;
                    ">${m.name}</h3>
                    <p style="
                      margin: 0 0 6px 0;
                      font-size: 14px;
                      color: #666;
                    ">类型: ${
                      m.type === 'ancientCity' ? '古城' : '文化要素'
                    }</p>
                    <p style="
                      margin: 0 0 12px 0;
                      font-size: 14px;
                      color: #666;
                      line-height: 1.5;
                    ">${m.summary || '暂无描述'}</p>

                    <!-- 关联信息入口 -->
                    <div id="relations-entry-${m.type}_${m.id}" style="
                      margin: 0 0 12px 0;
                      padding: 8px 12px;
                      background: #f8f9fa;
                      border-radius: 4px;
                      border-left: 3px solid #1890ff;
                      cursor: pointer;
                      transition: background-color 0.2s;
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    " onclick="window.expandRelations('${m.type}_${m.id}')"
                       onmouseover="this.style.backgroundColor='#e6f7ff'"
                       onmouseout="this.style.backgroundColor='#f8f9fa'">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="
                          font-size: 13px;
                          font-weight: 500;
                          color: #333;
                        ">查看关系信息</span>
                        <span id="relations-count-${m.type}_${m.id}" style="
                          font-size: 12px;
                          color: #999;
                          display: none;
                        "></span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 4px;">
                        <span id="relations-loading-${m.type}_${m.id}" style="
                          font-size: 12px;
                          color: #999;
                        ">加载中...</span>
                        <svg style="width: 14px; height: 14px; color: #666;" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                      </div>
                    </div>

                    <button style="
                      background: #1890ff;
                      color: white;
                      border: none;
                      padding: 8px 16px;
                      border-radius: 4px;
                      font-size: 14px;
                      cursor: pointer;
                      width: 100%;
                      font-weight: 500;
                    " onclick="window.handleMarkerDetail('${m.type}_${
                m.id
              }', '${m.type}', '${encodedName}')">查看详情</button>
                  </div>
                </div>

              `,
            };

            return markerData;
          });

          console.log('🎯 成功处理标记点:', ms.length, '个');

          setMarkers(ms);
        } else {
          console.error('地图标记点数据获取失败:', mkRes);
          setMarkers([]);
          setError('地图数据加载失败');
        }
      } catch (e: any) {
        console.error('❌ 接口请求失败:', e?.message);
        console.error('错误详情:', e);
        setError(`接口请求失败: ${e?.message || '未知错误'}`);
        setMarkers([]);
      } finally {
        setLoading(false);
      }
    };
    load();

    // 清理函数
    return () => {
      delete (window as any).handleMarkerDetail;
      delete (window as any).expandRelations;
      delete (window as any).loadRelations;
    };
  }, []);

  // 初始化图表
  useEffect(() => {
    if (chartRef.current && culturalElementStatistics) {
      // 销毁之前的图表实例
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
      }

      // 创建新的图表实例
      chartInstanceRef.current = echarts.init(chartRef.current);

      const option = {
        title: {
          text: '数据分布统计',
          textStyle: {
            color: '#333',
            fontSize: 12,
            fontWeight: 'normal',
          },
          left: 'center',
          top: 5,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '15%',
          right: '10%',
          bottom: '20%',
          top: '20%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: ['古城遗址', '文化要素', '关系数据'],
          axisLabel: {
            color: '#666',
            fontSize: 10,
          },
          axisLine: {
            lineStyle: {
              color: '#e8e8e8',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#666',
            fontSize: 10,
          },
          axisLine: {
            lineStyle: {
              color: '#e8e8e8',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#f5f5f5',
            },
          },
        },
        series: [
          {
            data: [
              {
                value: culturalElementStatistics.ancientCityCount,
                itemStyle: { color: '#1890ff' },
              },
              {
                value: culturalElementStatistics.totalCulturalElements,
                itemStyle: { color: '#52c41a' },
              },
              {
                value: culturalElementStatistics.totalRelationships,
                itemStyle: { color: '#fa8c16' },
              },
            ],
            type: 'bar',
            barWidth: '50%',
            label: {
              show: true,
              position: 'top',
              color: '#333',
              fontSize: 10,
            },
          },
        ],
      };

      chartInstanceRef.current.setOption(option);

      // 监听窗口大小变化
      const handleResize = () => {
        chartInstanceRef.current?.resize();
      };
      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [culturalElementStatistics]);

  // 组件卸载时销毁图表
  useEffect(() => {
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
      }
    };
  }, []);

  // 简化的预留图表显示逻辑
  useEffect(() => {
    const calculateExtendedChart = () => {
      if (!statisticsPanelRef.current) return;

      const panel = statisticsPanelRef.current;
      const panelRect = panel.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      // 计算统计面板可用的最大高度（减去顶部banner等固定高度）
      const maxPanelHeight = viewportHeight - panelRect.top - 20; // 20px底部边距

      // 固定的统计卡片高度 + 主图表最大高度 + 间距
      const cardsHeight = 240; // 统计卡片固定高度
      const mainChartMaxHeight = 500; // 主图表最大高度
      const spacing = 32; // 间距（16px * 2）

      const requiredHeight = cardsHeight + mainChartMaxHeight + spacing;

      console.log('预留图表计算:', {
        viewportHeight,
        panelTop: panelRect.top,
        maxPanelHeight,
        requiredHeight,
        hasSpace: maxPanelHeight > requiredHeight,
      });

      // 如果容器高度大于 统计卡片高度 + 主图表最大高度 + 间距，则显示预留图表
      setShowExtendedChart(maxPanelHeight > requiredHeight);
    };

    // 初始计算
    calculateExtendedChart();

    // 监听窗口大小变化
    const handleResize = () => {
      setTimeout(calculateExtendedChart, 100); // 延迟执行，确保DOM更新完成
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [culturalElementStatistics]); // 当统计数据变化时重新计算

  // 自定义标记点渲染函数
  const renderMarker = (marker: any) => {
    // 统一使用古城图标，因为现在所有标记点都是古城
    const iconConfig = getMarkerIcon('ancientCity');
    const typeName = '古城';

    return `
      <div style="
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1;
      "
      onmouseover="this.style.transform='scale(1.1)'; this.style.zIndex='1000';"
      onmouseout="this.style.transform='scale(1)'; this.style.zIndex='1';"
      title="${marker.title} (${typeName})">
        <img src="${iconConfig.image}"
             style="
               width: 32px;
               height: 40px;
               object-fit: contain;
               filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
               transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
               margin-bottom: 2px;
             "
             alt="${typeName}" />
        <div style="
          background: rgba(255, 255, 255, 0.95);
          color: #333;
          font-size: 11px;
          font-weight: 500;
          padding: 2px 6px;
          border-radius: 8px;
          border: 1px solid rgba(74, 144, 226, 0.3);
          box-shadow: 0 1px 3px rgba(0,0,0,0.2);
          white-space: nowrap;
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: center;
          line-height: 1.2;
        ">${marker.title}</div>
      </div>
    `;
  };

  // 地图标记点点击事件处理
  const handleMarkerClick = (marker: any, e: any) => {
    console.log('标记点点击:', marker, e);

    // 延迟加载关联信息，确保信息窗口已经打开
    setTimeout(() => {
      const markerId = marker.id;
      console.log('🔄 触发关联信息加载:', markerId);

      if ((window as any).loadRelations) {
        (window as any).loadRelations(markerId);
      } else {
        console.warn('loadRelations函数未定义');
      }
    }, 300); // 给信息窗口一些时间来渲染
  };

  // 地图点击事件处理
  const handleMapClick = () => {
    // 手动关闭信息窗口（如果存在）
    try {
      if ((window as any).closeAllInfoWindows) {
        (window as any).closeAllInfoWindows();
        console.log('🔄 关闭信息窗口');
      }
    } catch (e) {
      console.log('关闭信息窗口失败:', e);
    }

    // 点击地图空白处时隐藏所有弹出的详情
    if (relationDrawerVisible) {
      setRelationDrawerVisible(false);
    }

    if (sidePanelVisible) {
      setSidePanelVisible(false);
      setSelectedElement(null);
    }
  };

  // 地图实例引用
  const mapInstanceRef = useRef<any>(null);

  // 地图创建完成回调
  const handleMapCreated = (mapInstance: any) => {
    console.log('地图创建完成:', mapInstance);
    mapInstanceRef.current = mapInstance;

    // 添加全局的信息窗口关闭方法
    (window as any).closeAllInfoWindows = () => {
      if (mapInstance) {
        // 尝试多种方法关闭信息窗口
        try {
          // 方法1：通过地图实例关闭所有信息窗口
          mapInstance.clearInfoWindow();
        } catch (e) {
          console.log('方法1失败:', e);
        }

        try {
          // 方法2：获取所有覆盖物并关闭信息窗口类型的
          const overlays = mapInstance.getAllOverlays();
          overlays.forEach((overlay: any) => {
            if (overlay.CLASS_NAME === 'AMap.InfoWindow' && overlay.close) {
              overlay.close();
            }
          });
        } catch (e) {
          console.log('方法2失败:', e);
        }
      }
    };
  };

  // 导航到详情页面
  const navigateToSection = (path: string) => {
    history.push(path);
  };

  // 统计数据 - 直接使用 /openapi/statistics/basic 接口的数据
  const statisticsData = [
    {
      title: '古城数量',
      value: culturalElementStatistics?.ancientCityCount || 0,
      suffix: '座',
      color: '#1890ff',
      icon: '🏛️',
    },
    {
      title: '文化要素数量',
      value: culturalElementStatistics?.totalCulturalElements || 0,
      suffix: '项',
      color: '#52c41a',
      icon: '📚',
    },
    {
      title: '关系数量',
      value: culturalElementStatistics?.totalRelationships || 0,
      suffix: '个',
      color: '#fa8c16',
      icon: '🔗',
    },
  ];

  return (
    <PublicLayout>
      <FullPageScroll>
        {/* 第一屏：正确的布局 - Banner在上，统计在左下，地图在右下 */}
        <div className="first-screen">
          {/* 顶部Banner */}
          <PageHeader
            title="关中历史城镇营建经验数据分析平台"
            description="探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承"
            backgroundType="hero"
            height={200}
          />

          {/* 下方主体内容区域 */}
          <div className="main-content">
            {/* 左下 - 统计数据面板 */}
            <div className="statistics-panel" ref={statisticsPanelRef}>
              {/* 统计卡片区域 */}
              <div className="statistics-cards">
                {statisticsData.map((stat, index) => (
                  <Card
                    key={index}
                    className="stat-card"
                    size="small"
                    style={{
                      borderLeft: `4px solid ${stat.color}`,
                      marginBottom: '12px',
                    }}
                  >
                    <div className="stat-card-content">
                      <div
                        className="stat-icon"
                        style={{
                          color: stat.color,
                          backgroundColor: `${stat.color}15`,
                        }}
                      >
                        {stat.icon}
                      </div>
                      <div className="stat-info">
                        <div
                          className="stat-value"
                          style={{ color: stat.color }}
                        >
                          {stat.value}
                          <span className="stat-suffix">{stat.suffix}</span>
                        </div>
                        <div className="stat-title">{stat.title}</div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* 图表区域 */}
              <div className="chart-container">
                <div
                  ref={chartRef}
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                />
              </div>

              {/* 最近更新记录区域 */}
              {showExtendedChart && (
                <div className="extended-chart-container">
                  <RecentUpdates limit={8} />
                </div>
              )}
            </div>

            {/* 右下 - 地图区域 */}
            <div className="map-section">
              <div className="map-container-wrapper">
                {/* 状态显示 */}
                {loading && (
                  <div className="map-status loading">
                    <div className="status-content">
                      <div className="status-icon">🔄</div>
                      <div className="status-text">正在加载地图数据...</div>
                    </div>
                  </div>
                )}
                {error && (
                  <div className="map-status error">
                    <div className="status-content">
                      <div className="status-icon">❌</div>
                      <div className="status-text">{error}</div>
                    </div>
                  </div>
                )}
                {!loading && !error && markers.length === 0 && (
                  <div className="map-status warning">
                    <div className="status-content">
                      <div className="status-icon">⚠️</div>
                      <div className="status-text">暂无地图数据</div>
                    </div>
                  </div>
                )}

                <div id="map-container" className="map-container">
                  <GaoDeMap
                    city="西安"
                    center={[108.2, 34.5]}
                    zoom={8}
                    activedZoom={18}
                    markers={markers}
                    enableInfoWindow={true}
                    enableCluster={false}
                    markerRender={renderMarker}
                    events={{
                      onClick: handleMapClick,
                      onMarkerClick: handleMarkerClick,
                    }}
                    onMapCreated={handleMapCreated}
                    style={{ width: '100%', height: '100%' }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 第二屏：页脚信息 */}
        <div className="footer-section">
          <div className="container">
            <Row gutter={[48, 32]}>
              <Col xs={24} md={8}>
                <div className="footer-brand">
                  <Title level={4} style={{ color: 'white', marginBottom: 16 }}>
                    关中历史城镇营建经验数据分析平台
                  </Title>
                  <Paragraph style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
                  </Paragraph>
                </div>
              </Col>
              <Col xs={24} md={8}>
                <div className="footer-links">
                  <Title level={5} style={{ color: 'white', marginBottom: 16 }}>
                    快速导航
                  </Title>
                  <div className="footer-nav">
                    <a onClick={() => navigateToSection('/mountain')}>
                      山塬地貌
                    </a>
                    <a onClick={() => navigateToSection('/water-system')}>
                      水系网络
                    </a>
                    <a onClick={() => navigateToSection('/historical-element')}>
                      历史要素
                    </a>
                    <a onClick={() => navigateToSection('/digital')}>数字化</a>
                  </div>
                </div>
              </Col>
              <Col xs={24} md={8}>
                <div className="footer-contact">
                  <Title level={5} style={{ color: 'white', marginBottom: 16 }}>
                    系统信息
                  </Title>
                  <div className="footer-info">
                    <p>基于现代Web技术构建</p>
                    <p>支持多终端访问</p>
                    <p>实时数据更新</p>
                  </div>
                </div>
              </Col>
            </Row>
            <Divider
              style={{
                borderColor: 'rgba(255, 255, 255, 0.2)',
                margin: '40px 0 20px',
              }}
            />
            <div className="footer-bottom">
              <Paragraph
                style={{
                  color: 'rgba(255, 255, 255, 0.6)',
                  textAlign: 'center',
                  margin: 0,
                }}
              >
                © 2024 关中地区关中历史城镇营建经验数据分析平台. All rights
                reserved.
              </Paragraph>
            </div>
          </div>
        </div>
      </FullPageScroll>

      {/* 关联信息Drawer */}
      <Drawer
        title={`${currentElementInfo?.name || '要素'} - 关联信息`}
        placement="right"
        width={600}
        open={relationDrawerVisible}
        onClose={() => setRelationDrawerVisible(false)}
        styles={{
          body: { padding: 0 },
        }}
      >
        <div
          style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        >
          {/* 上半部分：关联信息列表 */}
          <div
            style={{
              flex: '1 1 50%',
              padding: '16px',
              borderBottom: '1px solid #f0f0f0',
            }}
          >
            <Title level={5} style={{ marginBottom: 16 }}>
              关联信息列表
            </Title>
            {currentRelations.length === 0 ? (
              <div
                style={{
                  textAlign: 'center',
                  color: '#999',
                  padding: '40px 0',
                }}
              >
                暂无关联信息
              </div>
            ) : (
              <div>
                {Object.entries(groupRelationsByType(currentRelations)).map(
                  ([relationName, relations]) => (
                    <div key={relationName} style={{ marginBottom: 24 }}>
                      <div
                        style={{
                          padding: '8px 12px',
                          background: '#f0f6ff',
                          borderRadius: '6px',
                          borderLeft: '3px solid #1890ff',
                          marginBottom: 12,
                        }}
                      >
                        <Text strong style={{ color: '#1890ff', fontSize: 14 }}>
                          {relationName} ({relations.length})
                        </Text>
                      </div>
                      <div style={{ paddingLeft: 8 }}>
                        {relations.map((relation: any, index: number) => {
                          const sourceElement = relation.sourceElement;
                          const targetElement = relation.targetElement;

                          return (
                            <div
                              key={index}
                              style={{
                                padding: '12px 16px',
                                marginBottom: 8,
                                background: '#fafafa',
                                borderRadius: '8px',
                                border: '1px solid #f0f0f0',
                                transition: 'all 0.2s',
                                cursor: 'pointer',
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.background = '#f0f6ff';
                                e.currentTarget.style.borderColor = '#d6e4ff';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.background = '#fafafa';
                                e.currentTarget.style.borderColor = '#f0f0f0';
                              }}
                            >
                              {/* 关系方向显示 */}
                              <div
                                style={{
                                  fontSize: 14,
                                  fontWeight: 500,
                                  color: '#333',
                                  marginBottom: 8,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 8,
                                  flexWrap: 'wrap',
                                }}
                              >
                                <span style={{ color: '#1890ff' }}>
                                  {sourceElement?.name || '未知要素'}
                                </span>
                                <div
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 4,
                                  }}
                                >
                                  <span
                                    style={{
                                      color: '#666',
                                      fontSize: 16,
                                      fontWeight: 'bold',
                                    }}
                                  >
                                    →
                                  </span>
                                  {/* 显示方向信息（name字段） */}
                                  {relation.directionName && (
                                    <span
                                      style={{
                                        color: '#ff7a00',
                                        fontSize: 12,
                                        fontWeight: 500,
                                        background: '#fff7e6',
                                        padding: '2px 6px',
                                        borderRadius: '4px',
                                        border: '1px solid #ffd591',
                                      }}
                                    >
                                      {relation.directionName}
                                    </span>
                                  )}
                                </div>
                                <span style={{ color: '#52c41a' }}>
                                  {targetElement?.name || '未知要素'}
                                </span>
                              </div>

                              {relation.term && (
                                <div
                                  style={{
                                    color: '#666',
                                    fontSize: 13,
                                    marginBottom: 6,
                                    padding: '4px 8px',
                                    background: '#f9f9f9',
                                    borderRadius: '4px',
                                    borderLeft: '2px solid #d9d9d9',
                                  }}
                                >
                                  <Text strong style={{ color: '#595959' }}>
                                    词条：
                                  </Text>
                                  {relation.term}
                                </div>
                              )}

                              {relation.name && (
                                <div
                                  style={{
                                    color: '#666',
                                    fontSize: 13,
                                    padding: '4px 8px',
                                    background: '#f9f9f9',
                                    borderRadius: '4px',
                                    borderLeft: '2px solid #d9d9d9',
                                    lineHeight: 1.5,
                                  }}
                                >
                                  <Text strong style={{ color: '#595959' }}>
                                    方向：
                                  </Text>
                                  {relation.name}
                                </div>
                              )}

                              {relation.record && (
                                <div
                                  style={{
                                    color: '#666',
                                    fontSize: 13,
                                    padding: '4px 8px',
                                    background: '#f9f9f9',
                                    borderRadius: '4px',
                                    borderLeft: '2px solid #d9d9d9',
                                    lineHeight: 1.5,
                                  }}
                                >
                                  <Text strong style={{ color: '#595959' }}>
                                    记载：
                                  </Text>
                                  {relation.record}
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ),
                )}
              </div>
            )}
          </div>

          {/* 下半部分：网络关系图 */}
          <div style={{ flex: '1 1 50%', padding: '16px' }}>
            <Title level={5} style={{ marginBottom: 16 }}>
              关系网络图
            </Title>
            {currentNetworkData ? (
              // 直接显示网络图数据（用于古城）
              <div
                style={{
                  height: '300px',
                  border: '1px solid #f0f0f0',
                  borderRadius: 4,
                }}
              >
                <NetworkGraph
                  data={currentNetworkData}
                  height={300}
                  title=""
                  style={{ border: 'none' }}
                />
              </div>
            ) : currentRelations.length === 0 ? (
              <div
                style={{
                  textAlign: 'center',
                  color: '#999',
                  padding: '40px 0',
                }}
              >
                暂无关系数据
              </div>
            ) : (
              // 转换关系数据为网络图（用于文化要素）
              <div
                style={{
                  height: '300px',
                  border: '1px solid #f0f0f0',
                  borderRadius: 4,
                }}
              >
                <NetworkGraph
                  data={convertRelationsToNetworkData(
                    currentRelations,
                    currentElementInfo,
                  )}
                  height={300}
                  title=""
                  style={{ border: 'none' }}
                />
              </div>
            )}
          </div>
        </div>
      </Drawer>

      {/* 关联信息侧滑面板 */}
      <RelationSidePanel
        visible={sidePanelVisible}
        elementData={selectedElement}
        onClose={() => {
          setSidePanelVisible(false);
          setSelectedElement(null);
        }}
      />
    </PublicLayout>
  );
};

export default HomePage;
