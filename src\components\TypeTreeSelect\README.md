# TypeTreeSelect 类型树选择组件

专门用于类型字典的树形选择组件，支持层级结构选择。

## 功能特性

- ✅ **层级结构支持**：支持父子级类型的树形展示和选择
- ✅ **单选/多选模式**：支持单选和多选两种模式
- ✅ **搜索功能**：支持按类型名称搜索
- ✅ **状态过滤**：可选择只显示启用状态的类型
- ✅ **自动缓存**：基于 dva 自动缓存类型字典数据
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **TypeScript 支持**：完整的类型定义

## 基础用法

### 单选模式

```tsx
import TypeTreeSelect from '@/components/TypeTreeSelect';

// 基础单选
<TypeTreeSelect
  value={typeId}
  onChange={(value) => setTypeId(value)}
  placeholder="请选择类型"
/>

// 在表单中使用
<Form.Item
  label="类型"
  name="typeDictId"
  rules={[{ required: true, message: '请选择类型' }]}
>
  <TypeTreeSelect placeholder="请选择类型" />
</Form.Item>
```

### 多选模式

```tsx
// 多选模式
<TypeTreeSelect
  multiple
  multipleValue={typeIds}
  onMultipleChange={(values) => setTypeIds(values)}
  placeholder="请选择多个类型"
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 当前选中的类型 ID（单选） | `number` | - |
| onChange | 选择变化回调（单选） | `(value?: number) => void` | - |
| multipleValue | 当前选中的类型 ID 数组（多选） | `number[]` | - |
| onMultipleChange | 选择变化回调（多选） | `(values?: number[]) => void` | - |
| placeholder | 占位符文本 | `string` | `'请选择类型'` |
| allowClear | 是否允许清空 | `boolean` | `true` |
| showSearch | 是否显示搜索框 | `boolean` | `true` |
| disabled | 是否禁用 | `boolean` | `false` |
| style | 自定义样式 | `React.CSSProperties` | - |
| onlyEnabled | 是否只显示启用的类型 | `boolean` | `true` |
| excludeId | 排除的类型 ID | `number` | - |
| treeDefaultExpandAll | 是否默认展开所有节点 | `boolean` | `false` |
| multiple | 是否支持多选 | `boolean` | `false` |

## 使用场景

### 1. 文化要素类型选择

```tsx
// 在文化要素编辑页面
<Form.Item
  label="类型"
  name="typeDictId"
  rules={[{ required: true, message: '请选择类型' }]}
>
  <TypeTreeSelect placeholder="请选择类型" treeDefaultExpandAll={true} />
</Form.Item>
```

### 2. 搜索筛选

```tsx
// 在列表页面的搜索表单中
<Form.Item label="类型" name="typeDictId">
  <TypeTreeSelect placeholder="请选择类型进行筛选" allowClear />
</Form.Item>
```

### 3. 批量操作

```tsx
// 批量选择类型
<TypeTreeSelect
  multiple
  multipleValue={selectedTypes}
  onMultipleChange={setSelectedTypes}
  placeholder="请选择要操作的类型"
  maxTagCount="responsive"
/>
```

### 4. 类型管理（排除自身）

```tsx
// 在类型字典编辑时，排除自身避免循环引用
<Form.Item label="父级类型" name="parentId">
  <TypeTreeSelect
    placeholder="请选择父级类型"
    excludeId={editingTypeId}
    allowClear
  />
</Form.Item>
```

## 数据结构

组件依赖的类型字典数据结构：

```typescript
interface TypeDict {
  id: number;
  typeCode: string;
  typeName: string;
  parentId?: number | null;
  status: number; // 0=禁用，1=启用
  sort: number;
  typeDesc?: string;
  createdAt?: string;
  updatedAt?: string;
  children?: TypeDict[];
}
```

## 样式定制

组件提供了完整的样式类名，可以通过 CSS 进行定制：

```less
.type-tree-select {
  // 自定义选择器样式

  .ant-select-tree {
    // 自定义树形结构样式
  }

  .ant-select-selection-item {
    // 自定义多选标签样式
  }
}
```

## 注意事项

1. **数据依赖**：组件依赖 dva 中的 dictionary 模型，确保已正确配置
2. **权限控制**：组件会自动过滤禁用状态的类型（可通过 onlyEnabled 控制）
3. **性能优化**：大量数据时建议设置 treeDefaultExpandAll 为 false
4. **搜索功能**：搜索是基于类型名称的模糊匹配
5. **多选模式**：多选时建议设置 maxTagCount 避免标签过多

## 相关组件

- `DictSelect` - 通用字典选择组件
- `AncientCitySelector` - 古城选择组件
- `AncientCityListSelector` - 古城列表选择组件
