import '@umijs/max/typings';

declare global {
  /** 综合基础路由与 umi 扩展路由 */
  type IBestAFSRoute = {
    component?: string | undefined;
    layout?: false | undefined;
    path?: string | undefined;
    redirect?: string | undefined;
    routes?: IBestAFSRoute[];
    wrappers?: Array<string> | undefined;
    name?: string;
    keepQuery?: boolean;
    wrappers?: Array<string>;
    /** 菜单图标 */
    icon?: string;
    /** 新页面打开 */
    target?: '_blank';
    /** 不展示顶栏 */
    headerRender?: boolean;
    /** 不展示页脚 */
    footerRender?: boolean;
    /** 不展示菜单 */
    menuRender?: boolean;
    /** 不展示菜单顶栏 */
    menuHeaderRender?: boolean;
    /** 权限配置，需要与 plugin-access 插件配合使用 */
    access?: string;
    /** 隐藏子菜单 */
    hideChildrenInMenu?: boolean;
    /** 隐藏自己和子菜单 */
    hideInMenu?: boolean;
    /** 在面包屑中隐藏 */
    hideInBreadcrumb?: boolean;
    /** 子项往上提，仍旧展示 */
    flatMenu?: boolean;
  };

  /** yeald语法参数定义 */
  type Call = <T>(
    fn: (...args: any[]) => Promise<T>,
    ...args: any[]
  ) => Promise<T>;

  /** yeald语法参数定义 */
  type Put = <A extends { type: string }>(action: A) => { type: string };

  interface Window {
    AMap: any;
  }

  // 高德地图配置类型声明
  declare const AMAP_CONFIG: {
    /** 高德地图 API Key */
    key: string;
    /** 高德地图安全密钥 */
    securityJsCode: string;
    /** API 版本 */
    version: string;
    /** 默认插件列表 */
    plugins: string[];
    /** 默认地图配置 */
    defaultMapOptions: {
      zoom: number;
      center: [number, number];
      mapStyle: string;
      features: string[];
      showLabel: boolean;
    };
    /** 标记点图标配置 */
    markerIcons: {
      ancientCity: string;
      culturalElement: string;
      default: string;
    };
  };

  type DictType = 'type' | 'relation';
}
