.type-tree-select {
  .ant-select-tree {
    .ant-select-tree-node-content-wrapper {
      &:hover {
        background-color: #f5f5f5;
      }
    }

    .ant-select-tree-node-selected {
      .ant-select-tree-node-content-wrapper {
        background-color: #e6f7ff;
      }
    }

    .ant-select-tree-treenode-disabled {
      .ant-select-tree-node-content-wrapper {
        color: #bfbfbf;
        cursor: not-allowed;
      }
    }
  }

  // 多选标签样式
  .ant-select-selection-item {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }

  // 搜索框样式
  .ant-select-selection-search-input {
    &::placeholder {
      color: #bfbfbf;
    }
  }
}

// 下拉面板样式
.ant-select-dropdown {
  .ant-select-tree {
    .ant-select-tree-treenode {
      padding: 2px 0;

      .ant-select-tree-node-content-wrapper {
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s;

        &:hover {
          background-color: #f5f5f5;
        }
      }

      &.ant-select-tree-treenode-selected {
        .ant-select-tree-node-content-wrapper {
          background-color: #e6f7ff;
          color: #1890ff;
        }
      }

      &.ant-select-tree-treenode-disabled {
        .ant-select-tree-node-content-wrapper {
          color: #bfbfbf;
          cursor: not-allowed;

          &:hover {
            background-color: transparent;
          }
        }
      }
    }

    // 展开/收起图标样式
    .ant-select-tree-switcher {
      color: #8c8c8c;

      &:hover {
        color: #1890ff;
      }
    }

    // 复选框样式（多选模式）
    .ant-select-tree-checkbox {
      margin-right: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .type-tree-select {
    .ant-select-selection-item {
      font-size: 12px;
      padding: 0 4px;
    }
  }
}
