/**
 * @file 文化要素数据管理Hook
 * @description 管理文化要素列表数据的获取、分页、统计等功能
 * <AUTHOR> Assistant
 * @date 2025-10-09
 */

import {
  getCulturalElementList,
  getCulturalElementStatistics,
} from '@/services/culturalElement';
import { message } from 'antd';
import { useCallback, useState } from 'react';

export interface UseCulturalElementDataReturn {
  // 数据状态
  data: API.CulturalElement[];
  statistics: API.CulturalElementStatistics | null;
  loading: boolean;
  statisticsLoading: boolean;

  // 分页状态
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };

  // 操作方法
  fetchData: (
    page?: number,
    pageSize?: number,
    keyword?: string,
    typeDictId?: number,
    ancientCityId?: number,
    constructionYear?: [number, number],
  ) => Promise<void>;
  fetchStatistics: () => Promise<void>;
  setPagination: React.Dispatch<
    React.SetStateAction<{
      current: number;
      pageSize: number;
      total: number;
    }>
  >;
}

/**
 * 文化要素数据管理Hook
 */
const useCulturalElementData = (): UseCulturalElementDataReturn => {
  // 数据状态
  const [data, setData] = useState<API.CulturalElement[]>([]);
  const [statistics, setStatistics] =
    useState<API.CulturalElementStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [statisticsLoading, setStatisticsLoading] = useState(false);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 获取列表数据
  const fetchData = useCallback(
    async (
      page = 1,
      pageSize = 20,
      keyword?: string,
      typeDictId?: number,
      ancientCityId?: number,
      constructionYear?: [number, number],
    ) => {
      setLoading(true);
      try {
        const params: API.CulturalElementQueryDTO = {
          page,
          pageSize,
          keyword,
          typeDictId,
          ancientCityId,
        };

        // 处理建造年份范围
        if (constructionYear && constructionYear.length === 2) {
          params.constructionYearStart = constructionYear[0];
          params.constructionYearEnd = constructionYear[1];
        }

        const response = await getCulturalElementList(params);

        if (response.errCode === 0 && response.data) {
          setData(response.data.list || []);
          setPagination((prev) => ({
            ...prev,
            current: page,
            pageSize,
            total: response.data!.total || 0,
          }));
        } else {
          message.error(response.msg || '获取数据失败');
          setData([]);
          setPagination((prev) => ({
            ...prev,
            current: page,
            pageSize,
            total: 0,
          }));
        }
      } catch (error: any) {
        console.error('获取文化要素列表失败:', error);
        message.error(error?.message || '获取数据失败');
        setData([]);
        setPagination((prev) => ({
          ...prev,
          current: page,
          pageSize,
          total: 0,
        }));
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取统计数据
  const fetchStatistics = useCallback(async () => {
    setStatisticsLoading(true);
    try {
      const response = await getCulturalElementStatistics();

      if (response.errCode === 0 && response.data) {
        setStatistics(response.data);
      } else {
        message.warning(response.msg || '获取统计数据失败');
        console.warn('获取统计数据失败:', response.msg);
        setStatistics(null);
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error);
      setStatistics(null);
    } finally {
      setStatisticsLoading(false);
    }
  }, []);

  return {
    // 数据状态
    data,
    statistics,
    loading,
    statisticsLoading,

    // 分页状态
    pagination,

    // 操作方法
    fetchData,
    fetchStatistics,
    setPagination,
  };
};

export default useCulturalElementData;
