/**
 * @file 数字化统计数据管理Hook
 * @description 管理数字化统计页面的数据加载、缓存和状态
 * <AUTHOR> Assistant
 * @date 2025-09-09
 */

import {
  type BasicStatisticsData,
  type OverviewStatisticsData,
  type TimelineStatisticsData,
} from '@/services/portal';
import { getPublicNetworkGraphData } from '@/services/relationship';
import {
  getBasicStatisticsAdapter,
  getComprehensiveStatisticsAdapter,
  getOverviewStatisticsAdapter,
  getTimelineStatisticsAdapter,
} from '@/utils/culturalElementAdapter';
import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';

export interface UseDigitalStatisticsParams {
  startTime?: string;
  endTime?: string;
  autoLoad?: boolean;
}

export interface UseDigitalStatisticsReturn {
  // 数据状态
  basicData: BasicStatisticsData | null;
  overviewData: OverviewStatisticsData | null;
  timelineData: TimelineStatisticsData[] | null;
  comprehensiveData: any | null;
  networkData: API.NetworkGraphData | null;

  // 加载状态
  loading: {
    basic: boolean;
    overview: boolean;
    timeline: boolean;
    comprehensive: boolean;
    network: boolean;
  };

  // 错误状态
  error: {
    basic: string | null;
    overview: string | null;
    timeline: string | null;
    comprehensive: string | null;
    network: string | null;
  };

  // 操作方法
  loadBasicData: () => Promise<void>;
  loadOverviewData: () => Promise<void>;
  loadTimelineData: () => Promise<void>;
  loadComprehensiveData: () => Promise<void>;
  loadNetworkData: () => Promise<void>;
  loadAllData: () => Promise<void>;
  refreshData: () => Promise<void>;
  clearData: () => void;
}

/**
 * 数字化统计数据管理Hook
 */
export const useDigitalStatistics = (
  params: UseDigitalStatisticsParams = {},
): UseDigitalStatisticsReturn => {
  const { startTime, endTime, autoLoad = true } = params;

  // 数据状态
  const [basicData, setBasicData] = useState<BasicStatisticsData | null>(null);
  const [overviewData, setOverviewData] =
    useState<OverviewStatisticsData | null>(null);
  const [timelineData, setTimelineData] = useState<
    TimelineStatisticsData[] | null
  >(null);
  const [comprehensiveData, setComprehensiveData] = useState<any | null>(null);
  const [networkData, setNetworkData] = useState<API.NetworkGraphData | null>(
    null,
  );

  // 加载状态
  const [loading, setLoading] = useState({
    basic: false,
    overview: false,
    timeline: false,
    comprehensive: false,
    network: false,
  });

  // 错误状态
  const [error, setError] = useState({
    basic: null as string | null,
    overview: null as string | null,
    timeline: null as string | null,
    comprehensive: null as string | null,
    network: null as string | null,
  });

  // 更新加载状态
  const updateLoading = useCallback(
    (key: keyof typeof loading, value: boolean) => {
      setLoading((prev) => ({ ...prev, [key]: value }));
    },
    [],
  );

  // 更新错误状态
  const updateError = useCallback(
    (key: keyof typeof error, value: string | null) => {
      setError((prev) => ({ ...prev, [key]: value }));
    },
    [],
  );

  // 加载基础统计数据
  const loadBasicData = useCallback(async () => {
    updateLoading('basic', true);
    updateError('basic', null);
    try {
      // 使用适配器获取基础统计数据，包含区域分布和时间轴数据
      const response = await getBasicStatisticsAdapter({
        startTime,
        endTime,
      });
      if (response.errCode === 0) {
        setBasicData(response.data || null);
        // 基础统计数据中包含时间轴数据，直接设置
        if (response.data?.timelineData) {
          setTimelineData(response.data.timelineData);
        }
      } else {
        const errorMsg = response.msg || '获取基础统计数据失败';
        updateError('basic', errorMsg);
        message.error(errorMsg);
      }
    } catch (err: any) {
      const errorMsg = err?.message || '获取基础统计数据失败';
      updateError('basic', errorMsg);
      message.error(errorMsg);
      console.error('获取基础统计数据失败:', err);
    } finally {
      updateLoading('basic', false);
    }
  }, [startTime, endTime, updateLoading, updateError]);

  // 加载概览数据
  const loadOverviewData = useCallback(async () => {
    updateLoading('overview', true);
    updateError('overview', null);
    try {
      // 使用适配器获取概览数据
      const response = await getOverviewStatisticsAdapter({});
      if (response.errCode === 0) {
        setOverviewData(response.data || null);
      } else {
        const errorMsg = response.msg || '获取概览数据失败';
        updateError('overview', errorMsg);
        message.error(errorMsg);
      }
    } catch (err: any) {
      const errorMsg = err?.message || '获取概览数据失败';
      updateError('overview', errorMsg);
      message.error(errorMsg);
      console.error('获取概览数据失败:', err);
    } finally {
      updateLoading('overview', false);
    }
  }, [updateLoading, updateError]);

  // 移除区域分布数据加载函数

  // 加载时间轴数据（独立接口，可选）
  const loadTimelineData = useCallback(async () => {
    updateLoading('timeline', true);
    updateError('timeline', null);
    try {
      // 使用适配器获取时间轴数据
      const response = await getTimelineStatisticsAdapter({});
      if (response.errCode === 0) {
        setTimelineData(response.data || null);
      } else {
        const errorMsg = response.msg || '获取时间轴数据失败';
        updateError('timeline', errorMsg);
        // 时间轴数据失败不显示错误消息，因为基础数据中已包含
        console.warn(errorMsg);
      }
    } catch (err: any) {
      const errorMsg = err?.message || '获取时间轴数据失败';
      updateError('timeline', errorMsg);
      console.warn('获取时间轴数据失败:', err);
    } finally {
      updateLoading('timeline', false);
    }
  }, [updateLoading, updateError]);

  // 加载综合统计数据
  const loadComprehensiveData = useCallback(async () => {
    updateLoading('comprehensive', true);
    updateError('comprehensive', null);
    try {
      // 使用适配器获取综合统计数据
      const response = await getComprehensiveStatisticsAdapter({});
      if (response.errCode === 0) {
        setComprehensiveData(response.data || null);
      } else {
        const errorMsg = response.msg || '获取综合统计数据失败';
        updateError('comprehensive', errorMsg);
        message.error(errorMsg);
      }
    } catch (err: any) {
      const errorMsg = err?.message || '获取综合统计数据失败';
      updateError('comprehensive', errorMsg);
      message.error(errorMsg);
      console.error('获取综合统计数据失败:', err);
    } finally {
      updateLoading('comprehensive', false);
    }
  }, [updateLoading, updateError]);

  // 加载网络图数据
  const loadNetworkData = useCallback(async () => {
    updateLoading('network', true);
    updateError('network', null);
    try {
      const response = await getPublicNetworkGraphData({
        status: 1, // 只获取启用状态的关系
      });
      if (response.errCode === 0) {
        setNetworkData(response.data || null);
      } else {
        // 如果API返回错误，使用模拟数据进行演示
        console.warn('网络图API返回错误，使用模拟数据:', response.msg);
        const mockData: API.NetworkGraphData = {
          nodes: [
            {
              id: 'mountain_1',
              name: '骊山',
              type: 'mountain',
              category: '山塬',
              size: 20,
              color: '#8B4513',
            },
            {
              id: 'water_system_1',
              name: '渭河',
              type: 'water_system',
              category: '水系',
              size: 18,
              color: '#4169E1',
            },
            {
              id: 'historical_element_1',
              name: '兵马俑',
              type: 'historical_element',
              category: '历史要素',
              size: 16,
              color: '#DC143C',
            },
            {
              id: 'mountain_2',
              name: '华山',
              type: 'mountain',
              category: '山塬',
              size: 18,
              color: '#8B4513',
            },
            {
              id: 'water_system_2',
              name: '灞河',
              type: 'water_system',
              category: '水系',
              size: 15,
              color: '#4169E1',
            },
          ],
          links: [
            {
              source: 'mountain_1',
              target: 'water_system_1',
              relation: '地理关联',
              direction: '双向',
              weight: 1,
              color: '#4169E1',
            },
            {
              source: 'mountain_1',
              target: 'historical_element_1',
              relation: '历史关联',
              direction: '单向',
              weight: 1,
              color: '#DC143C',
            },
            {
              source: 'mountain_2',
              target: 'water_system_2',
              relation: '地理关联',
              direction: '双向',
              weight: 1,
              color: '#4169E1',
            },
            {
              source: 'historical_element_1',
              target: 'water_system_1',
              relation: '空间关联',
              direction: '单向',
              weight: 1,
              color: '#32CD32',
            },
          ],
          categories: ['山塬', '水系', '历史要素'],
        };
        setNetworkData(mockData);
      }
    } catch (err: any) {
      // 如果API调用失败，也使用模拟数据
      console.warn('网络图API调用失败，使用模拟数据:', err);
      const mockData: API.NetworkGraphData = {
        nodes: [
          {
            id: 'mountain_1',
            name: '骊山',
            type: 'mountain',
            category: '山塬',
            size: 20,
            color: '#8B4513',
          },
          {
            id: 'water_system_1',
            name: '渭河',
            type: 'water_system',
            category: '水系',
            size: 18,
            color: '#4169E1',
          },
          {
            id: 'historical_element_1',
            name: '兵马俑',
            type: 'historical_element',
            category: '历史要素',
            size: 16,
            color: '#DC143C',
          },
        ],
        links: [
          {
            source: 'mountain_1',
            target: 'water_system_1',
            relation: '地理关联',
            direction: '双向',
            weight: 1,
            color: '#4169E1',
          },
          {
            source: 'mountain_1',
            target: 'historical_element_1',
            relation: '历史关联',
            direction: '单向',
            weight: 1,
            color: '#DC143C',
          },
        ],
        categories: ['山塬', '水系', '历史要素'],
      };
      setNetworkData(mockData);
    } finally {
      updateLoading('network', false);
    }
  }, [updateLoading, updateError]);

  // 加载所有数据
  const loadAllData = useCallback(async () => {
    // 优先加载基础数据（包含时间轴数据），然后并行加载其他数据
    await loadBasicData();
    await Promise.all([
      loadOverviewData(),
      loadComprehensiveData(),
      loadNetworkData(),
    ]);
  }, [loadBasicData, loadOverviewData, loadComprehensiveData, loadNetworkData]);

  // 刷新数据
  const refreshData = useCallback(async () => {
    await loadAllData();
  }, [loadAllData]);

  // 清空数据
  const clearData = useCallback(() => {
    setBasicData(null);
    setOverviewData(null);
    setTimelineData(null);
    setComprehensiveData(null);
    setNetworkData(null);
    setError({
      basic: null,
      overview: null,
      timeline: null,
      comprehensive: null,
      network: null,
    });
  }, []);

  // 自动加载数据
  useEffect(() => {
    if (autoLoad) {
      loadAllData();
    }
  }, [autoLoad, loadAllData]);

  return {
    // 数据状态
    basicData,
    overviewData,
    timelineData,
    comprehensiveData,
    networkData,

    // 加载状态
    loading,

    // 错误状态
    error,

    // 操作方法
    loadBasicData,
    loadOverviewData,
    loadTimelineData,
    loadComprehensiveData,
    loadNetworkData,
    loadAllData,
    refreshData,
    clearData,
  };
};

export default useDigitalStatistics;
