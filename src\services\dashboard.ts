import { request } from '@umijs/max';

// 管理端仪表盘服务

export interface DashboardOverviewData {
  statistics: {
    ancientCity: number;
    culturalElement: number;
    relationship: number;
    dict: number;
  };
  distribution: {
    byType: Array<{
      typeName: string;
      count: number;
      percentage: number;
    }>;
    byRegion: Array<{
      regionName: string;
      count: number;
      percentage: number;
    }>;
  };
  recentData: {
    culturalElements: Array<{
      id: number;
      name?: string;
      typeName?: string;
      typeDictId?: number;
      ancientCityName?: string;
      createdAt: string;
    }>;
    relationships: Array<{
      id: number;
      relationDict?: {
        relationName: string;
      };
      sourceElement?: {
        type: string;
        name: string;
        cityName?: string;
      };
      targetElement?: {
        type: string;
        name: string;
      };
      createdAt: string;
    }>;
  };
}

export interface DashboardStatisticsData {
  basic: {
    counts: {
      mountain: number;
      waterSystem: number;
      historicalElement: number;
    };
    regionStats: Array<{
      region: string;
      regionId: number;
      mountainCount: number;
      waterSystemCount: number;
      historicalElementCount: number;
      total: number;
    }>;
    timelineData: Array<{
      year: number;
      count: number;
      elements: Array<{ id: number; name: string; type: string }>;
    }>;
  };
  detailed: {
    mountains: {
      total: number;
      byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    };
    waterSystems: {
      total: number;
      totalLength: number;
      byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    };
    historicalElements: {
      total: number;
      byType: Array<{ typeId: number; typeName: string; count: number }>;
      byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    };
  };
  summary: {
    totalEntities: number;
    regionCoverage: number;
    timeSpan: { earliest: number; latest: number; span: number };
  };
}

export interface DashboardDataQualityData {
  completeness: {
    culturalElement: number;
    relationship: number;
  };
  missingFields: {
    culturalElement: Array<{
      fieldName: string;
      fieldCode: string;
      count: number;
      percentage: number;
    }>;
    relationship: Array<{
      fieldName: string;
      fieldCode: string;
      count: number;
      percentage: number;
    }>;
  };
  dataDistribution: {
    byAncientCity: Array<{
      name: string;
      count: number;
      percentage: number;
    }>;
    byType: Array<{
      name: string;
      count: number;
      percentage: number;
    }>;
  };
}

export async function getDashboardOverview(params?: {
  regionId?: number;
}): Promise<API.ResType<DashboardOverviewData>> {
  const queryParams = new URLSearchParams();
  if (params?.regionId) queryParams.append('regionId', String(params.regionId));
  const url = `/admin/dashboard/overview${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getDashboardStatistics(params?: {
  regionId?: number;
  startTime?: string;
  endTime?: string;
}): Promise<API.ResType<DashboardStatisticsData>> {
  const queryParams = new URLSearchParams();
  if (params?.regionId) queryParams.append('regionId', String(params.regionId));
  if (params?.startTime) queryParams.append('startTime', params.startTime);
  if (params?.endTime) queryParams.append('endTime', params.endTime);
  const url = `/admin/dashboard/statistics${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getDashboardDataQuality(): Promise<
  API.ResType<DashboardDataQualityData>
> {
  return request('/admin/dashboard/data-quality', { method: 'GET' });
}

// 系统状态接口
export interface SystemStatusData {
  version: string;
  environment: string;
  uptime: number;
  memory: {
    process: {
      heapUsed: number;
      heapTotal: number;
      external: number;
    };
    system: {
      total: number;
      free: number;
      used: number;
    };
    rss: number;
  };
  timestamp: string;
}

export async function getSystemStatus(): Promise<
  API.ResType<SystemStatusData>
> {
  return request('/admin/system/status', { method: 'GET' });
}

// 数据验证接口
export interface DataValidationResult {
  isValid: boolean;
  issues: string[];
}

export async function validateSystemData(): Promise<
  API.ResType<DataValidationResult>
> {
  return request('/admin/system/data/validate', { method: 'GET' });
}

// 数据修复接口
export interface DataRepairResult {
  fixedIssues: string[];
}

export async function repairSystemData(): Promise<
  API.ResType<DataRepairResult>
> {
  return request('/admin/system/data/repair', { method: 'POST' });
}

// 导出缺失数据接口
export interface ExportMissingDataParams {
  missingField?: string;
  type?: 'culturalElement' | 'relationship';
}

export async function exportMissingData(
  params: ExportMissingDataParams,
): Promise<Blob> {
  return request('/admin/cultural-element/export', {
    method: 'POST',
    data: params,
    responseType: 'blob',
  });
}
