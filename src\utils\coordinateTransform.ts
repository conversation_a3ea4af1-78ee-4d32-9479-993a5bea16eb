/**
 * @file 坐标系转换工具
 * @description 提供WGS-84与GCJ-02坐标系之间的转换功能
 * <AUTHOR> Assistant
 * @date 2025-10-16
 */

/**
 * 坐标系类型
 */
export type CoordinateSystem = 'WGS84' | 'GCJ02' | 'BD09';

/**
 * 坐标点接口
 */
export interface Coordinate {
  longitude: number;
  latitude: number;
}

/**
 * 使用高德地图API进行坐标转换
 * @param coordinates 要转换的坐标数组
 * @param from 源坐标系
 * @param to 目标坐标系
 * @returns Promise<Coordinate[]> 转换后的坐标数组
 */
export const convertCoordinatesWithAMap = async (
  coordinates: Coordinate[],
  from: CoordinateSystem,
  to: CoordinateSystem,
): Promise<Coordinate[]> => {
  // 确保高德地图API已加载
  const AMap = (window as any).AMap;
  if (!AMap) {
    throw new Error('高德地图API未加载');
  }

  // 如果源坐标系和目标坐标系相同，直接返回
  if (from === to) {
    return coordinates;
  }

  return new Promise((resolve, reject) => {
    // 创建坐标转换实例
    AMap.convertFrom(
      coordinates.map((coord) => [coord.longitude, coord.latitude]),
      from,
      (status: string, result: any) => {
        if (status === 'complete' && result.locations) {
          const convertedCoords = result.locations.map((location: any) => ({
            longitude: location.lng,
            latitude: location.lat,
          }));
          resolve(convertedCoords);
        } else {
          reject(new Error('坐标转换失败'));
        }
      },
    );
  });
};

/**
 * 数学算法坐标转换（不依赖高德API）
 * 基于开源算法实现WGS84与GCJ02之间的转换
 */

const PI = 3.1415926535897932384626;
const A = 6378245.0;
const EE = 0.00669342162296594323;

/**
 * 判断是否在中国境内
 */
function outOfChina(lng: number, lat: number): boolean {
  return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271;
}

/**
 * 转换纬度
 */
function transformLat(lng: number, lat: number): number {
  let ret =
    -100.0 +
    2.0 * lng +
    3.0 * lat +
    0.2 * lat * lat +
    0.1 * lng * lat +
    0.2 * Math.sqrt(Math.abs(lng));
  ret +=
    ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) /
    3.0;
  ret +=
    ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) *
      2.0) /
    3.0;
  return ret;
}

/**
 * 转换经度
 */
function transformLng(lng: number, lat: number): number {
  let ret =
    300.0 +
    lng +
    2.0 * lat +
    0.1 * lng * lng +
    0.1 * lng * lat +
    0.1 * Math.sqrt(Math.abs(lng));
  ret +=
    ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) /
    3.0;
  ret +=
    ((150.0 * Math.sin((lng / 12.0) * PI) +
      300.0 * Math.sin((lng / 30.0) * PI)) *
      2.0) /
    3.0;
  return ret;
}

/**
 * WGS84转GCJ02
 * @param wgsCoord WGS84坐标
 * @returns GCJ02坐标
 */
export function wgs84ToGcj02(wgsCoord: Coordinate): Coordinate {
  const { longitude: lng, latitude: lat } = wgsCoord;

  if (outOfChina(lng, lat)) {
    return { longitude: lng, latitude: lat };
  }

  let dLat = transformLat(lng - 105.0, lat - 35.0);
  let dLng = transformLng(lng - 105.0, lat - 35.0);

  const radLat = (lat / 180.0) * PI;
  let magic = Math.sin(radLat);
  magic = 1 - EE * magic * magic;
  const sqrtMagic = Math.sqrt(magic);

  dLat = (dLat * 180.0) / (((A * (1 - EE)) / (magic * sqrtMagic)) * PI);
  dLng = (dLng * 180.0) / ((A / sqrtMagic) * Math.cos(radLat) * PI);

  return {
    longitude: lng + dLng,
    latitude: lat + dLat,
  };
}

/**
 * GCJ02转WGS84
 * @param gcjCoord GCJ02坐标
 * @returns WGS84坐标
 */
export function gcj02ToWgs84(gcjCoord: Coordinate): Coordinate {
  const { longitude: lng, latitude: lat } = gcjCoord;

  if (outOfChina(lng, lat)) {
    return { longitude: lng, latitude: lat };
  }

  let dLat = transformLat(lng - 105.0, lat - 35.0);
  let dLng = transformLng(lng - 105.0, lat - 35.0);

  const radLat = (lat / 180.0) * PI;
  let magic = Math.sin(radLat);
  magic = 1 - EE * magic * magic;
  const sqrtMagic = Math.sqrt(magic);

  dLat = (dLat * 180.0) / (((A * (1 - EE)) / (magic * sqrtMagic)) * PI);
  dLng = (dLng * 180.0) / ((A / sqrtMagic) * Math.cos(radLat) * PI);

  return {
    longitude: lng - dLng,
    latitude: lat - dLat,
  };
}

/**
 * 批量坐标转换
 * @param coordinates 坐标数组
 * @param from 源坐标系
 * @param to 目标坐标系
 * @returns 转换后的坐标数组
 */
export function batchConvertCoordinates(
  coordinates: Coordinate[],
  from: CoordinateSystem,
  to: CoordinateSystem,
): Coordinate[] {
  if (from === to) {
    return coordinates;
  }

  if (from === 'WGS84' && to === 'GCJ02') {
    return coordinates.map(wgs84ToGcj02);
  }

  if (from === 'GCJ02' && to === 'WGS84') {
    return coordinates.map(gcj02ToWgs84);
  }

  throw new Error(`不支持的坐标系转换: ${from} -> ${to}`);
}

/**
 * 坐标系配置
 */
export interface CoordinateSystemConfig {
  /** 数据库存储的坐标系 */
  storage: CoordinateSystem;
  /** 地图显示使用的坐标系 */
  display: CoordinateSystem;
  /** 是否启用自动转换 */
  autoConvert: boolean;
}

/**
 * 默认坐标系配置
 */
export const DEFAULT_COORDINATE_CONFIG: CoordinateSystemConfig = {
  storage: 'GCJ02', // 当前数据库存储的是GCJ02
  display: 'GCJ02', // 高德地图使用GCJ02
  autoConvert: false, // 默认不启用转换
};

/**
 * 根据配置转换坐标
 * @param coordinate 原始坐标
 * @param config 坐标系配置
 * @param direction 转换方向：'storage-to-display' | 'display-to-storage'
 * @returns 转换后的坐标
 */
export function convertCoordinateByConfig(
  coordinate: Coordinate,
  config: CoordinateSystemConfig,
  direction: 'storage-to-display' | 'display-to-storage',
): Coordinate {
  if (!config.autoConvert) {
    return coordinate;
  }

  if (direction === 'storage-to-display') {
    return batchConvertCoordinates(
      [coordinate],
      config.storage,
      config.display,
    )[0];
  } else {
    return batchConvertCoordinates(
      [coordinate],
      config.display,
      config.storage,
    )[0];
  }
}
