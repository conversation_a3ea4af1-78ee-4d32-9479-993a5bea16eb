.photo-gallery {
  .photo-gallery-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .batch-toolbar {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f5f5f5;
    border-radius: 6px;
    border: 1px solid #d9d9d9;

    .batch-controls {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-wrap: wrap;

      .ant-checkbox-wrapper {
        font-weight: 500;
      }
    }
  }

  .photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;

    .photo-item {
      position: relative;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 15%);
      }

      &.primary {
        border: 2px solid #faad14;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(250, 173, 20, 30%);
      }

      &.selected {
        border: 2px solid #1890ff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 30%);
      }

      .photo-wrapper {
        position: relative;
        width: 100%;
        height: 150px;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #d9d9d9;
        background: #f5f5f5;

        .photo-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .batch-checkbox {
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 3;
          background: rgba(255, 255, 255, 90%);
          border-radius: 4px;
          padding: 4px;
        }

        .primary-badge {
          position: absolute;
          top: 8px;
          left: 8px;
          background: rgba(0, 0, 0, 70%);
          color: #faad14;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
          z-index: 2;

          span {
            color: #fff;
          }
        }

        .photo-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 50%);
          display: flex;
          justify-content: center;
          align-items: center;
          opacity: 0;
          transition: opacity 0.3s ease;

          .ant-btn {
            color: white;
            border-color: white;

            &:hover {
              background: rgba(255, 255, 255, 20%);
              border-color: #fff;
              color: #fff;
            }

            &.ant-btn-dangerous:hover {
              background: rgba(255, 77, 79, 80%);
              border-color: #ff4d4f;
              color: #fff;
            }
          }
        }

        &:hover {
          .photo-image {
            transform: scale(1.05);
          }

          .photo-overlay {
            opacity: 1;
          }
        }
      }

      .photo-name {
        margin-top: 8px;
        text-align: center;
        font-size: 12px;
        color: #666;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &.upload-item {
        .upload-wrapper {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 150px;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          background: #fafafa;
          cursor: pointer;
          transition: all 0.3s ease;

          .anticon {
            font-size: 24px;
            color: #999;
            margin-bottom: 8px;
          }

          div {
            color: #999;
            font-size: 14px;
          }

          &:hover {
            border-color: #1890ff;
            background: #f0f8ff;

            .anticon,
            div {
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .photo-gallery {
    .photo-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 12px;

      .photo-item .photo-wrapper {
        height: 120px;
      }

      .photo-item.upload-item .upload-wrapper {
        height: 120px;
      }
    }
  }
}
