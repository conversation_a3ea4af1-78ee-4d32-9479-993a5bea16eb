/**
 * 古城选择器组件
 * 用于替换原有的区域树选择器，现在通过古城字典管理区域信息
 */
import { useDispatch, useSelector } from '@umijs/max';
import { Select, Spin } from 'antd';
import React, { useEffect } from 'react';

// 古城字典状态接口
interface AncientCityState {
  ancientCityList: API.AncientCityDict[];
  loading: boolean;
}

export interface AncientCitySelectorProps {
  /** 当前选中的古城ID */
  value?: number;
  /** 古城变化回调 */
  onChange?: (cityId?: number, cityInfo?: API.AncientCityDict) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否允许清空 */
  allowClear?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 是否显示区域信息 */
  showRegion?: boolean;
}

const AncientCitySelector: React.FC<AncientCitySelectorProps> = ({
  value,
  onChange,
  placeholder = '请选择古城',
  allowClear = true,
  disabled = false,
  style,
  className,
  showRegion = true,
}) => {
  const dispatch = useDispatch();
  const ancientCity = useSelector(
    (state: any) => state.ancientCity,
  ) as AncientCityState;

  const cities = ancientCity?.ancientCityList || [];
  const loading = ancientCity?.loading || false;

  // 初始化加载数据
  useEffect(() => {
    if (!cities || cities.length === 0) {
      dispatch({ type: 'ancientCity/fetchAncientCityList' });
    }
  }, [dispatch, cities]);

  const handleChange = (cityId?: number) => {
    const cityInfo = cities.find(
      (city: API.AncientCityDict) => city.id === cityId,
    );
    onChange?.(cityId, cityInfo);
  };

  const options = cities.map((city: API.AncientCityDict) => ({
    label:
      showRegion && city.region
        ? `${city.cityName} (${city.region})`
        : city.cityName,
    value: city.id,
  }));

  return (
    <Select
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      loading={loading}
      style={style}
      className={className}
      options={options}
      showSearch
      filterOption={(input, option) => {
        if (!option) return false;
        const searchText = input.toLowerCase();
        const label = option.label as string;
        return label.toLowerCase().includes(searchText);
      }}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
    />
  );
};

export default AncientCitySelector;
