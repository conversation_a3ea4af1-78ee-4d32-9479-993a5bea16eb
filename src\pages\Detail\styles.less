// 详情页样式
.detail-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 0;

  // Banner区域样式
  .banner-section {
    position: relative;
    height: 400px;
    overflow: hidden;

    .banner-image {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      position: relative;

      // 如果没有图片，使用渐变背景
      background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .banner-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(102, 126, 234, 70%) 0%,
        rgba(118, 75, 162, 70%) 100%
      );
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .banner-content {
      max-width: 1400px;
      width: 100%;
      padding: 0 32px;
      position: relative;

      .back-button {
        position: absolute;
        top: 32px;
        left: 32px;
        z-index: 10;
        margin-bottom: 0;
        border-radius: 12px;
        border: 2px solid rgba(255, 255, 255, 80%);
        background: rgba(255, 255, 255, 90%);
        backdrop-filter: blur(10px);
        color: #667eea !important;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 10%);
        height: 40px;
        padding: 0 20px;
        display: flex;
        align-items: center;

        &:hover,
        &:focus {
          border-color: #667eea !important;
          background: #fff !important;
          color: #667eea !important;
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(102, 126, 234, 30%);
        }

        .anticon {
          margin-right: 8px;
          font-size: 14px;
          color: #667eea;
        }

        span {
          color: #667eea;
        }
      }

      .banner-title-section {
        text-align: center;

        .page-title {
          color: #fff !important;
          font-size: 48px !important;
          font-weight: 800 !important;
          margin: 0 0 16px !important;
          text-shadow: 0 4px 8px rgba(0, 0, 0, 30%);
          letter-spacing: 2px;
          line-height: 1.2;

          @media (max-width: 768px) {
            font-size: 32px !important;
            letter-spacing: 1px;
          }

          @media (max-width: 480px) {
            font-size: 24px !important;
            letter-spacing: 0.5px;
          }
        }

        .page-code {
          .code-tag {
            background: rgba(255, 255, 255, 90%);
            color: #667eea;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 20%);
            backdrop-filter: blur(10px);

            @media (max-width: 768px) {
              font-size: 14px;
              padding: 6px 16px;
            }
          }
        }
      }
    }

    // 响应式设计
    @media (max-width: 768px) {
      height: 300px;

      .banner-content {
        padding: 0 20px;

        .back-button {
          top: 20px;
          left: 20px;
          height: 36px;
          padding: 0 16px;
          font-size: 14px;
        }
      }
    }

    @media (max-width: 480px) {
      height: 250px;

      .banner-content {
        padding: 0 16px;

        .back-button {
          top: 16px;
          left: 16px;
          height: 32px;
          padding: 0 12px;
          font-size: 12px;
        }
      }
    }
  }

  .content-card {
    background: rgba(255, 255, 255, 95%);
    backdrop-filter: blur(10px);
    border-radius: 30px 30px 0 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 10%);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 20%);
    position: relative;
    z-index: 5;
    animation: fade-in-up 0.8s ease-out;
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.content-card {
  background: rgba(255, 255, 255, 95%);
  backdrop-filter: blur(10px);
  border-radius: 30px 30px 0 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 10%);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 20%);
  position: relative;
  z-index: 5;
  animation: fade-in-up 0.8s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-layout {
  display: grid;
  grid-template-columns: 1fr 1fr 320px;
  gap: 28px;
  align-items: start;
  margin-top: 32px;

  // 网络图始终占满整行
  .network-graph {
    grid-column: 1 / -1;
    margin-top: 16px;
  }

  @media (max-width: 1200px) {
    grid-template-columns: 1fr 300px;
    gap: 24px;

    .photos-section {
      grid-column: 1 / -1;
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 24px;
  }
}

// 关联信息样式
.relation-info {
  // 设置最大高度和滚动条
  .ant-card-body {
    max-height: 600px;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .relation-group {
    &:not(:last-child) {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(240, 240, 240, 60%);
    }
  }

  .relation-type-tag {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    border-radius: 8px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 30%);
  }

  .relation-item {
    margin-bottom: 12px;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 90%) 0%,
      rgba(255, 255, 255, 70%) 100%
    );
    position: relative;
    overflow: hidden;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid rgba(240, 240, 240, 80%);
    transition: all 0.2s ease;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    &:hover {
      background: linear-gradient(
        135deg,
        rgba(102, 126, 234, 10%) 0%,
        rgba(118, 75, 162, 10%) 100%
      );
      border-color: #667eea;
      transform: translateX(8px);
      box-shadow: 0 4px 20px rgba(102, 126, 234, 20%);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .relation-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .relation-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .relation-flow {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 15px;
  }

  .element {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;

    &.current {
      background: rgba(102, 126, 234, 10%);
      color: #667eea;
    }

    &.other {
      background: rgba(0, 0, 0, 5%);
      color: #333;
    }
  }

  .arrow {
    color: #667eea;
    font-weight: bold;
    font-size: 18px;
    margin: 0 4px;
  }

  .relation-type {
    display: flex;
    gap: 6px;
    align-items: center;

    .ant-tag {
      margin: 0;
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
    }
  }

  .relation-record {
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(255, 248, 220, 60%);
    border-radius: 6px;
    border-left: 3px solid #ffa940;
    color: #666;
    font-size: 13px;
    line-height: 1.5;
    display: flex;
    align-items: flex-start;
    gap: 6px;
  }
}

// 网络图样式
.network-graph {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 5%) 0%,
      rgba(118, 75, 162, 5%) 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  .ant-card-body {
    padding: 20px;
    position: relative;
    z-index: 2;
  }

  // 确保网络图容器有合适的高度
  .echarts-for-react {
    min-height: 500px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 10%);
  }
}

// 基本信息样式
.basic-info {
  .info-item {
    display: flex;
    margin-bottom: 16px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(240, 240, 240, 50%);
    transition: all 0.2s ease;

    &:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }

    &:hover {
      background: rgba(102, 126, 234, 5%);
      border-radius: 8px;
      padding: 12px 16px;
      margin: 0 -16px 16px;
    }
  }

  .info-label {
    min-width: 90px;
    color: #667eea;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 2px;
      margin-right: 8px;
    }
  }

  .info-value {
    flex: 1;
    color: #333;
    font-size: 14px;
    word-break: break-all;
    line-height: 1.6;

    &.historical-records {
      margin-top: 8px;
      padding: 16px 20px;
      background: linear-gradient(
        135deg,
        rgba(102, 126, 234, 10%) 0%,
        rgba(118, 75, 162, 10%) 100%
      );
      border-radius: 12px;
      border-left: 4px solid #667eea;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 10%);
      position: relative;

      &::before {
        content: '"';
        position: absolute;
        top: 8px;
        left: 12px;
        font-size: 24px;
        color: #667eea;
        opacity: 0.3;
      }
    }
  }
}

// 图片展示样式
.photos-section {
  .photo-item {
    margin-bottom: 16px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 10%);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 15%);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .ant-image {
      border-radius: 12px;
      overflow: hidden;
    }
  }
}

// 返回按钮样式
.back-button {
  margin-bottom: 24px;
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 80%);
  background: rgba(255, 255, 255, 90%);
  backdrop-filter: blur(10px);
  color: #667eea !important;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 10%);
  height: 40px;
  padding: 0 20px;
  display: flex;
  align-items: center;

  &:hover,
  &:focus {
    border-color: #667eea !important;
    background: #fff !important;
    color: #667eea !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 30%);
  }

  .anticon {
    margin-right: 8px;
    font-size: 14px;
    color: #667eea;
  }

  span {
    color: #667eea;
  }
}

// 标题样式
.page-title {
  margin-bottom: 16px;
  text-align: center;
  color: #fff;
  font-size: 32px;
  font-weight: 700;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 40%);
  letter-spacing: 1px;
  background: linear-gradient(135deg, #fff 0%, rgba(255, 255, 255, 90%) 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 30%));
}

.page-code {
  text-align: center;
  margin-bottom: 32px;

  .code-tag {
    padding: 8px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 40%);
    border: 2px solid rgba(255, 255, 255, 20%);
    backdrop-filter: blur(10px);
  }
}

// 卡片通用样式
.detail-card {
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 8%);
  border: 1px solid rgba(255, 255, 255, 30%);
  height: 100%;
  background: rgba(255, 255, 255, 95%);
  backdrop-filter: blur(15px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // 为卡片添加进入动画
  animation: fade-in-up 0.6s ease-out;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.3s;
  }

  &:nth-child(4) {
    animation-delay: 0.4s;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 60%),
      transparent
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 15%);
    border-color: rgba(255, 255, 255, 50%);

    &::before {
      opacity: 1;
    }
  }

  .ant-card-head {
    border-bottom: 1px solid rgba(240, 240, 240, 40%);
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding: 24px 28px 18px;
    position: relative;
  }

  .ant-card-body {
    padding: 28px;
  }

  &.small-card .ant-card-head {
    font-size: 16px;
    padding: 20px 24px 16px;
  }

  &.small-card .ant-card-body {
    padding: 24px;
  }

  // 网络图卡片特殊样式
  &.network-graph {
    border-radius: 24px;

    .ant-card-head {
      font-size: 20px;
      padding: 28px 32px 20px;
    }

    .ant-card-body {
      padding: 32px;
    }
  }
}

// 空状态样式
.empty-state {
  margin: 40px 0;
  padding: 40px 20px;
  text-align: center;

  .ant-empty-description {
    color: #999;
    font-size: 16px;
  }

  .ant-empty-image {
    opacity: 0.6;
  }
}

// 动画效果
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式优化
@media (max-width: 768px) {
  .detail-page {
    padding: 16px;

    .page-title {
      font-size: 24px;
    }

    .detail-card {
      .ant-card-body {
        padding: 16px;
      }
    }

    .basic-info {
      .info-item {
        flex-direction: column;

        .info-label {
          margin-bottom: 4px;
          min-width: auto;
        }
      }
    }
  }
}
