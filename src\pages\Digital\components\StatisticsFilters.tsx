/**
 * @file 统计筛选器组件
 * @description 数字化统计页面的筛选器组件
 * <AUTHOR> Assistant
 * @date 2025-09-09
 */

import { useDropdownFix } from '@/components/DropdownFix';
import { ReloadOutlined } from '@ant-design/icons';
import { But<PERSON>, Card, DatePicker, Space } from 'antd';
import type { Dayjs } from 'dayjs';
import React from 'react';

export interface StatisticsFiltersProps {
  dateRange: [Dayjs, Dayjs] | null;
  loading?: boolean;
  onDateRangeChange: (dates: [Dayjs | null, Dayjs | null] | null) => void;
  onRefresh: () => void;
}

/**
 * 统计筛选器组件
 */
export const StatisticsFilters: React.FC<StatisticsFiltersProps> = ({
  dateRange,
  loading = false,
  onDateRangeChange,
  onRefresh,
}) => {
  // 修复下拉框定位问题
  useDropdownFix();

  return (
    <Card style={{ marginBottom: 24 }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          gap: 16,
        }}
      >
        <Space size="large" wrap>
          {/* 移除区域筛选器 */}

          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 8, fontWeight: 'bold', minWidth: 80 }}>
              时间范围：
            </span>
            <DatePicker.RangePicker
              value={dateRange}
              onChange={onDateRangeChange}
              placeholder={['开始时间', '结束时间']}
              style={{ width: 300 }}
              allowClear
              getPopupContainer={(triggerNode) => {
                // 查找最近的Card容器作为定位参考
                let parent = triggerNode?.parentElement;
                while (parent) {
                  if (
                    parent.classList.contains('ant-card') ||
                    parent.classList.contains('content-card')
                  ) {
                    return parent;
                  }
                  parent = parent.parentElement;
                }
                return document.body;
              }}
            />
          </div>
        </Space>

        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>
    </Card>
  );
};

export default StatisticsFilters;
