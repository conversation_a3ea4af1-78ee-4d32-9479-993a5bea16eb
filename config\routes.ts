export default [
  // 公共网站路由（不使用layout）
  {
    path: '/',
    component: '@/pages/Home',
    layout: false,
  },
  {
    path: '/cultural-element',
    component: '@/pages/CulturalElement',
    layout: false,
  },
  {
    path: '/digital',
    component: '@/pages/Digital',
    layout: false,
  },
  {
    path: '/detail/:type/:id',
    component: '@/pages/Detail',
    layout: false,
  },
  // 管理端登录页（不使用layout）
  {
    path: '/admin/login',
    component: '@/pages/Admin/Login',
    layout: false,
  },
  // 403无权限页面
  {
    path: '/403',
    component: '@/pages/403',
    layout: false,
  },
  // 管理端路由（使用layout）
  {
    path: '/admin',
    access: 'isLoggedIn',
    flatMenu: true,
    routes: [
      {
        path: '/admin',
        redirect: '/admin/dashboard',
      },
      {
        path: '/admin/dashboard',
        name: '仪表盘',
        icon: 'DashboardOutlined',
        component: '@/pages/Admin/Dashboard',
        access: 'canView',
      },
      // 古城管理 - 基础数据，放在文化要素管理前面
      {
        path: '/admin/ancient-city',
        name: '古城管理',
        icon: 'HomeOutlined',
        component: '@/pages/Admin/AncientCity',
        access: 'canManageDictionary',
      },
      // 新的统一文化要素管理
      {
        path: '/admin/cultural-element',
        name: '文化要素管理',
        icon: 'AppstoreOutlined',
        access: 'canEdit',
        routes: [
          {
            path: '/admin/cultural-element',
            component: '@/pages/Admin/CulturalElement',
            exact: true,
          },
          {
            path: '/admin/cultural-element/create',
            component: '@/pages/Admin/CulturalElement/Edit',
            hideInMenu: true,
          },
          {
            path: '/admin/cultural-element/edit/:id',
            component: '@/pages/Admin/CulturalElement/Edit',
            hideInMenu: true,
          },
          {
            path: '/admin/cultural-element/detail/:id',
            component: '@/pages/Admin/CulturalElement/Detail',
            hideInMenu: true,
          },
        ],
      },

      {
        path: '/admin/element-relation',
        name: '要素关联管理',
        icon: 'ShareAltOutlined',
        component: '@/pages/Admin/ElementRelation',
        access: 'canEdit',
      },
      {
        path: '/admin/dictionary',
        name: '字典管理',
        icon: 'BookOutlined',
        component: '@/pages/Admin/Dictionary',
        access: 'canManageDictionary',
      },
      {
        path: '/admin/upload',
        name: '资源管理',
        icon: 'CloudUploadOutlined',
        component: '@/pages/Admin/Upload',
        access: 'canManageResources',
      },
      {
        path: '/admin/user',
        name: '用户管理',
        icon: 'UserOutlined',
        component: '@/pages/Admin/User',
        access: 'canManageUsers',
      },
    ],
  },
] as IBestAFSRoute[];
