import { Breadcrumb, Card, Col, Row, Space, Typography } from 'antd';
import React from 'react';
import './index.less';

const { Title, Text } = Typography;

export interface FormLayoutProps {
  title: string;
  subtitle?: string;
  breadcrumb?: Array<{
    title: string;
    path?: string;
  }>;
  actions?: React.ReactNode;
  children: React.ReactNode;
  loading?: boolean;
}

const FormLayout: React.FC<FormLayoutProps> = ({
  title,
  subtitle,
  breadcrumb,
  actions,
  children,
}) => {
  return (
    <div className="form-layout">
      {/* 面包屑导航 */}
      {breadcrumb && breadcrumb.length > 0 && (
        <Card size="small" className="breadcrumb-card">
          <Breadcrumb>
            {breadcrumb.map((item, index) => (
              <Breadcrumb.Item key={index}>
                {item.path ? <a href={item.path}>{item.title}</a> : item.title}
              </Breadcrumb.Item>
            ))}
          </Breadcrumb>
        </Card>
      )}

      {/* 页面头部 */}
      <Card className="header-card">
        <Row justify="space-between" align="middle">
          <Col>
            <Space direction="vertical" size={4}>
              <Title level={2} style={{ margin: 0 }}>
                {title}
              </Title>
              {subtitle && <Text type="secondary">{subtitle}</Text>}
            </Space>
          </Col>
          {actions && <Col>{actions}</Col>}
        </Row>
      </Card>

      {/* 表单内容 */}
      <div className="form-content">{children}</div>
    </div>
  );
};

export default FormLayout;
