import { getRecentUpdates, RecentUpdatesData } from '@/services/portal';
import { ClockCircleOutlined, FileTextOutlined } from '@ant-design/icons';
import { Card, Empty, List, Spin, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import React, { useEffect, useState } from 'react';

// 配置dayjs
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { Text } = Typography;

interface RecentUpdatesProps {
  limit?: number;
}

const RecentUpdates: React.FC<RecentUpdatesProps> = ({ limit = 10 }) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<RecentUpdatesData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getRecentUpdates({
        limit,
        type: 'culturalElement', // 只获取文化要素
      });

      if (response.errCode === 0 && response.data) {
        setData(response.data);
      } else {
        throw new Error(response.msg || '获取数据失败');
      }
    } catch (err: any) {
      console.error('获取最近更新记录失败:', err);
      setError(err.message || '获取数据失败');
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [limit]);

  // 格式化时间
  const formatTime = (timeStr: string) => {
    return dayjs(timeStr).fromNow();
  };

  // 渲染文化要素项
  const renderCulturalElement = (item: any) => (
    <List.Item
      key={`cultural-${item.id}`}
      style={{
        margin: '8px 5px',
        borderRadius: '8px',
        border: '1px solid #f0f0f0',
        backgroundColor: '#fafafa',
        transition: 'all 0.2s ease',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.04)',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f0f8ff';
        e.currentTarget.style.borderColor = '#d9d9d9';
        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#fafafa';
        e.currentTarget.style.borderColor = '#f0f0f0';
        e.currentTarget.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.04)';
      }}
    >
      <List.Item.Meta
        avatar={
          <div
            style={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: '#e6f7ff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <FileTextOutlined style={{ color: '#1890ff', fontSize: 16 }} />
          </div>
        }
        title={
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 10,
              marginBottom: 4,
            }}
          >
            <Text strong style={{ fontSize: 15, color: '#262626' }}>
              {item.name}
            </Text>
            <Tag color="blue" style={{ fontSize: 12, padding: '2px 8px' }}>
              {item.typeName}
            </Tag>
            {item.cityName && (
              <Tag color="green" style={{ fontSize: 12, padding: '2px 8px' }}>
                {item.cityName}
              </Tag>
            )}
          </div>
        }
        description={
          <div style={{ marginTop: 8 }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 12,
                flexWrap: 'wrap',
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <ClockCircleOutlined style={{ color: '#999', fontSize: 12 }} />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {formatTime(item.updatedAt)}
                </Text>
              </div>
              {item.code && (
                <Text
                  type="secondary"
                  style={{ fontSize: 12, color: '#8c8c8c' }}
                >
                  编号: {item.code}
                </Text>
              )}
            </div>
          </div>
        }
        style={{
          padding: '16px 20px',
        }}
      />
    </List.Item>
  );

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16, color: '#666' }}>正在加载最近更新...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '40px 0' }}>
        <div style={{ color: '#ff4d4f', marginBottom: 8 }}>❌ 加载失败</div>
        <div style={{ color: '#666', fontSize: 12 }}>{error}</div>
      </div>
    );
  }

  if (!data || data.culturalElements.length === 0) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="暂无更新记录"
        style={{ padding: '40px 0' }}
      />
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ClockCircleOutlined style={{ color: '#1890ff' }} />
          <span>最近更新的文化要素</span>
          <Tag color="blue">{data.culturalElements.length}</Tag>
        </div>
      }
      size="small"
      style={{
        height: '100%',
        backgroundColor: '#fff',
      }}
      styles={{
        body: {
          padding: '8px 0',
          height: 'calc(100% - 57px)',
          overflow: 'auto',
        },
      }}
    >
      <List
        dataSource={data.culturalElements}
        renderItem={renderCulturalElement}
        style={{ height: '100%' }}
        split={false}
      />
    </Card>
  );
};

export default RecentUpdates;
