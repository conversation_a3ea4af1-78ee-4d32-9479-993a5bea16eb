/**
 * @file 古城字典数据dva model
 * @description 使用dva管理古城字典数据的全局缓存，方便其他页面引用
 * <AUTHOR> Assistant
 * @date 2025-10-12
 */

import { getAncientCityDictTree } from '@/services/dictionary';
import { AnyAction, Reducer } from '@umijs/max';
import { message } from 'antd';

// 递归过滤禁用状态的古城项
function filterEnabledAncientCities(
  cities: API.AncientCityDict[],
): API.AncientCityDict[] {
  return cities
    .filter((city) => city.status === 1) // 只保留启用状态的项
    .map((city) => ({
      ...city,
      children: city.children
        ? filterEnabledAncientCities(city.children)
        : undefined,
    }));
}

// 古城字典状态接口
export interface AncientCityState {
  ancientCityList: API.AncientCityDict[];
  loading: boolean;
}

// Model接口
interface IAncientCityModel {
  namespace: 'ancientCity';
  state: AncientCityState;
  effects: {
    fetchAncientCityList: (
      action: { payload: Record<string, any> },
      effects: { call: Call; put: Put },
    ) => void;
  };
  reducers: {
    saveAncientCityList: Reducer<AncientCityState>;
    updateLoading: Reducer<AncientCityState>;
  };
}

// 类型定义
type Call = (fn: any, ...args: any[]) => any;
type Put = (action: AnyAction) => any;

const AncientCityModel: IAncientCityModel = {
  namespace: 'ancientCity',

  state: {
    ancientCityList: [],
    loading: false,
  } as AncientCityState,

  effects: {
    /** 查询古城列表 **/
    *fetchAncientCityList(
      _: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'updateLoading', payload: true });
      try {
        const { errCode, msg, data } = yield call(getAncientCityDictTree);
        if (errCode) {
          message.error(msg || '获取古城字典失败');
        } else {
          // 过滤出启用状态的古城
          const enabledCities = filterEnabledAncientCities(data || []);
          yield put({ type: 'saveAncientCityList', payload: enabledCities });
        }
      } catch (error) {
        console.error('获取古城字典失败:', error);
        message.error('获取古城字典失败');
      } finally {
        yield put({ type: 'updateLoading', payload: false });
      }
    },
  },

  reducers: {
    saveAncientCityList(state, action) {
      return {
        ...state,
        ancientCityList: action.payload,
      };
    },

    updateLoading(state, action) {
      return {
        ...state,
        loading: action.payload,
      };
    },
  },
};

export default AncientCityModel;
