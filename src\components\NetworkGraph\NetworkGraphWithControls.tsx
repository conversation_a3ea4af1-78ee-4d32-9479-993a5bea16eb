import { AncientCityState } from '@/models/ancientCity';
import { DictionaryState } from '@/models/dictionary';
import {
  FilterOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useDispatch, useSelector } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Drawer,
  Form,
  Row,
  Select,
  Slider,
  Space,
  Tooltip,
} from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import NetworkGraph from './index';
import NodeDetail from './NodeDetail';

export interface NetworkGraphWithControlsProps {
  title?: string;
  onExport?: () => void;
  onNodeClick?: (nodeData: any) => void;
  onLinkClick?: (linkData: any) => void;
  style?: React.CSSProperties;
  className?: string;
  isPublic?: boolean; // 是否为公开模式
}

const NetworkGraphWithControls: React.FC<NetworkGraphWithControlsProps> = ({
  title = '关系网络图',
  // onExport,
  onNodeClick,
  onLinkClick,
  style,
  className,
  isPublic = false,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [filtersVisible, setFiltersVisible] = useState(true);
  const [graphHeight, setGraphHeight] = useState(600);
  // const [showLabels, setShowLabels] = useState(true);
  // const [nodeSize, setNodeSize] = useState(1);
  // const [linkWidth, setLinkWidth] = useState(1);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterRelation, setFilterRelation] = useState<string>('all');
  const [filterAncientCity, setFilterAncientCity] = useState<string>('all');
  const [showDetail, setShowDetail] = useState(false);
  const [selectedNode, setSelectedNode] = useState<any>(null);

  // 网络图数据状态
  const [networkData, setNetworkData] = useState<API.NetworkGraphData | null>(
    null,
  );
  const [networkLoading, setNetworkLoading] = useState(false);

  // dva状态和dispatch
  const dispatch = useDispatch();
  const dictionary = useSelector(
    (state: any) => state.dictionary,
  ) as DictionaryState;
  const ancientCity = useSelector(
    (state: any) => state.ancientCity,
  ) as AncientCityState;

  // 初始化加载字典数据
  useEffect(() => {
    if (!dictionary?.typeList || dictionary.typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
    if (!dictionary?.relationList || dictionary.relationList.length === 0) {
      dispatch({ type: 'dictionary/fetchRelationList' });
    }
    if (
      !ancientCity?.ancientCityList ||
      ancientCity.ancientCityList.length === 0
    ) {
      dispatch({ type: 'ancientCity/fetchAncientCityList' });
    }
  }, [
    dispatch,
    dictionary?.typeList,
    dictionary?.relationList,
    ancientCity?.ancientCityList,
  ]);

  // 加载网络图数据
  const loadNetworkData = useCallback(async () => {
    setNetworkLoading(true);
    try {
      // 构建API参数
      const params: any = {};

      // 要素类型筛选
      if (filterType === 'ancient_city') {
        params.sourceEntityType = 'ancient_city';
      } else if (filterType === 'cultural_element') {
        params.sourceEntityType = 'cultural_element';
      }

      // 关系类型筛选
      if (filterRelation !== 'all') {
        // 根据关系名称找到关系ID
        const relationDict = dictionary?.relationList?.find(
          (relation) => relation.relationName === filterRelation,
        );
        if (relationDict) {
          params.relationDictId = relationDict.id;
        }
      }

      // 古城筛选 - 这个需要特殊处理，可能需要后端支持
      if (filterAncientCity !== 'all') {
        // 可以通过sourceEntityName或其他参数传递古城名称
        params.ancientCityName = filterAncientCity;
      }

      // 状态筛选
      if (!isPublic) {
        // 管理端可以看到所有状态，公开端只看启用状态
        params.status = 1;
      }

      console.log('🔍 网络图API参数:', params);

      // 调用API
      let response;
      if (isPublic) {
        const { getPublicNetworkGraphData } = await import(
          '@/services/relationship'
        );
        response = await getPublicNetworkGraphData(params);
      } else {
        const { getNetworkGraphData } = await import('@/services/relationship');
        response = await getNetworkGraphData(params);
      }

      if (response.errCode === 0) {
        console.log('📊 网络图数据:', response.data);
        setNetworkData(response.data || null);
      } else {
        console.error('获取网络图数据失败:', response.msg);
        setNetworkData(null);
      }
    } catch (error) {
      console.error('加载网络图数据失败:', error);
      setNetworkData(null);
    } finally {
      setNetworkLoading(false);
    }
  }, [
    filterType,
    filterRelation,
    filterAncientCity,
    isPublic,
    dictionary?.relationList,
  ]);

  // 当筛选条件变化时重新加载数据
  useEffect(() => {
    loadNetworkData();
  }, [loadNetworkData]);

  // 获取关系类型列表（从dva缓存获取）
  const relationTypes = useMemo(() => {
    if (!dictionary?.relationList || dictionary.relationList.length === 0) {
      return [];
    }
    return dictionary.relationList
      .filter((relation) => relation.status === 1) // 只显示启用的关系
      .map((relation) => ({
        label: relation.relationName,
        value: relation.relationName,
      }));
  }, [dictionary?.relationList]);

  // 获取古城列表（从dva缓存获取）
  const ancientCityTypes = useMemo(() => {
    if (
      !ancientCity?.ancientCityList ||
      ancientCity.ancientCityList.length === 0
    ) {
      return [];
    }
    return ancientCity.ancientCityList
      .filter((city) => city.status === 1) // 只显示启用的古城
      .map((city) => ({
        label: city.cityName,
        value: city.cityName,
      }));
  }, [ancientCity?.ancientCityList]);

  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 处理节点点击 - 只显示详情，不进行筛选
  const handleNodeClick = useCallback(
    (nodeData: any) => {
      // 设置选中的节点并显示详情
      setSelectedNode(nodeData);
      setShowDetail(true);

      // 调用原始的节点点击处理函数
      onNodeClick?.(nodeData);
    },
    [onNodeClick],
  );

  // 处理连线点击 - 快速筛选关系
  const handleLinkClick = useCallback(
    (linkData: any) => {
      // 如果当前已经筛选了该关系，则取消筛选
      if (filterRelation === linkData.relation) {
        setFilterRelation('all');
      } else {
        // 否则筛选该关系
        setFilterRelation(linkData.relation);
      }

      // 调用原始的连线点击处理函数
      onLinkClick?.(linkData);
    },
    [filterRelation, onLinkClick],
  );

  // 重置设置
  const resetSettings = () => {
    setGraphHeight(600);
    // setShowLabels(true);
    // setNodeSize(1);
    // setLinkWidth(1);
    setFilterType('all');
    setFilterRelation('all');
    setFilterAncientCity('all');
  };

  // 重置筛选
  const resetFilters = () => {
    setFilterType('all');
    setFilterRelation('all');
    setFilterAncientCity('all');
  };

  // 筛选栏
  const filtersBar = filtersVisible && (
    <Card size="small" style={{ marginBottom: 8 }}>
      <Row gutter={16} align="middle" justify="space-between">
        <Col>
          <Space style={{ gap: 0 }}>
            <span style={{ fontWeight: 'bold' }}>筛选条件：</span>
            <Tooltip title="提示：点击图中的节点或连线可快速筛选">
              <Button
                type="text"
                size="small"
                style={{ color: '#999', padding: 0 }}
              >
                <QuestionCircleOutlined />
              </Button>
            </Tooltip>
          </Space>
        </Col>
        <Col>
          <Space>
            <span>古城：</span>
            <Select
              value={filterAncientCity}
              onChange={setFilterAncientCity}
              style={{ width: 120 }}
              size="small"
              placeholder="选择古城"
              options={[{ label: '全部', value: 'all' }, ...ancientCityTypes]}
            />
          </Space>
        </Col>
        {/* <Col span={5}>
          <Space>
            <span>要素类型：</span>
            <Select
              value={filterType}
              onChange={setFilterType}
              style={{ width: 120 }}
              size="small"
              options={[{ label: '全部', value: 'all' }, ...nodeTypes]}
            />
          </Space>
        </Col> */}
        <Col>
          <Space>
            <span>关系类型：</span>
            <Select
              value={filterRelation}
              onChange={setFilterRelation}
              style={{ width: 120 }}
              size="small"
              options={[{ label: '全部', value: 'all' }, ...relationTypes]}
            />
          </Space>
        </Col>
        <Col>
          <Space>
            <Button size="small" onClick={resetFilters}>
              重置
            </Button>
            <span style={{ color: '#666', fontSize: '12px' }}>
              节点 {networkData?.nodes.length || 0} | 关系{' '}
              {networkData?.links.length || 0}
            </span>
          </Space>
        </Col>
      </Row>
    </Card>
  );

  // 工具栏
  const toolbar = (
    <Space>
      <Tooltip title={filtersVisible ? '隐藏筛选' : '显示筛选'}>
        <Button
          icon={<FilterOutlined />}
          type={filtersVisible ? 'primary' : 'default'}
          onClick={() => setFiltersVisible(!filtersVisible)}
        />
      </Tooltip>
      <Tooltip title={showDetail ? '隐藏详情' : '显示详情'}>
        <Button
          icon={<InfoCircleOutlined />}
          type={showDetail ? 'primary' : 'default'}
          onClick={() => setShowDetail(!showDetail)}
          disabled={!selectedNode}
        />
      </Tooltip>
      <Tooltip title="刷新数据">
        <Button
          icon={<ReloadOutlined />}
          onClick={loadNetworkData}
          loading={networkLoading}
        />
      </Tooltip>
      {/* <Tooltip title="导出图片">
        <Button icon={<DownloadOutlined />} onClick={onExport} />
      </Tooltip> */}
      <Tooltip title="设置">
        <Button
          icon={<SettingOutlined />}
          onClick={() => setSettingsVisible(true)}
        />
      </Tooltip>
      <Tooltip title={isFullscreen ? '退出全屏' : '全屏显示'}>
        <Button
          icon={
            isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />
          }
          onClick={toggleFullscreen}
        />
      </Tooltip>
    </Space>
  );

  // 设置面板
  const settingsPanel = (
    <Drawer
      title="图表设置"
      placement="right"
      onClose={() => setSettingsVisible(false)}
      open={settingsVisible}
      width={320}
    >
      <Form layout="vertical">
        <Form.Item label="图表高度">
          <Slider
            min={400}
            max={1200}
            value={graphHeight}
            onChange={setGraphHeight}
            marks={{
              400: '400px',
              600: '600px',
              800: '800px',
              1200: '1200px',
            }}
          />
        </Form.Item>

        {/* <Form.Item label="显示标签">
          <Switch checked={showLabels} onChange={setShowLabels} />
        </Form.Item>

        <Form.Item label="节点大小">
          <Slider
            min={0.5}
            max={2}
            step={0.1}
            value={nodeSize}
            onChange={setNodeSize}
            marks={{
              0.5: '小',
              1: '中',
              2: '大',
            }}
          />
        </Form.Item>

        <Form.Item label="连线粗细">
          <Slider
            min={0.5}
            max={2}
            step={0.1}
            value={linkWidth}
            onChange={setLinkWidth}
            marks={{
              0.5: '细',
              1: '中',
              2: '粗',
            }}
          />
        </Form.Item> */}

        <Form.Item>
          <Button onClick={resetSettings} block>
            重置设置
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  );

  const graphStyle = isFullscreen
    ? {
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        backgroundColor: '#fff',
      }
    : style;

  return (
    <>
      <Row gutter={16} style={{ height: '100%' }}>
        <Col span={showDetail ? 18 : 24}>
          <Card
            title={title}
            extra={toolbar}
            style={graphStyle}
            className={className}
            styles={{ body: { padding: 0 } }}
          >
            {filtersBar}
            <NetworkGraph
              data={networkData}
              loading={networkLoading}
              title=""
              height={isFullscreen ? '100vh' : graphHeight}
              onNodeClick={handleNodeClick}
              onLinkClick={handleLinkClick}
            />
          </Card>
        </Col>
        {showDetail && (
          <Col span={6} style={{ height: '100%' }}>
            <NodeDetail nodeData={selectedNode} visible={showDetail} />
          </Col>
        )}
      </Row>
      {settingsPanel}
    </>
  );
};

export default NetworkGraphWithControls;
