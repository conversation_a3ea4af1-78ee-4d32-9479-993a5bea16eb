/* 第一屏布局 - Banner在上，统计在左下，地图在右下 */
.first-screen {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 主体内容区域 - 下方左右分布 */
.main-content {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

/* 左下 - 统计数据面板 */
.statistics-panel {
  width: 320px;
  background: rgba(255, 255, 255, 95%);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 20%);
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  z-index: 5;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 10%);
  overflow-y: auto;

  .statistics-cards {
    flex-shrink: 0; /* 防止卡片被压缩 */
  }

  .stat-card {
    border-radius: 6px;
    transition: all 0.3s ease;
    margin-bottom: 8px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 3px 8px rgba(0, 0, 0, 12%);
    }

    .stat-card-content {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 6px 0;

      .stat-icon {
        font-size: 20px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
      }

      .stat-info {
        flex: 1;

        .stat-value {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 2px;
          display: flex;
          align-items: baseline;
          gap: 3px;

          .stat-suffix {
            font-size: 12px;
            opacity: 0.8;
          }
        }

        .stat-title {
          font-size: 12px;
          color: #666;
          font-weight: 500;
          line-height: 1.3;
        }
      }
    }
  }

  .chart-container {
    flex: 1; // 自动占满剩余空间
    max-height: 500px; // 最大高度限制
    background: white;
    border-radius: 6px;
    padding: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 8%);
    min-height: 200px; // 最小高度
  }

  // 预留的扩展图表区域
  .extended-chart-container {
    flex: 1; // 占满剩余空间
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 8%);
    margin-top: 16px;
    min-height: 200px; // 最小高度

    .chart-placeholder {
      height: 100%; // 占满容器高度
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;

      .placeholder-content {
        text-align: center;
        color: #999;

        .placeholder-icon {
          font-size: 32px;
          margin-bottom: 12px;
          opacity: 0.6;
        }

        .placeholder-text {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 4px;
          color: #666;
        }

        .placeholder-subtitle {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}

/* 右侧地图区域 - T字布局的主要内容区 */
.map-section {
  flex: 1;
  position: relative;
  background: #f8fafc;
  padding: 24px;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素能正确收缩 */

  .map-container-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 12%);
    overflow: hidden;
    background: #fff;
  }

  .map-container {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
  }

  /* 地图状态显示 */
  .map-status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    background: rgba(255, 255, 255, 95%);
    padding: 24px 32px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 10%);
    text-align: center;

    .status-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    .status-icon {
      font-size: 32px;
    }

    .status-text {
      font-size: 16px;
      color: #666;
      font-weight: 500;
    }

    &.loading .status-icon {
      animation: spin 1s linear infinite;
    }

    &.error .status-text {
      color: #ff4d4f;
    }

    &.warning .status-text {
      color: #faad14;
    }
  }
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 右侧地图区域 - 已不再使用，保留以备后用 */
.dashboard-map {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  background: #fff;

  .map-header {
    padding: 32px 32px 20px;
    background: #fff;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .map-header-left {
      flex: 1;

      .map-title {
        color: #1e293b !important;
        font-size: 20px !important;
        font-weight: 600 !important;
        margin-bottom: 8px !important;
        letter-spacing: -0.01em;
      }

      .map-description {
        color: #64748b !important;
        font-size: 14px;
        margin-bottom: 0 !important;
        font-weight: 400;
      }
    }

    .map-header-right {
      flex: 1;
      text-align: right;

      .system-title {
        color: #1e293b !important;
        font-size: 20px !important;
        font-weight: 600 !important;
        margin-bottom: 8px !important;
        letter-spacing: -0.01em;
      }

      .system-description {
        color: #64748b !important;
        font-size: 14px;
        margin-bottom: 0 !important;
        font-weight: 400;
      }
    }
  }

  .map-container-wrapper {
    flex: 1;
    position: relative;
    margin: 20px 32px 32px;
    border-radius: 12px;
    overflow: hidden;
    background: #fff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 10%);

    .map-container {
      width: 100%;
      height: 100%;
      border-radius: 12px;
      overflow: hidden;
    }

    .map-status {
      position: absolute;
      top: 24px;
      left: 24px;
      z-index: 1000;
      border-radius: 12px;
      backdrop-filter: blur(20px);
      padding: 12px 20px;
      border: 1px solid rgba(255, 255, 255, 30%);
      font-size: 14px;
      font-weight: 600;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 10%);

      &.loading {
        background: rgba(59, 130, 246, 15%);
        border-color: rgba(59, 130, 246, 30%);
        color: #3b82f6;
      }

      &.error {
        background: rgba(239, 68, 68, 15%);
        border-color: rgba(239, 68, 68, 30%);
        color: #ef4444;
      }

      &.warning {
        background: rgba(245, 158, 11, 15%);
        border-color: rgba(245, 158, 11, 30%);
        color: #f59e0b;
      }

      .status-content {
        display: flex;
        align-items: center;
        gap: 10px;

        .status-icon {
          font-size: 16px;
        }

        .status-text {
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
}

/* 动画定义 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }

  33% {
    transform: translateY(-10px) rotate(1deg);
  }

  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.6;
  }
}

/* 装饰性元素 */
.dashboard-sidebar {
  .decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 20%;
      right: 10%;
      width: 100px;
      height: 100px;
      background: linear-gradient(45deg, rgba(102, 126, 234, 10%), transparent);
      border-radius: 50%;
      animation: pulse 4s ease-in-out infinite;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 30%;
      left: 5%;
      width: 60px;
      height: 60px;
      background: linear-gradient(45deg, rgba(118, 75, 162, 8%), transparent);
      border-radius: 50%;
      animation: pulse 6s ease-in-out infinite reverse;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-dashboard {
    flex-direction: column;
    height: auto;
    min-height: 100vh;

    .dashboard-sidebar {
      width: 100%;
      min-width: auto;
      max-width: none;
      border-right: none;
      border-bottom: 1px solid rgba(255, 255, 255, 20%);

      .sidebar-content {
        padding: 32px 24px;
      }

      .brand-section {
        margin-bottom: 32px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        margin-bottom: 32px;
      }

      .stat-card {
        padding: 16px 12px;
        min-height: 90px;

        .stat-content {
          .stat-number .stat-value {
            font-size: 32px;
          }

          .stat-title {
            font-size: 12px;
          }
        }
      }
    }

    .dashboard-map {
      min-height: 60vh;

      .map-header {
        padding: 40px 32px 20px;
      }

      .map-container-wrapper {
        margin: 20px 32px 40px;
      }
    }
  }
}

@media (max-width: 768px) {
  .main-dashboard {
    .dashboard-sidebar {
      .sidebar-content {
        padding: 24px 16px;
      }

      .brand-section {
        margin-bottom: 24px;

        .brand-title {
          font-size: 24px !important;
        }

        .brand-description {
          font-size: 14px;
        }
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        margin-bottom: 24px;
      }

      .stat-card {
        padding: 16px 14px;
        border-radius: 12px;
        min-height: 80px;

        .stat-content {
          .stat-number {
            margin-bottom: 4px;

            .stat-value {
              font-size: 28px;
            }

            .stat-suffix {
              font-size: 14px;
            }
          }

          .stat-title {
            font-size: 12px;
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
          }
        }
      }

      .action-buttons {
        flex-direction: column;
        gap: 12px;

        .primary-action,
        .secondary-action {
          height: 48px;
          border-radius: 12px;
          font-size: 15px;
        }
      }
    }

    .dashboard-map {
      .map-header {
        padding: 32px 20px 16px;

        .map-title {
          font-size: 20px !important;
        }

        .map-description {
          font-size: 16px;
        }
      }

      .map-container-wrapper {
        margin: 16px 20px 32px;
        border-radius: 16px;

        .map-container {
          border-radius: 16px;
        }
      }
    }
  }
}

/* 响应式设计 - Banner在上，统计在左下，地图在右下 */

/* 大屏幕优化 (1200px以上) */
@media (min-width: 1200px) {
  .statistics-panel {
    width: 380px;
    padding: 24px 20px;

    .stat-card {
      .stat-card-content {
        .stat-icon {
          font-size: 22px;
          width: 36px;
          height: 36px;
        }

        .stat-info {
          .stat-value {
            font-size: 22px;
          }

          .stat-title {
            font-size: 13px;
          }
        }
      }
    }

    .chart-container {
      min-height: 200px;
      max-height: 500px;
    }

    // 大屏幕样式优化（扩展图表通过条件渲染控制）
  }
}

/* 超大屏幕优化 (1400px以上) */
@media (min-width: 1400px) {
  .statistics-panel {
    width: 420px;
    padding: 28px 24px;

    // 超大屏幕样式优化（扩展图表通过条件渲染控制）
  }
}

/* 中等屏幕 (768px - 1199px) */
@media (max-width: 1199px) and (min-width: 769px) {
  .statistics-panel {
    width: 300px;
    padding: 18px 14px;

    .stat-card {
      .stat-card-content {
        .stat-icon {
          font-size: 18px;
          width: 28px;
          height: 28px;
        }

        .stat-info {
          .stat-value {
            font-size: 18px;
          }

          .stat-title {
            font-size: 11px;
          }
        }
      }
    }

    .chart-container {
      min-height: 140px;
      max-height: 160px;
    }

    // 中等屏幕样式优化（扩展图表通过条件渲染控制）
  }
}

/* 平板设备 (768px以下) */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    height: calc(100vh - 120px); /* 减去banner高度 */
  }

  .statistics-panel {
    width: 100%;
    height: 200px; /* 固定高度 */
    padding: 16px;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 20%);
    flex-direction: row;
    gap: 16px;

    .statistics-cards {
      flex: 1;
      display: flex;
      gap: 8px;
      margin-bottom: 0;

      .stat-card {
        flex: 1;
        margin-bottom: 0;

        .stat-card-content {
          flex-direction: column;
          text-align: center;
          gap: 6px;
          padding: 4px 0;

          .stat-icon {
            font-size: 16px;
            width: 24px;
            height: 24px;
            margin: 0 auto;
          }

          .stat-info {
            .stat-value {
              font-size: 16px;
              justify-content: center;
            }

            .stat-title {
              font-size: 10px;
            }
          }
        }
      }
    }

    .chart-container {
      flex: 1;
      min-height: 120px;
      max-height: 160px;
    }

    // 平板设备样式优化（扩展图表通过条件渲染控制）
  }

  .map-section {
    flex: 1;
    padding: 16px;
  }
}

/* 手机设备 (480px以下) */
@media (max-width: 480px) {
  .statistics-panel {
    height: 240px;
    flex-direction: column;
    gap: 12px;

    .statistics-cards {
      flex: none;
      flex-direction: column;
      gap: 6px;

      .stat-card {
        .stat-card-content {
          flex-direction: row;
          text-align: left;
          gap: 8px;
          padding: 4px 0;

          .stat-icon {
            margin: 0;
          }

          .stat-info {
            .stat-value {
              font-size: 18px;
              justify-content: flex-start;
            }

            .stat-title {
              font-size: 11px;
            }
          }
        }
      }
    }

    .chart-container {
      flex: 1;
      min-height: 100px;
      max-height: 120px;
    }

    // 手机设备样式优化（扩展图表通过条件渲染控制）
  }

  .map-section {
    padding: 12px;
  }
}

/* 地图区域响应式设计 */
@media (max-width: 768px) {
  .map-section {
    padding: 16px;

    .map-container-wrapper {
      border-radius: 10px;
    }

    .map-container {
      border-radius: 10px;
    }
  }
}

@media (max-width: 480px) {
  .map-section {
    padding: 12px;

    .map-container-wrapper {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 6%);
    }

    .map-container {
      border-radius: 8px;
    }
  }
}
