// 地图标记点配置

// 地图标记点类型定义
export interface MapMarkerItem {
  id: number;
  type: 'ancientCity';
  name: string;
  longitude: number;
  latitude: number;
  elevation?: number;
  summary?: string;
  thumbnailUrl?: string;
  photos?: Array<{
    id: number;
    name: string;
    url: string;
  }>;
}

// 根据类型获取标记点图标
export const getMarkerIcon = (type: MapMarkerItem['type']) => {
  void type;
  // 现在只使用古城图标 - 倒立水滴形状，蓝绿色调
  const ancientCityIcon = {
    image: '/images/markers/ancient-city.svg', // 古城图标
    size: [32, 40] as [number, number], // 新尺寸：32×40
    offset: [-16, -40] as [number, number], // 注意：使用自定义渲染时此offset不生效，实际offset在GaoDeMap组件中设置
  };

  // 所有类型都使用古城图标
  return ancientCityIcon;
};

// 根据类型获取标记点颜色
export const getMarkerColor = (type: MapMarkerItem['type']) => {
  void type;
  return '#4A90E2'; // 蓝色 - 与地图协调的颜色
};

// 根据类型获取中文名称
export const getTypeLabel = (type: MapMarkerItem['type']) => {
  void type;
  return '古城';
};

// ==================== 简化：只支持古城类型 ====================

/**
 * 获取标记点图标（简化版）
 */
export const getMarkerIconFromCulturalElement = () => {
  return getMarkerIcon('ancientCity');
};

/**
 * 获取标记点颜色（简化版）
 */
export const getMarkerColorFromCulturalElement = () => {
  return getMarkerColor('ancientCity');
};

/**
 * 获取类型标签（简化版）
 */
export const getTypeLabelFromCulturalElement = () => {
  return getTypeLabel('ancientCity');
};
