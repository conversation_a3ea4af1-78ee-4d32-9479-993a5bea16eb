/**
 * @file 坐标系配置组件
 * @description 用于管理端配置和切换坐标系
 * <AUTHOR> Assistant
 * @date 2025-10-16
 */

import {
  useCoordinateSystem,
  useCoordinateSystemSwitch,
} from '@/hooks/useCoordinateSystem';
import { InfoCircleOutlined } from '@ant-design/icons';
import {
  Alert,
  Card,
  Descriptions,
  Radio,
  Space,
  Switch,
  Tooltip,
  Typography,
} from 'antd';
import React, { useState } from 'react';

const { Text } = Typography;

interface CoordinateSystemConfigProps {
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 是否允许切换坐标系 */
  allowSwitch?: boolean;
  /** 配置变更回调 */
  onChange?: (config: any) => void;
}

const CoordinateSystemConfig: React.FC<CoordinateSystemConfigProps> = ({
  showDetails = true,
  allowSwitch = false,
  onChange,
}) => {
  const { config, getCoordinateSystemInfo } = useCoordinateSystem();
  const { getSupportedSystems } = useCoordinateSystemSwitch();

  const [enableConversion, setEnableConversion] = useState(config.autoConvert);
  const [selectedSystem, setSelectedSystem] = useState(config.storage);

  const coordinateInfo = getCoordinateSystemInfo();
  const supportedSystems = getSupportedSystems();

  const handleConversionToggle = (checked: boolean) => {
    setEnableConversion(checked);
    onChange?.({ ...config, autoConvert: checked });
  };

  const handleSystemChange = (value: string) => {
    setSelectedSystem(value as any);
    onChange?.({ ...config, storage: value });
  };

  return (
    <Card title="坐标系配置" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 当前配置信息 */}
        {showDetails && (
          <Descriptions size="small" column={1} bordered>
            <Descriptions.Item label="存储坐标系">
              <Space>
                <Text strong>{coordinateInfo.storage}</Text>
                <Text type="secondary">
                  {coordinateInfo.description.storage}
                </Text>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="显示坐标系">
              <Space>
                <Text strong>{coordinateInfo.display}</Text>
                <Text type="secondary">
                  {coordinateInfo.description.display}
                </Text>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="自动转换">
              <Text strong>{coordinateInfo.autoConvert ? '启用' : '禁用'}</Text>
            </Descriptions.Item>
          </Descriptions>
        )}

        {/* 坐标系说明 */}
        <Alert
          message="坐标系说明"
          description={
            <div>
              <p>
                <strong>GCJ-02（当前使用）：</strong>
                中国国测局坐标系，高德地图、腾讯地图等使用
              </p>
              <p>
                <strong>WGS-84：</strong>
                世界标准坐标系，GPS设备、国际地图服务使用
              </p>
              <p>
                <strong>BD-09：</strong>百度坐标系，百度地图专用
              </p>
            </div>
          }
          type="info"
          showIcon
        />

        {/* 坐标转换开关 */}
        <Card size="small" title="坐标转换设置">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Space>
                <Text>启用坐标转换</Text>
                <Tooltip title="启用后可以在不同坐标系之间自动转换">
                  <InfoCircleOutlined />
                </Tooltip>
              </Space>
              <Switch
                checked={enableConversion}
                onChange={handleConversionToggle}
                disabled={!allowSwitch}
              />
            </div>

            {enableConversion && (
              <Alert
                message="注意：启用坐标转换后，系统会自动在不同坐标系之间转换坐标数据"
                type="warning"
                showIcon
              />
            )}
          </Space>
        </Card>

        {/* 坐标系选择 */}
        {allowSwitch && (
          <Card size="small" title="坐标系选择">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text>选择数据存储使用的坐标系：</Text>
              <Radio.Group
                value={selectedSystem}
                onChange={(e) => handleSystemChange(e.target.value)}
              >
                <Space direction="vertical">
                  {supportedSystems.map((system) => (
                    <Radio key={system.value} value={system.value}>
                      <Space direction="vertical" size={0}>
                        <Text strong>{system.label}</Text>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {system.description}
                        </Text>
                      </Space>
                    </Radio>
                  ))}
                </Space>
              </Radio.Group>
            </Space>
          </Card>
        )}

        {/* 迁移警告 */}
        {allowSwitch && selectedSystem !== config.storage && (
          <Alert
            message="坐标系变更警告"
            description={
              <div>
                <p>更改坐标系将影响以下功能：</p>
                <ul>
                  <li>现有数据需要进行坐标转换</li>
                  <li>地图显示可能出现偏移</li>
                  <li>需要重新校验所有坐标数据</li>
                </ul>
                <p>
                  <strong>建议在系统维护期间进行此操作</strong>
                </p>
              </div>
            }
            type="error"
            showIcon
          />
        )}
      </Space>
    </Card>
  );
};

export default CoordinateSystemConfig;
