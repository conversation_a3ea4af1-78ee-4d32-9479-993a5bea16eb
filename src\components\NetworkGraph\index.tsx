import { Card, Empty, Spin } from 'antd';
import type { EChartsOption } from 'echarts';
import ReactECharts from 'echarts-for-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';

export interface NetworkGraphProps {
  data: API.NetworkGraphData | null; // 后台API格式：nodes: NetworkGraphNode[], category: string
  loading?: boolean;
  title?: string;
  height?: number | string;
  onNodeClick?: (nodeData: any) => void;
  onLinkClick?: (linkData: any) => void;
  style?: React.CSSProperties;
  className?: string;
}

// 使用React.memo包装组件，防止不必要的重新渲染
const NetworkGraph: React.FC<NetworkGraphProps> = React.memo(
  ({
    data,
    loading = false,
    title = '关系网络图',
    height = 600,
    onNodeClick,
    onLinkClick,
    style,
    className,
  }) => {
    // 保存节点位置的状态
    const [nodePositions, setNodePositions] = useState<
      Record<string, [number, number]>
    >({});

    // 添加渲染状态，解决首次渲染时ECharts不显示的问题
    const [isReady, setIsReady] = useState(false);
    const chartRef = useRef<any>(null);

    // 当数据变化时，延迟一点时间确保容器已经渲染完成
    useEffect(() => {
      if (data && data.nodes.length > 0) {
        setIsReady(false);
        const timer = setTimeout(() => {
          setIsReady(true);
          // 强制重新渲染图表
          if (chartRef.current) {
            const chartInstance = chartRef.current.getEchartsInstance();
            if (chartInstance) {
              chartInstance.resize();
            }
          }
        }, 100); // 延迟100ms确保DOM已经渲染

        return () => clearTimeout(timer);
      } else {
        setIsReady(false);
      }
    }, [data]);

    // 生成ECharts配置
    const chartOption = useMemo((): EChartsOption => {
      console.log('🎨 网络图组件接收到的数据:', data);
      if (!data || !data.nodes.length) {
        console.log('⚠️ 网络图数据为空或没有节点');
        return {};
      }

      // 处理节点数据 - 数据已经是ECharts格式，直接使用
      const nodes = data.nodes.map((node) => {
        // 数据格式：{ id, name, category: number, symbolSize, itemStyle, label }
        // 安全地访问可能为undefined的字段
        const nodeAny = node as any;
        const nodeLabel = node.label || {};
        const nodeItemStyle = node.itemStyle || {};

        return {
          id: node.id,
          name: node.name,
          category: typeof node.category === 'number' ? node.category : 0, // 确保是数字索引
          symbolSize: node.symbolSize || nodeAny.size || 15,
          itemStyle: {
            ...nodeItemStyle,
            color: nodeItemStyle.color || nodeAny.color || '#5470c6',
          },
          label: {
            ...nodeLabel,
            show: nodeLabel.show !== false, // 默认显示标签
            fontSize: (nodeLabel as any).fontSize || 12,
            fontWeight: 'bold' as const,
          },
          emphasis: {
            focus: 'adjacency' as const,
            label: {
              fontSize: 14,
            },
          },
          // 使用保存的位置
          ...(nodePositions[node.id]
            ? {
                x: nodePositions[node.id][0],
                y: nodePositions[node.id][1],
                fixed: true,
              }
            : {}),
        };
      });

      // 处理连线数据 - 数据已经是ECharts格式，直接使用
      const links = data.links.map((link) => {
        // 数据格式：{ source, target, name, lineStyle, label, value }
        // 安全地访问可能为undefined的label和lineStyle字段
        const linkLabel = link.label || {};
        const linkLineStyle = link.lineStyle || {};

        // 类型转换以支持不同的数据格式
        const linkAny = link as any;

        return {
          source: link.source,
          target: link.target,
          name: link.name || linkAny.relation || '关联',
          label: {
            show: linkLabel.show !== false, // 默认显示标签
            formatter: linkLabel.formatter || '{b}',
            fontSize: linkLabel.fontSize || 10,
            color: linkLabel.color || '#666',
          },
          lineStyle: {
            color: linkLineStyle.color || linkAny.color || '#999',
            width: linkLineStyle.width || 2,
            type: 'solid' as const,
            curveness: linkLineStyle.curveness || 0.1,
            opacity: linkLineStyle.opacity || 0.8,
          },
          emphasis: {
            lineStyle: {
              width: 4,
              opacity: 1,
            },
            label: {
              fontSize: 12,
              fontWeight: 'bold' as const,
            },
          },
          value: link.value || linkAny.weight || 1,
        };
      });

      // 处理分类数据 - 数据已经是ECharts格式，直接使用
      const categories = Array.isArray(data.categories)
        ? data.categories.map((category) => {
            // 数据格式：{ name, itemStyle: { color } }
            return {
              name: category.name,
              itemStyle: {
                color: category.itemStyle?.color,
              },
            };
          })
        : [
            // 默认分类（只包含古城和文化要素）
            { name: '古城', itemStyle: { color: '#5470c6' } },
            { name: '文化要素', itemStyle: { color: '#91cc75' } },
          ];

      return {
        title: title
          ? {
              text: title,
              left: 'center',
              textStyle: {
                fontSize: 16,
                fontWeight: 'bold',
              },
            }
          : undefined,
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            if (params.dataType === 'node') {
              const categoryName =
                categories[params.data.category]?.name ||
                `类别${params.data.category}`;
              return `
              <div>
                <strong>${params.data.name}</strong><br/>
                类别: ${categoryName}
              </div>
            `;
            } else if (params.dataType === 'edge') {
              const linkData = data.links.find(
                (link) =>
                  link.source === params.data.source &&
                  link.target === params.data.target,
              );

              // 优先使用新的 relationshipData 结构
              const relationshipData = linkData?.relationshipData;
              if (relationshipData) {
                return `
                <div style="padding: 8px; line-height: 1.5; max-width: 300px;">
                  <strong style="font-size: 14px; color: #333;">${
                    relationshipData.relationName || '关联关系'
                  }</strong><br/>
                  <span style="color: #666;">方向: ${
                    relationshipData.direction || '无'
                  }</span><br/>
                  <span style="color: #666;">词条: ${
                    relationshipData.term || '无'
                  }</span><br/>
                  <span style="color: #666;">源要素: ${
                    relationshipData.sourceElement || '无'
                  }</span><br/>
                  <span style="color: #666;">目标要素: ${
                    relationshipData.targetElement || '无'
                  }</span><br/>
                  ${
                    relationshipData.record
                      ? `<div style="margin-top: 4px; padding: 4px; background: #f5f5f5; border-radius: 3px; font-size: 12px; color: #555;">记载: ${relationshipData.record}</div>`
                      : ''
                  }
                </div>
              `;
              }

              // 简化的链接信息显示
              return `
              <div style="padding: 8px; line-height: 1.5;">
                <strong style="font-size: 14px; color: #333;">${
                  params.data.name || linkData?.name || '关联'
                }</strong><br/>
                <span style="color: #666;">连接: ${params.data.source} → ${
                params.data.target
              }</span>
              </div>
            `;
            }
            return '';
          },
        },
        legend: {
          data: categories,
          orient: 'vertical',
          left: 'left',
          top: 'middle',
        },
        series: [
          {
            type: 'graph',
            layout: 'force',
            data: nodes,
            links: links,
            categories: categories,
            roam: true, // 允许缩放和平移
            focusNodeAdjacency: true, // 鼠标悬停时突出显示相邻节点
            draggable: true, // 允许拖拽节点
            force: {
              repulsion: 1000, // 节点间斥力
              gravity: 0.1, // 重力
              edgeLength: 150, // 边长
              layoutAnimation: true, // 启用布局动画，保持节点动态效果
              friction: 0.6, // 适当的摩擦力，允许节点自然移动
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{b}',
            },
            lineStyle: {
              color: 'source',
              curveness: 0.1,
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 4,
              },
            },
          },
        ],
        animationDuration: 1500,
        animationEasingUpdate: 'quinticInOut',
      };
      // 注意：不要将selectedNode或showDetail等状态添加到依赖数组中，避免点击节点时重新渲染图表
    }, [data, title, nodePositions]);

    // 处理图表事件
    const onEvents = useMemo(() => {
      const events: Record<string, (params: any) => void> = {};

      // 点击事件
      events.click = (params: any) => {
        if (params.dataType === 'node' && onNodeClick) {
          onNodeClick(params.data);
        } else if (params.dataType === 'edge' && onLinkClick) {
          onLinkClick(params.data);
        }
      };

      // 拖拽结束事件，保存节点位置
      events.dragend = (params: any) => {
        if (params.dataType === 'node') {
          setNodePositions((prev) => ({
            ...prev,
            [params.data.id]: [params.data.x, params.data.y],
          }));
        }
      };

      return events;
    }, [onNodeClick, onLinkClick, setNodePositions]);

    // 渲染内容
    const renderContent = () => {
      if (loading) {
        return (
          <div style={{ textAlign: 'center', padding: '100px 0' }}>
            <Spin size="large" />
          </div>
        );
      }

      if (!data || !data.nodes.length) {
        return (
          <Empty description="暂无关系数据" style={{ padding: '100px 0' }} />
        );
      }

      // 如果数据存在但还没准备好，显示加载状态
      if (!isReady) {
        return (
          <div style={{ textAlign: 'center', padding: '100px 0' }}>
            <Spin size="large" tip="正在渲染网络图..." />
          </div>
        );
      }

      return (
        <ReactECharts
          ref={chartRef}
          option={chartOption}
          style={{
            height: typeof height === 'number' ? `${height}px` : height,
          }}
          onEvents={onEvents}
          opts={{ renderer: 'canvas' }}
          notMerge={true} // 强制重新渲染，不合并配置
        />
      );
    };

    return (
      <Card
        title={title}
        style={style}
        className={className}
        styles={{ body: { padding: 0 } }}
      >
        {renderContent()}
      </Card>
    );
  },
  (prevProps, nextProps) => {
    // 只有在数据真正变化时才重新渲染
    // 忽略onNodeClick等函数属性的变化，因为它们通常在父组件重新渲染时会创建新的函数引用
    return (
      prevProps.data === nextProps.data &&
      prevProps.loading === nextProps.loading &&
      prevProps.height === nextProps.height &&
      prevProps.title === nextProps.title
    );
  },
);

export default NetworkGraph;
