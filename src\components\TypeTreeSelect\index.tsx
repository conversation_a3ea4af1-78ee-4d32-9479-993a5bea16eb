/**
 * @file 类型树选择组件
 * @description 专门用于类型字典的树形选择组件，支持层级结构选择
 * <AUTHOR> Assistant
 * @date 2025-10-12
 */
import { useDispatch, useSelector } from '@umijs/max';
import { TreeSelect } from 'antd';
import React, { useEffect, useMemo } from 'react';

// 树形选择数据接口
interface TreeSelectOption {
  title: string;
  value: number;
  key: number;
  children?: TreeSelectOption[];
  disabled?: boolean;
}

export interface TypeTreeSelectProps {
  /** 当前选中的类型ID */
  value?: number;
  /** 选择变化回调 */
  onChange?: (value?: number) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否允许清空 */
  allowClear?: boolean;
  /** 是否显示搜索框 */
  showSearch?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否只显示启用的类型 */
  onlyEnabled?: boolean;
  /** 排除的类型ID（用于编辑时排除自身） */
  excludeId?: number;
  /** 是否默认展开所有节点 */
  treeDefaultExpandAll?: boolean;
  /** 是否支持多选 */
  multiple?: boolean;
  /** 多选时的值 */
  multipleValue?: number[];
  /** 多选时的变化回调 */
  onMultipleChange?: (values?: number[]) => void;
}

const TypeTreeSelect: React.FC<TypeTreeSelectProps> = ({
  value,
  onChange,
  placeholder = '请选择类型',
  allowClear = true,
  showSearch = true,
  disabled = false,
  style,
  onlyEnabled = true,
  excludeId,
  treeDefaultExpandAll = false,
  multiple = false,
  multipleValue,
  onMultipleChange,
}) => {
  // 使用hooks获取dva状态和dispatch
  const dispatch = useDispatch();
  const dictionary = useSelector((state: any) => state.dictionary);

  // 加载类型字典数据
  useEffect(() => {
    if (!dictionary?.typeList || dictionary.typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
  }, [dispatch, dictionary?.typeList]);

  // 转换为TreeSelect选项
  const convertToTreeSelectOptions = (
    typeList: API.TypeDict[],
  ): TreeSelectOption[] => {
    // 检查数据是否已经是树形结构
    const hasTreeStructure = typeList.some(
      (item) => item.children && item.children.length > 0,
    );

    if (hasTreeStructure) {
      // 数据已经是树形结构，直接转换格式
      return typeList
        .filter((item) => {
          // 排除指定ID
          const notExcluded = item.id !== excludeId;
          // 只显示启用的类型（如果设置了onlyEnabled）
          const isEnabled = !onlyEnabled || item.status === 1;
          return notExcluded && isEnabled;
        })
        .map((item) => ({
          title: item.typeName,
          value: item.id,
          key: item.id,
          disabled: item.status === 0,
          children:
            item.children && item.children.length > 0
              ? convertToTreeSelectOptions(item.children)
              : undefined,
        }));
    } else {
      // 数据是扁平结构，需要构建树形结构
      const buildTree = (parentId?: number | null): TreeSelectOption[] => {
        return typeList
          .filter((item) => {
            // 过滤父级ID匹配的项
            const isMatch =
              parentId === undefined || parentId === null
                ? item.parentId === null || item.parentId === undefined
                : item.parentId === parentId;

            // 排除指定ID
            const notExcluded = item.id !== excludeId;

            // 只显示启用的类型（如果设置了onlyEnabled）
            const isEnabled = !onlyEnabled || item.status === 1;

            return isMatch && notExcluded && isEnabled;
          })
          .map((item) => {
            const children = buildTree(item.id);
            return {
              title: item.typeName,
              value: item.id,
              key: item.id,
              disabled: item.status === 0,
              children: children.length > 0 ? children : undefined,
            };
          });
      };

      return buildTree();
    }
  };

  // 生成树形数据
  const treeData = useMemo(() => {
    if (!dictionary?.typeList) return [];
    return convertToTreeSelectOptions(dictionary.typeList);
  }, [dictionary?.typeList, excludeId, onlyEnabled]);

  // 处理单选变化
  const handleSingleChange = (val?: number) => {
    onChange?.(val);
  };

  // 处理多选变化
  const handleMultipleChange = (vals?: number[]) => {
    onMultipleChange?.(vals);
  };

  // 搜索过滤函数
  const filterTreeNode = (input: string, node: any) => {
    return (node.title as string).toLowerCase().includes(input.toLowerCase());
  };

  console.log('treeData:', treeData);
  if (multiple) {
    return (
      <TreeSelect
        multiple
        value={multipleValue}
        onChange={handleMultipleChange}
        placeholder={placeholder}
        allowClear={allowClear}
        showSearch={showSearch}
        disabled={disabled}
        loading={dictionary?.loading}
        style={style}
        treeData={treeData}
        treeDefaultExpandAll={treeDefaultExpandAll}
        filterTreeNode={filterTreeNode}
        getPopupContainer={(triggerNode) =>
          triggerNode.parentElement || document.body
        }
        maxTagCount="responsive"
        showCheckedStrategy={TreeSelect.SHOW_PARENT}
      />
    );
  }

  return (
    <TreeSelect
      value={value}
      onChange={handleSingleChange}
      placeholder={placeholder}
      allowClear={allowClear}
      showSearch={showSearch}
      disabled={disabled}
      loading={dictionary?.loading}
      style={style}
      treeData={treeData}
      treeDefaultExpandAll={treeDefaultExpandAll}
      filterTreeNode={filterTreeNode}
      getPopupContainer={(triggerNode) =>
        triggerNode.parentElement || document.body
      }
    />
  );
};

export default TypeTreeSelect;
