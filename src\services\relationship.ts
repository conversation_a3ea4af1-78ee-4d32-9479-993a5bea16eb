import { request } from '@umijs/max';

// ==================== 模拟数据生成函数 ====================

/**
 * 根据古城名称生成模拟节点数据
 * 按照ECharts格式：{ id, name, category: number, symbolSize, itemStyle, label }
 */
function generateMockNodes(cityName: string): any[] {
  const baseNodes = [
    {
      id: 'ancient_city_1',
      name: cityName,
      type: 'ancient_city',
      category: 0, // 古城类别索引
      symbolSize: 35,
      itemStyle: { color: '#5470c6' },
      label: { show: true },
    },
  ];

  // 根据不同古城生成不同的关联要素（只包含古城和文化要素）
  const cityRelations: Record<string, any[]> = {
    长安: [
      {
        id: 'cultural_1',
        name: '大雁塔',
        type: 'cultural_element',
        category: 1,
        symbolSize: 25,
        itemStyle: { color: '#91cc75' },
        label: { show: true },
      },
      {
        id: 'cultural_2',
        name: '小雁塔',
        type: 'cultural_element',
        category: 1,
        symbolSize: 20,
        itemStyle: { color: '#91cc75' },
        label: { show: true },
      },
      {
        id: 'cultural_3',
        name: '兵马俑',
        type: 'cultural_element',
        category: 1,
        symbolSize: 30,
        itemStyle: { color: '#91cc75' },
        label: { show: true },
      },
    ],
    洛阳: [
      {
        id: 'cultural_4',
        name: '白马寺',
        type: 'cultural_element',
        category: 1,
        symbolSize: 25,
        itemStyle: { color: '#91cc75' },
        label: { show: true },
      },
      {
        id: 'cultural_5',
        name: '龙门石窟',
        type: 'cultural_element',
        category: 1,
        symbolSize: 30,
        itemStyle: { color: '#91cc75' },
        label: { show: true },
      },
    ],
    开封: [
      {
        id: 'cultural_6',
        name: '铁塔',
        type: 'cultural_element',
        category: 1,
        symbolSize: 20,
        itemStyle: { color: '#91cc75' },
        label: { show: true },
      },
      {
        id: 'cultural_7',
        name: '大相国寺',
        type: 'cultural_element',
        category: 1,
        symbolSize: 25,
        itemStyle: { color: '#91cc75' },
        label: { show: true },
      },
    ],
  };

  const relatedElements = cityRelations[cityName] || cityRelations['长安'];

  // 添加文化要素节点（已经是ECharts格式）
  relatedElements.forEach((element) => {
    baseNodes.push(element);
  });

  return baseNodes;
}

/**
 * 根据古城名称生成模拟连线数据
 * 按照ECharts格式：{ source, target, name, lineStyle, label, value }
 */
function generateMockLinks(cityName: string): any[] {
  const cityLinkPatterns: Record<
    string,
    Array<{ target: string; name: string }>
  > = {
    长安: [
      { target: 'cultural_1', name: '文化承载' },
      { target: 'cultural_2', name: '历史传承' },
      { target: 'cultural_3', name: '文化象征' },
    ],
    洛阳: [
      { target: 'cultural_4', name: '宗教文化' },
      { target: 'cultural_5', name: '艺术传承' },
    ],
    开封: [
      { target: 'cultural_6', name: '建筑文化' },
      { target: 'cultural_7', name: '宗教传承' },
    ],
  };

  const linkPatterns = cityLinkPatterns[cityName] || cityLinkPatterns['长安'];

  return linkPatterns.map((pattern, index) => ({
    source: 'ancient_city_1',
    target: pattern.target,
    name: pattern.name,
    lineStyle: {
      width: 2 + index * 0.5,
      curveness: 0.1,
      color: '#999',
      opacity: 0.8,
    },
    label: {
      show: true,
      formatter: pattern.name,
      fontSize: 10,
      color: '#666',
    },
    value: 1 + index * 0.2,
  }));
}

// ==================== 管理端接口 ====================

/**
 * 创建关联关系
 */
export async function createRelationship(
  data: API.RelationshipDTO,
): Promise<API.ResType<API.RelationshipResponseDTO>> {
  return request('/admin/relationship', {
    method: 'POST',
    data,
  });
}

/**
 * 更新关联关系
 */
export async function updateRelationship(
  id: number,
  data: Partial<API.RelationshipDTO>,
): Promise<API.ResType<API.RelationshipResponseDTO>> {
  return request(`/admin/relationship/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除关联关系
 */
export async function deleteRelationship(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/relationship/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取关联关系详情
 */
export async function getRelationshipDetail(
  id: number,
): Promise<API.ResType<API.RelationshipResponseDTO>> {
  return request(`/admin/relationship/${id}`, {
    method: 'GET',
  });
}

/**
 * 分页查询关联关系
 */
export async function getRelationshipList(
  params?: API.RelationshipQueryDTO,
): Promise<API.ResType<API.PageResult<API.RelationshipResponseDTO>>> {
  return request('/admin/relationship', {
    method: 'GET',
    params,
  });
}

/**
 * 批量创建关联关系
 */
export async function batchCreateRelationship(
  data: API.BatchCreateRelationshipDTO,
): Promise<API.ResType<API.RelationshipResponseDTO[]>> {
  return request('/admin/relationship/batch', {
    method: 'POST',
    data,
  });
}

/**
 * 批量更新状态
 */
export async function batchUpdateRelationshipStatus(
  data: API.BatchUpdateStatusDTO,
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/relationship/batch-status', {
    method: 'POST',
    data,
  });
}

/**
 * 根据实体查询关联关系
 */
export async function getRelationshipsByElement(
  elementType: API.EntityType,
  elementId: number,
): Promise<API.ResType<API.RelationshipResponseDTO[]>> {
  return request(`/admin/relationship/by-element/${elementType}/${elementId}`, {
    method: 'GET',
  });
}

/**
 * 获取所有关联关系（不分页）
 */
export async function getAllRelationships(
  params?: Omit<API.RelationshipQueryDTO, 'page' | 'pageSize'>,
): Promise<API.ResType<API.RelationshipResponseDTO[]>> {
  return request('/admin/relationship/all', {
    method: 'GET',
    params,
  });
}

/**
 * 获取关系统计概览
 */
export async function getRelationshipStatistics(): Promise<
  API.ResType<API.RelationshipStatisticsDTO>
> {
  return request('/admin/relationship/statistics/overview', {
    method: 'GET',
  });
}

/**
 * 获取网络图数据
 */
export async function getNetworkGraphData(params?: {
  sourceEntityType?: API.EntityType;
  targetEntityType?: API.EntityType;
  relationDictId?: number;
  status?: number;
}): Promise<API.ResType<API.NetworkGraphData>> {
  return request('/openapi/relationship/network-graph', {
    method: 'GET',
    params,
  });
}

/**
 * 下载导入模板
 */
export async function getRelationshipImportTemplate(): Promise<
  API.ResType<API.TemplateDownloadResponse>
> {
  return request('/admin/relationship/template/download', {
    method: 'GET',
  });
}

/**
 * 预览导入数据
 */
export async function previewRelationshipImport(
  file: File,
): Promise<API.ResType<API.RelationshipImportPreviewResponse>> {
  const formData = new FormData();
  formData.append('files', file);

  return request('/admin/relationship/import/preview', {
    method: 'POST',
    data: formData,
  });
}

/**
 * 执行导入
 */
export async function executeRelationshipImport(
  file: File,
): Promise<API.ResType<API.RelationshipImportResponse>> {
  const formData = new FormData();
  formData.append('files', file);

  return request('/admin/relationship/import/execute', {
    method: 'POST',
    data: formData,
  });
}

/**
 * 导出关联数据（POST方式）
 */
export async function exportRelationships(
  params?: API.RelationshipQueryDTO,
): Promise<API.ResType<API.ExportResponse>> {
  return request('/admin/relationship/export', {
    method: 'POST',
    data: params,
  });
}

/**
 * 导出关联数据（GET方式）
 */
export async function exportRelationshipsGet(
  params?: API.RelationshipQueryDTO,
): Promise<Response> {
  const queryParams = new URLSearchParams();

  if (params?.sourceEntityType) {
    queryParams.append('sourceEntityType', params.sourceEntityType);
  }
  if (params?.targetEntityType) {
    queryParams.append('targetEntityType', params.targetEntityType);
  }
  if (params?.relationDictId) {
    queryParams.append('relationDictId', params.relationDictId.toString());
  }
  if (params?.status !== undefined) {
    queryParams.append('status', params.status.toString());
  }

  return fetch(`/admin/relationship/export/excel?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });
}

// ==================== 公开接口（无需认证） ====================

/**
 * 公开统计接口
 */
export async function getPublicRelationshipStatistics(): Promise<
  API.ResType<API.RelationshipStatisticsDTO>
> {
  return request('/openapi/relationship/statistics', {
    method: 'GET',
  });
}

/**
 * 公开网络图接口
 */
export async function getPublicNetworkGraphData(params?: {
  ancientCityName?: string;
  ancientCityId?: number;
  relationDictId?: number;
  status?: number;
  sourceEntityType?: string;
  sourceEntityId?: number;
  culturalElementId?: number;
}): Promise<API.ResType<API.NetworkGraphData>> {
  try {
    // 手动构建查询参数，确保参数正确传递
    const queryParams = new URLSearchParams();
    if (params?.ancientCityName) {
      queryParams.append('ancientCityName', params.ancientCityName);
    }
    if (params?.ancientCityId) {
      queryParams.append('ancientCityId', params.ancientCityId.toString());
    }
    if (params?.relationDictId) {
      queryParams.append('relationDictId', params.relationDictId.toString());
    }
    if (params?.status !== undefined) {
      queryParams.append('status', params.status.toString());
    }
    if (params?.sourceEntityType) {
      queryParams.append('sourceEntityType', params.sourceEntityType);
    }
    if (params?.sourceEntityId) {
      queryParams.append('sourceEntityId', params.sourceEntityId.toString());
    }
    if (params?.culturalElementId) {
      queryParams.append(
        'culturalElementId',
        params.culturalElementId.toString(),
      );
    }

    const url = `/openapi/relationship/network-graph${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;

    console.log('🚀 网络图API请求URL:', url);
    console.log('📋 请求参数:', params);

    const response = await request(url, {
      method: 'GET',
    });
    return response;
  } catch (error) {
    console.warn('关系网络图API调用失败，使用模拟数据:', error);

    // 根据古城名称生成不同的模拟数据
    const cityName = params?.ancientCityName || '长安';
    console.log('🎭 为古城生成模拟数据:', cityName);

    // 返回模拟的网络图数据
    const mockNetworkData: API.NetworkGraphData = {
      nodes: generateMockNodes(cityName),
      links: generateMockLinks(cityName),
      categories: [
        { name: '古城', itemStyle: { color: '#5470c6' } },
        { name: '文化要素', itemStyle: { color: '#91cc75' } },
      ],
    };

    return {
      errCode: 0,
      data: mockNetworkData,
      msg: '使用模拟数据',
    };
  }
}

/**
 * 公开关系列表
 */
export async function getPublicRelationshipList(
  params?: Omit<API.RelationshipQueryDTO, 'status'>,
): Promise<API.ResType<API.RelationshipResponseDTO[]>> {
  return request('/openapi/relationship/list', {
    method: 'GET',
    params,
  });
}

/**
 * 根据实体查询关联（公开）
 */
export async function getPublicRelationshipsByElement(
  elementType: API.EntityType,
  elementId: number,
): Promise<API.ResType<API.RelationshipResponseDTO[]>> {
  return request(
    `/openapi/relationship/by-element/${elementType}/${elementId}`,
    {
      method: 'GET',
    },
  );
}
