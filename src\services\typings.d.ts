declare namespace API {
  /**
   * 统一返回信息格式
   */
  type ResType<T> = {
    errCode: number;
    data?: T;
    msg?: string;
  };

  /**
   * 新版本响应格式（备用）
   */
  type NewResType<T> = {
    success: boolean;
    message: string;
    data?: T;
    code?: number;
    timestamp: string;
  };

  interface IResponseDate {
    createdAt: Date;
    updatedAt: Date;
  }

  // ==================== 认证相关类型定义 ====================

  /**
   * 登录参数
   */
  interface LoginParams {
    username: string;
    password: string;
  }

  /**
   * 登录响应
   */
  interface LoginResponse {
    token: string;
    user: {
      id: number;
      username: string;
      role: string;
      enabled: boolean;
      createdAt: string;
      updatedAt: string;
    };
  }

  /**
   * 当前用户信息
   */
  interface CurrentUser {
    id: number;
    username: string;
    role: string;
    permissions: string[];
  }

  /**
   * 通用消息响应
   */
  interface MessageResponse {
    message: string;
  }

  // ==================== 用户管理相关类型定义 ====================

  /**
   * 用户信息类型定义
   */
  interface UserInfo {
    id: number;
    username: string;
    role: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  }

  /**
   * 用户列表响应类型
   */
  interface UserListResponse {
    list: UserInfo[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }

  /**
   * 创建用户参数
   */
  interface CreateUserParams {
    username: string;
    password: string;
    role?: string;
    isActive?: boolean;
  }

  /**
   * 更新用户参数
   */
  interface UpdateUserParams {
    username?: string;
    password?: string;
    role?: string;
    isActive?: boolean;
  }

  /**
   * 获取用户列表参数
   */
  interface GetUserListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
  }

  // ==================== 上传相关类型定义 ====================

  /**
   * 上传文件响应类型
   */
  interface UploadFileResponse {
    url: string;
    filename: string;
    size: number;
    photoId?: number; // 当createPhoto=true时返回
  }

  /**
   * 文件信息类型
   */
  interface FileInfo {
    id: number;
    name: string;
    url: string;
    size?: number;
    type?: string;
    createdAt?: string;
    updatedAt?: string;
  }

  /**
   * 照片记录类型
   */
  interface PhotoRecord {
    id: number;
    name: string;
    url: string;
    path?: string;
    culturalElementId?: number;
    culturalElement?: { id: number; name: string };
    createdAt: string;
    updatedAt: string;
  }

  /**
   * 照片列表响应类型
   */
  interface PhotoListResponse {
    list: PhotoRecord[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 照片统计响应类型
   */
  interface PhotoStatistics {
    total: number;
    culturalElementPhotos: number;
    unassignedPhotos: number;
  }

  /**
   * 上传文件选项
   */
  interface UploadFileOptions {
    photoName?: string;
    entityType?: 'culturalElement';
    entityId?: number;
  }

  /**
   * 创建照片参数
   */
  interface CreatePhotoParams {
    name: string;
    url: string;
    culturalElementId?: number;
  }

  /**
   * 更新照片参数
   */
  interface UpdatePhotoParams {
    name?: string;
    url?: string;
    culturalElementId?: number;
  }

  /**
   * 获取照片列表参数
   */
  interface GetPhotoListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
  }

  // ==================== 历史要素相关类型定义 ====================

  /**
   * 历史要素类型
   */
  interface HistoricalElement {
    id: number;
    name: string;
    code: string;
    typeDictId?: number;
    constructionLongitude?: number;
    constructionLatitude?: number;
    locationDescription?: string;
    constructionYear?: number;
    historicalRecords?: string;
    // 移除 regionDictId，区域信息现在通过 region 字段管理
    region?: string;
    typeDict?: {
      id: number;
      typeName: string;
      typeCode: string;
    };
    // 移除 regionDict，区域信息现在通过 region 字段管理
    photos?: Array<{
      id: number;
      name: string;
      url: string;
    }>;
    createdAt?: string;
    updatedAt?: string;
  }

  /**
   * 创建历史要素参数
   */
  interface CreateHistoricalElementParams {
    name: string;
    code: string;
    typeDictId?: number;
    constructionLongitude?: number;
    constructionLatitude?: number;
    locationDescription?: string;
    constructionYear?: number;
    historicalRecords?: string;
    region?: string; // 替换 regionDictId
  }

  /**
   * 更新历史要素参数
   */
  interface UpdateHistoricalElementParams {
    name?: string;
    code?: string;
    typeDictId?: number;
    constructionLongitude?: number;
    constructionLatitude?: number;
    locationDescription?: string;
    constructionYear?: number;
    historicalRecords?: string;
    region?: string; // 替换 regionDictId
  }

  /**
   * 获取历史要素列表参数
   */
  interface GetHistoricalElementListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    regionId?: number;
    typeId?: number;
  }

  /**
   * 历史要素列表响应
   */
  interface HistoricalElementListResponse {
    list: HistoricalElement[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 批量导入参数
   */
  interface BatchImportParams {
    elements: CreateHistoricalElementParams[];
  }

  /**
   * 模板下载响应
   */
  interface TemplateDownloadResponse {
    downloadUrl?: string;
    filename: string;
    description: string;
    buffer?: string; // base64编码的文件内容
  }

  /**
   * 导出响应
   */
  interface ExportResponse {
    downloadUrl?: string;
    filename?: string;
    type?: string; // 数据类型，如 "Buffer"
    data?: number[]; // 当type为Buffer时，文件数据在此数组中
  }

  // ==================== 要素关联管理相关类型 ====================

  /**
   * 实体类型枚举
   */
  type EntityType = 'ancient_city' | 'cultural_element';

  /**
   * 关联关系DTO
   */
  interface RelationshipDTO {
    id?: number;
    relationDictId?: number;
    parentRelationshipId?: number;
    sourceEntityType: EntityType;
    sourceEntityId: number;
    targetEntityType: EntityType;
    targetEntityId: number;
    direction?: string;
    description?: string;
    historicalRecord?: string;
    sort?: number;
    status?: number;
  }

  /**
   * 关联关系响应DTO
   */
  interface RelationshipResponseDTO {
    id: number;
    relationDictId?: number;
    parentRelationshipId?: number;
    sourceEntityType: EntityType;
    sourceEntityId: number;
    targetEntityType: EntityType;
    targetEntityId: number;
    direction?: string;
    description?: string;
    historicalRecord?: string;
    sort: number;
    status: number;
    createdAt: string;
    updatedAt: string;
    relationDict?: {
      id: number;
      relationName: string;
      relationCode: string;
    };
    sourceElement?: {
      id: number;
      name?: string;
      cityName?: string;
      code?: string;
      cityCode?: string;
    };
    targetElement?: {
      id: number;
      name?: string;
      cityName?: string;
      code?: string;
      cityCode?: string;
    };
  }

  /**
   * 关联关系查询参数
   */
  interface RelationshipQueryDTO {
    page?: number;
    pageSize?: number;
    keyword?: string;
    sourceEntityType?: EntityType;
    targetEntityType?: EntityType;
    relationDictId?: number;
    status?: number;
  }

  /**
   * 批量创建关联关系
   */
  interface BatchCreateRelationshipDTO {
    relations: RelationshipDTO[];
  }

  /**
   * 批量更新状态
   */
  interface BatchUpdateStatusDTO {
    ids: number[];
    status: number;
  }

  /**
   * 关联关系统计概览
   */
  interface RelationshipStatisticsDTO {
    total: number;
    bySourceEntityType: Array<{
      entityType: EntityType;
      entityTypeName: string;
      count: number;
    }>;
    byTargetEntityType: Array<{
      entityType: EntityType;
      entityTypeName: string;
      count: number;
    }>;
    byRelationType: Array<{
      relationId: number;
      relationName: string;
      count: number;
    }>;
    byDirection: Array<{
      direction: string;
      count: number;
    }>;
    byCreateTime: Array<{
      month: string;
      count: number;
    }>;
    topSourceEntities: Array<{
      entityType: EntityType;
      entityId: number;
      entityName: string;
      count: number;
    }>;
    topTargetEntities: Array<{
      entityType: EntityType;
      entityId: number;
      entityName: string;
      count: number;
    }>;
  }

  /**
   * 网络图节点（关系管理）
   */
  interface NetworkNode {
    id: string;
    name: string;
    category: number;
    symbolSize: number;
    size?: number;
    type?: string;
    color?: string;
    itemStyle: {
      color: string;
    };
    label: {
      show: boolean;
    };
  }

  /**
   * 网络图连接（关系管理）
   */
  interface NetworkLink {
    source: string;
    target: string;
    name: string;
    weight?: number;
    lineStyle: {
      width: number;
      curveness: number;
      color?: string;
      type?: string;
      opacity?: number;
    };
    label: {
      show: boolean;
      formatter: string;
      fontSize?: number;
      color?: string;
      backgroundColor?: string;
      borderRadius?: number;
      padding?: number[];
    };
    value: number;
    relationshipData?: {
      id: number;
      relationName: string;
      relationCode: string;
      direction?: string;
      term?: string;
      record?: string;
      sourceElement: string;
      targetElement: string;
    };
  }

  /**
   * 网络图类别（关系管理）
   */
  interface NetworkCategory {
    name: string;
    itemStyle: {
      color: string;
    };
  }

  /**
   * 网络图数据（关系管理）
   */
  interface NetworkGraphData {
    nodes: NetworkNode[];
    links: NetworkLink[];
    categories: NetworkCategory[];
  }

  /**
   * 关联关系导入预览响应
   */
  interface RelationshipImportPreviewResponse {
    totalRows: number;
    validRows: number;
    invalidRows: number;
    previewData: any[];
    errors: ExcelImportError[];
  }

  /**
   * 关联关系导入响应
   */
  interface RelationshipImportResponse {
    success: boolean;
    message: string;
    totalRows: number;
    validRows: number;
    successCount: number;
    failureCount: number;
    errors: ImportError[];
  }

  /**
   * Excel导入错误信息
   */
  interface ExcelImportError {
    row: number;
    field: string;
    value: any;
    message: string;
  }

  /**
   * Excel导入预览响应
   */
  interface ExcelImportPreviewResponse {
    success: boolean;
    message: string;
    totalRows: number;
    validRows: number;
    errors: ExcelImportError[];
    preview: CreateHistoricalElementParams[];
  }

  /**
   * Excel导入响应
   */
  interface ExcelImportResponse {
    success: boolean;
    message: string;
    totalRows: number;
    validRows: number;
    importedCount?: number;
    errors?: ExcelImportError[];
  }

  /**
   * 历史要素统计
   */
  interface HistoricalElementStatistics {
    total: number;
    byType: Array<{
      typeId: number;
      typeName: string;
      count: number;
    }>;
    byRegion: Array<{
      regionId: number;
      regionName: string;
      count: number;
    }>;
    byPeriod: Array<{
      period: string;
      count: number;
    }>;
  }

  /**
   * 时间轴数据
   */
  interface TimelineData {
    year: number;
    count: number;
    elements: Array<{
      id: number;
      name: string;
      type: string;
    }>;
  }

  // ==================== 数字化统计相关类型定义 ====================

  /**
   * 基础统计数据
   */
  interface BasicStatisticsData {
    counts: {
      mountain: number;
      waterSystem: number;
      historicalElement: number;
      user: number;
      typeDict: number;
      relationshipDict: number;
    };
    // 移除区域统计数据
    timelineData: Array<{
      year: number;
      elements: Array<{
        id: number;
        name: string;
        type: string;
      }>;
    }>;
  }

  /**
   * 区域分布统计数据
   */
  interface RegionDistributionData {
    region: string;
    regionId: number;
    mountainCount: number;
    waterSystemCount: number;
    historicalElementCount: number;
    total: number;
  }

  /**
   * 时间轴统计数据
   */
  interface TimelineStatisticsData {
    year: number;
    elements: Array<{
      id: number;
      name: string;
      type: string;
    }>;
  }

  /**
   * 数据概览统计
   */
  interface OverviewStatisticsData {
    totalCounts: {
      mountain: number;
      waterSystem: number;
      historicalElement: number;
      user: number;
      typeDict: number;
      relationshipDict: number;
    };
    // 移除区域统计数据
    timelineData: TimelineStatisticsData[];
  }

  /**
   * 综合统计报告数据
   */
  interface ComprehensiveStatisticsData {
    // 根据后台实际返回结构定义
    [key: string]: any;
  }

  /**
   * 统计查询参数
   */
  interface StatisticsQueryParams {
    regionId?: number;
    startTime?: string;
    endTime?: string;
  }

  /**
   * 获取时间轴参数
   */
  interface GetTimelineParams {
    regionId?: number;
  }

  /**
   * 根据建造时间查询参数（旧版本，保留兼容性）
   */
  interface GetByConstructionTimeParams {
    startTime?: string;
    endTime?: string;
  }

  /**
   * 根据建造年份查询参数
   */
  interface GetByConstructionYearParams {
    startYear?: number;
    endYear?: number;
  }

  // ==================== 山塬管理相关类型定义 ====================

  /**
   * 山塬类型
   */
  interface Mountain {
    id: number;
    name: string;
    code: string;
    typeDictId?: number;
    longitude?: number;
    latitude?: number;
    height: number;
    locationDescription?: string;
    historicalRecords?: string;
    region?: string; // 替换 regionDictId
    typeDict?: {
      id: number;
      typeName: string;
      typeCode: string;
    };
    // 移除 regionDict，区域信息现在通过 region 字段管理
    photos?: Array<{
      id: number;
      name: string;
      url: string;
    }>;
    createdAt?: string;
    updatedAt?: string;
  }

  /**
   * 创建山塬参数
   */
  interface CreateMountainParams {
    name: string;
    code: string;
    typeDictId?: number;
    longitude?: number;
    latitude?: number;
    height: number;
    locationDescription?: string;
    historicalRecords?: string;
    region?: string; // 替换 regionDictId
  }

  /**
   * 更新山塬参数
   */
  interface UpdateMountainParams {
    name?: string;
    code?: string;
    typeDictId?: number;
    longitude?: number;
    latitude?: number;
    height?: number;
    locationDescription?: string;
    historicalRecords?: string;
    region?: string; // 替换 regionDictId
  }

  /**
   * 获取山塬列表参数
   */
  interface GetMountainListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    regionId?: number;
    typeId?: number;
  }

  /**
   * 山塬列表响应
   */
  interface MountainListResponse {
    list: Mountain[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 山塬统计
   */
  interface MountainStatistics {
    total: number;
    byRegion: Array<{
      regionId: number;
      regionName: string;
      count: number;
    }>;
    byHeight: Array<{
      range: string;
      count: number;
    }>;
  }

  // ==================== 水系管理相关类型定义 ====================

  /**
   * 水系类型
   */
  interface WaterSystem {
    id: number;
    name: string;
    code: string;
    typeDictId?: number;
    longitude?: number;
    latitude?: number;
    lengthArea?: string;
    locationDescription?: string;
    historicalRecords?: string;
    region?: string; // 替换 regionDictId
    typeDict?: {
      id: number;
      typeName: string;
      typeCode: string;
    };
    // 移除 regionDict，区域信息现在通过 region 字段管理
    photos?: Array<{
      id: number;
      name: string;
      url: string;
    }>;
    createdAt?: string;
    updatedAt?: string;
  }

  /**
   * 创建水系参数
   */
  interface CreateWaterSystemParams {
    name: string;
    code: string;
    typeDictId?: number;
    longitude?: number;
    latitude?: number;
    lengthArea?: string;
    locationDescription?: string;
    historicalRecords?: string;
    region?: string; // 替换 regionDictId
  }

  /**
   * 更新水系参数
   */
  interface UpdateWaterSystemParams {
    name?: string;
    code?: string;
    typeDictId?: number;
    longitude?: number;
    latitude?: number;
    lengthArea?: string;
    locationDescription?: string;
    historicalRecords?: string;
    region?: string; // 替换 regionDictId
  }

  /**
   * 获取水系列表参数
   */
  interface GetWaterSystemListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    regionId?: number;
    typeId?: number;
  }

  /**
   * 水系列表响应
   */
  interface WaterSystemListResponse {
    list: WaterSystem[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 水系统计
   */
  interface WaterSystemStatistics {
    total: number;
    totalLength: number;
    byRegion: Array<{
      regionId: number;
      regionName: string;
      count: number;
    }>;
    byLengthArea: Array<{
      range: string;
      count: number;
    }>;
  }

  /**
   * 获取统计参数
   */
  interface GetStatisticsParams {
    regionId?: number;
    typeId?: number;
  }

  // ==================== 字典管理相关类型定义 ====================

  // 通用字典基础类型
  interface BaseDictionary {
    id: number;
    status: number;
    sort: number;
    parentId?: number | null;
    createdAt?: string;
    updatedAt?: string;
  }

  // 区域字典类型（已废弃，区域信息现在通过古城字典的 region 字段管理）

  // 类型字典类型
  interface TypeDictionary extends BaseDictionary {
    typeCode: string;
    typeName: string;
    typeDesc?: string;
    parent?: TypeDictionary | null;
    children?: TypeDictionary[];
  }

  // 关系字典类型
  interface RelationshipDictionary extends BaseDictionary {
    relationCode: string;
    relationName: string;
    relationDesc?: string;
    parent?: RelationshipDictionary | null;
    children?: RelationshipDictionary[];
  }

  // 字典列表查询参数
  interface DictionaryListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: number;
    parentId?: number;
  }

  // 字典列表响应类型
  interface DictionaryListResponse<T> {
    list: T[];
    total: number;
    page: number;
    pageSize: number;
  }

  // 批量状态更新参数
  interface BatchStatusUpdateParams {
    ids: number[];
    status: number;
  }

  // 状态切换响应
  interface StatusToggleResponse {
    status: number;
    message: string;
  }

  // 区域字典相关接口（已废弃，区域信息现在通过古城字典的 region 字段管理）

  // 类型字典数据类型
  interface TypeDict {
    id: number;
    typeCode: string;
    typeName: string;
    parentId?: number | null;
    status: number;
    sort: number;
    typeDesc?: string;
    createdAt?: string;
    updatedAt?: string;
    parent?: TypeDict | null;
    children?: TypeDict[];
  }

  // 类型字典创建参数
  interface CreateTypeDictParams {
    typeCode: string;
    typeName: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    typeDesc?: string;
  }

  // 类型字典更新参数
  interface UpdateTypeDictParams {
    typeCode?: string;
    typeName?: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    typeDesc?: string;
  }

  // 类型字典列表查询参数
  interface GetTypeDictListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: number;
    parentId?: number;
  }

  // 类型字典列表响应
  interface TypeDictListResponse {
    list: TypeDict[];
    total: number;
    page: number;
    pageSize: number;
  }

  // 关系字典数据类型
  interface RelationshipDict {
    id: number;
    relationCode: string;
    relationName: string;
    parentId?: number | null;
    status: number;
    sort: number;
    relationDesc?: string;
    region?: string; // 新增：区域信息（用于古城）
    createdAt?: string;
    updatedAt?: string;
    parent?: RelationshipDict | null;
    children?: RelationshipDict[];
  }

  // 关系字典创建参数
  interface CreateRelationshipDictParams {
    relationCode: string;
    relationName: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    relationDesc?: string;
    region?: string; // 新增：区域信息
  }

  // 关系字典更新参数
  interface UpdateRelationshipDictParams {
    relationCode?: string;
    relationName?: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    relationDesc?: string;
    region?: string; // 新增：区域信息
  }

  // 关系字典列表查询参数
  interface GetRelationshipDictListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: number;
    parentId?: number;
  }

  // 关系字典列表响应
  interface RelationshipDictListResponse {
    list: RelationshipDict[];
    total: number;
    page: number;
    pageSize: number;
  }

  // ==================== 要素关联管理相关类型定义 ====================

  /**
   * 要素关联类型
   */
  interface ElementRelation {
    id: number;
    relationDictId?: number;
    parentRelationshipId?: number;
    sourceType: 'ancient_city' | 'cultural_element';
    sourceId: number;
    targetEntityType: 'ancient_city' | 'cultural_element' | 'type_dict';
    targetId: number;
    direction?: string;
    term?: string;
    record?: string;
    sort?: number;
    status: number;
    createdAt?: string;
    updatedAt?: string;
    relationDict?: {
      id: number;
      relationName: string;
      relationCode: string;
    };
    parent?: {
      id: number;
      term?: string;
      record?: string;
    };
    children?: ElementRelation[];
    sourceElement?: {
      id: number;
      name: string;
      cityName: string;
      typeName: string;
      code: string;
    };
    targetElement?: {
      id: number;
      name: string;
      cityName: string;
      typeName: string;
      code: string;
    };
  }

  /**
   * 创建要素关联参数
   */
  interface CreateElementRelationParams {
    relationDictId?: number;
    parentRelationshipId?: number;
    sourceType: 'ancient_city' | 'cultural_element';
    sourceId: number;
    targetEntityType: 'ancient_city' | 'cultural_element' | 'type_dict';
    targetId: number;
    direction?: string;
    term?: string;
    record?: string;
    sort?: number;
    status?: number;
  }

  /**
   * 更新要素关联参数
   */
  interface UpdateElementRelationParams {
    relationDictId?: number;
    parentRelationshipId?: number;
    sourceType?: 'ancient_city' | 'cultural_element';
    sourceId?: number;
    targetEntityType?: 'ancient_city' | 'cultural_element' | 'type_dict';
    targetId?: number;
    direction?: string;
    term?: string;
    record?: string;
    sort?: number;
    status?: number;
  }

  /**
   * 获取要素关联列表参数
   */
  interface GetElementRelationListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    relationDictId?: number;
    parentRelationshipId?: number;
    sourceType?: string;
    sourceId?: number;
    targetEntityType?: string;
    targetId?: number;
    direction?: string;
    status?: number;
    ancientCityId?: number; // 新增：按古城筛选
  }

  /**
   * 要素关联列表响应
   */
  interface ElementRelationListResponse {
    list: ElementRelation[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 要素关联统计参数
   */
  interface GetElementRelationStatisticsParams {
    sourceType?: string;
    targetType?: string;
    relationDictId?: number;
    status?: number;
  }

  /**
   * 要素关联统计
   */
  interface ElementRelationStatistics {
    total: number;
    bySourceType: Array<{
      sourceType: string;
      count: number;
    }>;
    byTargetEntityType: Array<{
      targetEntityType: string;
      count: number;
    }>;
    byRelationType: Array<{
      relationId: number;
      relationName: string;
      count: number;
    }>;
    byDirection: Array<{
      direction: string;
      count: number;
    }>;
  }

  /**
   * 网络图数据参数
   */
  interface GetNetworkGraphParams {
    sourceType?: string;
    targetType?: string;
    relationDictId?: number;
    status?: number;
  }

  /**
   * 网络图节点（按照后台API响应格式）
   */
  interface NetworkGraphNode {
    id: string;
    name: string;
    type: string;
    category: string;
    size: number;
    color: string;
    x?: number;
    y?: number;
    // 兼容ECharts格式的属性
    symbolSize?: number;
    itemStyle?: {
      color: string;
    };
    label?: {
      show: boolean;
      fontSize?: number;
      fontWeight?: string;
    };
  }

  /**
   * 网络图连线（按照后台API响应格式）
   */
  interface NetworkGraphLink {
    source: string;
    target: string;
    relation: string;
    relationName?: string; // 后端API返回的关系名称字段
    relationshipData?: {
      // 完整的关系数据对象
      relationName?: string; // 关系类型名称，用于分组
      [key: string]: any;
    };
    direction: 'unidirectional' | 'bidirectional';
    term: string;
    weight: number;
    color: string;
    // 兼容ECharts格式的属性
    name?: string;
    value?: number;
    lineStyle?: {
      width: number;
      curveness: number;
      color?: string;
      type?: string;
      opacity?: number;
    };
    label?: {
      show: boolean;
      formatter?: string;
      fontSize?: number;
      color?: string;
    };
  }

  /**
   * 网络图类别（按照后台API响应格式）
   */
  interface NetworkGraphCategory {
    name: string;
    color: string;
  }

  /**
   * 网络图数据（按照后台API响应格式）
   */
  interface NetworkGraphData {
    nodes: NetworkGraphNode[];
    links: NetworkGraphLink[];
    categories: NetworkGraphCategory[];
  }

  // ==================== 要素关联导入相关类型定义 ====================

  /**
   * 关系导入模板下载响应
   */
  interface RelationshipTemplateDownloadResponse {
    downloadUrl: string;
    filename: string;
    description: string;
    buffer?: string; // base64编码的文件内容
  }

  /**
   * 关系导入错误信息
   */
  interface RelationshipImportError {
    row: number;
    field: string;
    value: any;
    message: string;
  }

  /**
   * 关系导入预览数据项
   */
  interface RelationshipImportPreviewItem {
    relationName?: string;
    sourceType: string;
    sourceName: string;
    targetEntityType: string;
    targetName: string;
    direction?: string;
    term?: string;
    record?: string;
    sort?: number;
  }

  /**
   * 关系导入预览响应
   */
  interface RelationshipImportPreviewResponse {
    success: boolean;
    message: string;
    data?: RelationshipImportPreviewItem[];
    preview?: RelationshipImportPreviewItem[]; // 实际的预览数据字段
    totalRows: number;
    validRows: number;
    previewCount?: number;
    errors?: RelationshipImportError[];
  }

  /**
   * 关系导入执行错误详情
   */
  interface RelationshipImportExecuteError {
    index: number;
    error: string;
    data: {
      sourceName?: string;
      sourceType?: string;
      targetName?: string;
      targetType?: string;
      [key: string]: any;
    };
  }

  /**
   * 关系导入执行响应
   */
  interface RelationshipImportExecuteResponse {
    success: boolean;
    message: string;
    totalRows: number;
    validRows: number;
    successCount: number;
    failureCount: number;
    errors?: RelationshipImportExecuteError[];
  }

  /**
   * 关系批量导入响应
   */
  interface RelationshipBatchImportResponse {
    message: string;
    successCount: number;
    failureCount: number;
    errors: RelationshipImportExecuteError[];
  }

  // ==================== 文化要素统一管理相关类型定义 ====================

  /**
   * 文化要素类型（统一后的数据结构）
   */
  interface CulturalElement {
    /** ID */
    id: number;
    /** 名称 */
    name: string;
    /** 编号 */
    code: string;
    /** 所属类型ID */
    typeDictId: number;
    /** 所属古城ID */
    ancientCityId: number;

    // 统一的位置信息
    /** 经度 */
    longitude?: number;
    /** 纬度 */
    latitude?: number;

    // 原山塬特有字段
    /** 高度（米） */
    height?: number;

    // 原水系特有字段
    /** 长度/面积 */
    lengthArea?: string;

    // 原历史要素特有字段
    /** 位置描述 */
    locationDescription?: string;
    /** 建造年份（支持公元前，负数表示公元前） */
    constructionYear?: number;

    // 通用字段
    /** 历史记载 */
    historicalRecords?: string;
    /** 描述信息 */
    description?: string;
    /** 状态 */
    status?: number;
    /** 排序号 */
    sort?: number;

    // 系统字段
    createdAt: Date;
    updatedAt: Date;

    // 关联数据
    typeName?: string;
    cityName?: string;
    ancientCityName?: string;
    typeDict?: {
      id: number;
      typeName: string;
      typeCode: string;
    };
    ancientCity?: {
      id: number;
      name: string;
      code: string;
      region?: string; // 古城的区域信息
    };
    photos?: Array<{
      id: number;
      name: string;
      url: string;
    }>;
  }

  /**
   * 创建文化要素参数
   */
  interface CreateCulturalElementParams {
    /** 名称 */
    name: string;
    /** 编号 */
    code: string;
    /** 所属类型ID */
    typeDictId: number;
    /** 所属古城ID */
    ancientCityId?: number;

    // 统一的位置信息
    /** 经度 */
    longitude?: number;
    /** 纬度 */
    latitude?: number;

    // 原山塬特有字段
    /** 高度（米） */
    height?: number;

    // 原水系特有字段
    /** 长度/面积 */
    lengthArea?: string;

    // 原历史要素特有字段
    /** 位置描述 */
    locationDescription?: string;
    /** 建造年份（支持公元前，负数表示公元前） */
    constructionYear?: number;

    // 通用字段
    /** 历史记载 */
    historicalRecords?: string;
    /** 描述信息 */
    description?: string;
    /** 状态 */
    status?: number;
    /** 排序号 */
    sort?: number;
  }

  /**
   * 更新文化要素参数
   */
  interface UpdateCulturalElementParams {
    /** 名称 */
    name?: string;
    /** 编号 */
    code?: string;
    /** 所属类型ID */
    typeDictId?: number;
    /** 所属古城ID */
    ancientCityId?: number;

    // 统一的位置信息
    /** 经度 */
    longitude?: number;
    /** 纬度 */
    latitude?: number;

    // 原山塬特有字段
    /** 高度（米） */
    height?: number;

    // 原水系特有字段
    /** 长度/面积 */
    lengthArea?: string;

    // 原历史要素特有字段
    /** 位置描述 */
    locationDescription?: string;
    /** 建造年份（支持公元前，负数表示公元前） */
    constructionYear?: number;

    // 通用字段
    /** 历史记载 */
    historicalRecords?: string;
    /** 描述信息 */
    description?: string;
    /** 状态 */
    status?: number;
    /** 排序号 */
    sort?: number;
  }

  /**
   * 文化要素查询参数
   */
  interface CulturalElementQueryDTO {
    page?: number;
    pageSize?: number;
    name?: string;
    code?: string;
    typeDictId?: number;
    region?: string; // 新增：按区域名称查询
    ancientCityId?: number;
    constructionYear?: number;
    constructionYearStart?: number;
    constructionYearEnd?: number;
    keyword?: string;
  }

  /**
   * 文化要素列表响应
   */
  interface CulturalElementListResponse {
    list: CulturalElement[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 文化要素统计数据
   */
  interface CulturalElementStatistics {
    total: number;
    byType: Array<{
      typeDictId: number;
      typeName: string;
      count: number;
    }>;
    byRegion: Array<{
      regionName: string;
      count: number;
    }>;
    byAncientCity: Array<{
      ancientCityId: number;
      cityName: string;
      count: number;
    }>;
    byConstructionYear: Array<{
      year: number;
      count: number;
    }>;
  }

  /**
   * 批量创建文化要素参数
   */
  interface BatchCreateCulturalElementDTO {
    elements: CreateCulturalElementParams[];
  }

  /**
   * 文化要素导入预览响应
   */
  interface CulturalElementImportPreviewResponse {
    success: boolean;
    message: string;
    totalRows: number;
    validRows: number;
    errors: ExcelImportError[];
    preview: CreateCulturalElementParams[];
  }

  /**
   * 文化要素导入执行响应
   */
  interface CulturalElementImportResponse {
    success: boolean;
    message: string;
    totalRows: number;
    validRows: number;
    importedCount?: number;
    errors?: ExcelImportError[];
  }

  /**
   * 地图数据查询参数
   */
  interface MapDataQueryDTO {
    typeDictId?: number;
    region?: string; // 改为区域名称查询
    ancientCityId?: number;
    constructionYear?: number;
  }

  /**
   * 地图标记点数据
   */
  interface MapMarkerData {
    id: number;
    name: string;
    code: string;
    type: string; // 兼容旧版本的类型字段
    typeName: string;
    longitude: number;
    latitude: number;
    regionName?: string;
    ancientCityName?: string;
    constructionYear?: number;
    extData: {
      type: 'mountain' | 'waterSystem' | 'historicalElement'; // 兼容字段
      typeDictId?: number;
      region?: string; // 替换 regionDictId
      ancientCityId?: number;
    };
  }

  /**
   * 地图数据响应
   */
  interface MapDataResponse {
    markers: MapMarkerData[];
    statistics: {
      total: number;
      byType: Array<{
        type: string;
        typeName: string;
        count: number;
      }>;
    };
  }

  /**
   * 文化要素分组数据
   */
  interface CulturalElementGroupedData {
    [typeName: string]: CulturalElement[];
  }

  /**
   * 搜索文化要素参数
   */
  interface SearchCulturalElementParams {
    keyword?: string;
    page?: number;
    pageSize?: number;
  }

  /**
   * 热门/推荐文化要素参数
   */
  interface PopularCulturalElementParams {
    limit?: number;
  }

  // ==================== 古城字典相关类型定义 ====================

  /**
   * 古城字典基础接口
   */
  interface AncientCityDict {
    id: number;
    cityCode: string;
    cityName: string;
    parentId?: number;
    region?: string; // 所属区域
    status: number;
    sort: number;
    cityDesc?: string;
    establishedYear?: number;
    locationDesc?: string;
    longitude?: number;
    latitude?: number;
    createdAt: Date;
    updatedAt: Date;
    children?: AncientCityDict[];
  }

  /**
   * 创建古城字典参数
   */
  interface CreateAncientCityDictParams {
    cityCode: string;
    cityName: string;
    parentId?: number;
    region?: string;
    status?: number;
    sort?: number;
    cityDesc?: string;
    establishedYear?: number;
    locationDesc?: string;
    longitude?: number;
    latitude?: number;
  }

  /**
   * 更新古城字典参数
   */
  interface UpdateAncientCityDictParams {
    cityCode?: string;
    cityName?: string;
    parentId?: number;
    region?: string;
    status?: number;
    sort?: number;
    cityDesc?: string;
    establishedYear?: number;
    locationDesc?: string;
    longitude?: number;
    latitude?: number;
  }

  /**
   * 获取古城字典列表参数
   */
  interface GetAncientCityDictListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    region?: string;
    status?: number;
    parentId?: number;
  }

  /**
   * 古城字典列表响应
   */
  interface AncientCityDictListResponse {
    list: AncientCityDict[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 古城字典统计
   */
  interface AncientCityDictStatistics {
    total: number;
    byRegion: Array<{
      region: string;
      count: number;
    }>;
    byStatus: Array<{
      status: number;
      statusName: string;
      count: number;
    }>;
  }
}
