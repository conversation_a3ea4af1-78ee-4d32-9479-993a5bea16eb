/**
 * @file 字典操作管理Hook
 * @description 处理字典的增删改查操作，包括创建、更新、删除、状态切换、批量操作等
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import {
  batchUpdateRelationshipDictStatus,
  batchUpdateTypeDictStatus,
  createRelationshipDict,
  createTypeDict,
  deleteRelationshipDict,
  deleteTypeDict,
  toggleRelationshipDictStatus,
  toggleTypeDictStatus,
  updateRelationshipDict,
  updateTypeDict,
} from '@/services/dictionary';
import { message } from 'antd';
import { useCallback, useState } from 'react';
import { MESSAGES } from '../constants';
import type { BatchUpdateParams, DictFormData, DictType } from '../dict-types';

export const useDictOperations = () => {
  const [loading, setLoading] = useState(false);
  const [batchLoading, setBatchLoading] = useState(false);

  // 创建字典项
  const createDict = useCallback(
    async (type: DictType, values: DictFormData) => {
      try {
        setLoading(true);

        // 处理parentId为null的情况
        if (values.parentId === 0 || !values.parentId) {
          values.parentId = null;
        }

        let response;
        switch (type) {
          case 'type':
            response = await createTypeDict(values as any);
            break;
          case 'relation':
            response = await createRelationshipDict(values as any);
            break;
          default:
            return { success: false, message: '未知的字典类型' };
        }

        if (response.errCode === 0) {
          message.success(MESSAGES.success.add);
          return { success: true };
        } else {
          message.error(response.msg || MESSAGES.error.add);
          return { success: false, message: response.msg };
        }
      } catch (error) {
        message.error(MESSAGES.error.add);
        return { success: false, message: MESSAGES.error.add };
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 更新字典项
  const updateDict = useCallback(
    async (type: DictType, id: number, values: DictFormData) => {
      try {
        setLoading(true);

        // 处理parentId为null的情况
        if (values.parentId === 0 || !values.parentId) {
          values.parentId = null;
        }

        let response;
        switch (type) {
          case 'type':
            response = await updateTypeDict(id, values);
            break;
          case 'relation':
            response = await updateRelationshipDict(id, values);
            break;
          default:
            return { success: false, message: '未知的字典类型' };
        }

        if (response.errCode === 0) {
          message.success(MESSAGES.success.edit);
          return { success: true };
        } else {
          message.error(response.msg || MESSAGES.error.edit);
          return { success: false, message: response.msg };
        }
      } catch (error) {
        message.error(MESSAGES.error.edit);
        return { success: false, message: MESSAGES.error.edit };
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 删除字典项
  const deleteDict = useCallback(async (type: DictType, id: number) => {
    try {
      let response;
      switch (type) {
        case 'type':
          response = await deleteTypeDict(id);
          break;
        case 'relation':
          response = await deleteRelationshipDict(id);
          break;
        default:
          return { success: false, message: '未知的字典类型' };
      }

      if (response.errCode === 0) {
        message.success(MESSAGES.success.delete);
        return { success: true };
      } else {
        message.error(response.msg || MESSAGES.error.delete);
        return { success: false, message: response.msg };
      }
    } catch (error) {
      message.error(MESSAGES.error.delete);
      return { success: false, message: MESSAGES.error.delete };
    }
  }, []);

  // 切换状态
  const toggleStatus = useCallback(async (type: DictType, id: number) => {
    try {
      let response;
      switch (type) {
        case 'type':
          response = await toggleTypeDictStatus(id);
          break;
        case 'relation':
          response = await toggleRelationshipDictStatus(id);
          break;
        default:
          return { success: false, message: '未知的字典类型' };
      }

      if (response.errCode === 0) {
        message.success(
          response.data?.message || MESSAGES.success.statusUpdate,
        );
        return { success: true };
      } else {
        message.error(response.msg || MESSAGES.error.statusUpdate);
        return { success: false, message: response.msg };
      }
    } catch (error) {
      message.error(MESSAGES.error.statusUpdate);
      return { success: false, message: MESSAGES.error.statusUpdate };
    }
  }, []);

  // 批量更新状态
  const batchUpdateStatus = useCallback(
    async (type: DictType, params: BatchUpdateParams) => {
      try {
        setBatchLoading(true);
        let response;

        switch (type) {
          case 'type':
            response = await batchUpdateTypeDictStatus(params);
            break;
          case 'relation':
            response = await batchUpdateRelationshipDictStatus(params);
            break;
          default:
            return { success: false, message: '未知的字典类型' };
        }

        if (response.errCode === 0) {
          const successMessage =
            params.status === 1
              ? MESSAGES.success.batchEnable
              : MESSAGES.success.batchDisable;
          message.success(successMessage);
          return { success: true };
        } else {
          message.error(response.msg || MESSAGES.error.batchOperation);
          return { success: false, message: response.msg };
        }
      } catch (error) {
        message.error(MESSAGES.error.batchOperation);
        return { success: false, message: MESSAGES.error.batchOperation };
      } finally {
        setBatchLoading(false);
      }
    },
    [],
  );

  return {
    loading,
    batchLoading,
    createDict,
    updateDict,
    deleteDict,
    toggleStatus,
    batchUpdateStatus,
  };
};
