// 简化版文化要素适配器 - 只支持古城类型

import {
  getPublicCulturalElementDetail,
  getPublicCulturalElementList,
  getPublicCulturalElementStatistics,
} from '@/services/culturalElement';
import { getAncientCityDictTree } from '@/services/dictionary';
// 移除不存在的统计服务导入
import { getPublicAncientCityMapData } from '@/services/ancientCity';

// ==================== 类型定义 ====================

export interface Coordinate {
  longitude: number;
  latitude: number;
}

// ==================== 简化：只支持古城类型 ====================

/**
 * 类型名称映射（简化版）
 */
export const TYPE_NAME_MAPPING = {
  ancientCity: '古城',
} as const;

// ==================== 门户数据适配器 ====================

/**
 * 门户概览数据适配器 - 简化版
 */
export const getPortalOverviewAdapter = async (): Promise<API.ResType<any>> => {
  try {
    // 使用新的统一统计接口
    const response = await getPublicCulturalElementStatistics();

    if (response.errCode === 0 && response.data) {
      // 转换为门户概览格式
      const portalData = {
        totalElements: 0,
        ancientCities: 0,
        // 移除区域分布数据
        recentData: {
          mountains: [],
          waterSystems: [],
          historicalElements: [],
        },
      };

      return {
        errCode: 0,
        data: portalData,
      };
    }

    return response as any;
  } catch (error) {
    console.error('门户概览数据适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

// ==================== 古城地图标记点适配器 ====================

/**
 * 古城地图标记点适配器 - 获取古城节点用于首页地图显示
 */
export const getAncientCityMapMarkersAdapter = async (params?: {
  region?: string;
}): Promise<API.ResType<any[]>> => {
  try {
    // 首先尝试使用地图接口获取古城数据
    try {
      const mapResponse = await getPublicAncientCityMapData(params);
      if (mapResponse.errCode === 0 && mapResponse.data) {
        console.log('🏛️ 使用古城地图接口数据');

        // 转换为门户地图标记点格式
        const ancientCityMarkers = mapResponse.data.map(
          (marker: API.MapMarkerData) => {
            return {
              id: marker.id,
              type: 'ancientCity',
              name: marker.name,
              longitude: marker.longitude,
              latitude: marker.latitude,
              thumbnailUrl: undefined, // MapMarkerData 中没有 thumbnailUrl 字段
              summary: `${marker.regionName || ''} | ${
                marker.ancientCityName || ''
              }`,
              extData: {
                ...marker.extData,
                type: 'ancientCity', // 覆盖原有的 type 字段
                region: marker.regionName,
                ancientCityName: marker.ancientCityName,
              },
            };
          },
        );

        return {
          errCode: 0,
          data: ancientCityMarkers,
        };
      }
    } catch (mapError) {
      console.warn('古城地图接口调用失败，尝试使用字典接口:', mapError);
    }

    // 如果地图接口失败，使用字典树接口作为备选
    console.log('🏛️ 使用古城字典树数据');
    const treeResponse = await getAncientCityDictTree();

    if (treeResponse.errCode === 0 && treeResponse.data) {
      // 扁平化字典树数据，获取所有古城节点
      const flattenCities = (nodes: any[]): any[] => {
        let cities: any[] = [];
        nodes.forEach((node) => {
          if (node.longitude && node.latitude) {
            cities.push(node);
          }
          if (node.children && node.children.length > 0) {
            cities = cities.concat(flattenCities(node.children));
          }
        });
        return cities;
      };

      const allCities = flattenCities(treeResponse.data);

      // 根据区域过滤
      const filteredCities = params?.region
        ? allCities.filter((city) => city.region === params.region)
        : allCities;

      // 转换为门户地图标记点格式
      const ancientCityMarkers = filteredCities.map((city) => {
        return {
          id: city.id,
          type: 'ancientCity',
          name: city.cityName,
          longitude: city.longitude!,
          latitude: city.latitude!,
          thumbnailUrl: undefined, // 字典数据中可能没有缩略图
          summary: `${city.region || ''} | ${city.cityCode}`,
          extData: {
            type: 'ancientCity',
            region: city.region,
            cityCode: city.cityCode,
            cityDesc: city.cityDesc, // 使用 cityDesc 而不是 description
            parentId: city.parentId,
            establishedYear: city.establishedYear,
            locationDesc: city.locationDesc,
          },
        };
      });

      return {
        errCode: 0,
        data: ancientCityMarkers,
      };
    }

    return {
      errCode: -1,
      msg: '获取古城数据失败',
    };
  } catch (error) {
    console.error('古城地图标记点适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

// ==================== 兼容性适配器（简化版） ====================

/**
 * 文化要素列表查询适配器（兼容性）
 */
export const getCulturalElementListAdapter = async (
  params?: any,
): Promise<API.ResType<any>> => {
  try {
    return await getPublicCulturalElementList(params);
  } catch (error) {
    console.error('文化要素列表查询适配器错误:', error);
    return { errCode: -1, msg: '查询失败' };
  }
};

/**
 * 文化要素详情查询适配器（兼容性）
 */
export const getCulturalElementDetailAdapter = async (
  id: number,
): Promise<API.ResType<any>> => {
  try {
    return await getPublicCulturalElementDetail(id);
  } catch (error) {
    console.error('文化要素详情查询适配器错误:', error);
    return { errCode: -1, msg: '查询失败' };
  }
};

/**
 * 基础统计数据适配器
 */
export const getBasicStatisticsAdapter = async (params?: {
  regionId?: number;
  startTime?: string;
  endTime?: string;
}): Promise<API.ResType<any>> => {
  try {
    const { getBasicStatistics } = await import('@/services/portal');
    const response = await getBasicStatistics(params);

    if (response.errCode === 0 && response.data) {
      // 转换实际API响应格式为接口文档定义的格式
      const apiData = response.data;
      const culturalElements = apiData.culturalElements || {};
      const relationships = apiData.relationships || {};
      const summary = apiData.summary || {};
      const byAncientCity = culturalElements.byAncientCity || [];
      const byConstructionYear = culturalElements.byConstructionYear || [];

      // 转换类型统计 - 使用新的字段名称，基于实际API数据
      const counts = {
        ancientCity: summary.ancientCityCount || 0,
        culturalElement: summary.totalCulturalElements || 0,
        relationship: summary.totalRelationships || 0,
        user: 0, // 暂时设为0，API中没有用户统计
        typeDict: summary.typeCount || 0,
        regionDict: summary.ancientCityCount || 0,
        relationshipDict: relationships.byRelationType?.length || 0,
      };

      // 转换区域统计 - 使用新的字段名称
      const regionStats = byAncientCity.map((city: any) => ({
        region: city.cityName,
        regionId: city.cityId || 0,
        ancientCityCount: 1, // 每个古城计为1
        culturalElementCount: city.count || 0,
        relationshipCount: 0, // 需要更详细的API数据
        total: (city.count || 0) + 1, // 文化要素数量 + 古城本身
      }));

      // 转换时间轴数据 - 使用新的类型名称
      const timelineData = byConstructionYear.map((year: any) => ({
        year: year.year,
        elements: Array(year.count)
          .fill(null)
          .map((_, index) => ({
            id: `${year.year}_${index}`,
            name: `${year.year}年建造的要素${index + 1}`,
            type: 'culturalElement',
          })),
      }));

      const transformedData = {
        counts,
        regionStats,
        timelineData,
      };

      return {
        errCode: 0,
        data: transformedData,
        msg: response.msg,
      };
    }

    return response;
  } catch (error) {
    console.error('基础统计数据适配器错误:', error);
    return { errCode: -1, msg: '查询失败' };
  }
};

/**
 * 概览统计数据适配器
 */
export const getOverviewStatisticsAdapter = async (params?: {
  regionId?: number;
}): Promise<API.ResType<any>> => {
  try {
    const { getOverviewStatistics } = await import('@/services/portal');
    return await getOverviewStatistics(params);
  } catch (error) {
    console.error('概览统计数据适配器错误:', error);
    return { errCode: -1, msg: '查询失败' };
  }
};

/**
 * 综合统计数据适配器
 */
export const getComprehensiveStatisticsAdapter = async (params?: {
  regionId?: number;
}): Promise<API.ResType<any>> => {
  try {
    const { getComprehensiveStatistics } = await import('@/services/portal');
    return await getComprehensiveStatistics(params);
  } catch (error) {
    console.error('综合统计数据适配器错误:', error);
    return { errCode: -1, msg: '查询失败' };
  }
};

/**
 * 时间轴统计数据适配器
 */
export const getTimelineStatisticsAdapter = async (params?: {
  regionId?: number;
}): Promise<API.ResType<any>> => {
  try {
    const { getTimelineStatistics } = await import('@/services/portal');
    return await getTimelineStatistics(params);
  } catch (error) {
    console.error('时间轴统计数据适配器错误:', error);
    return { errCode: -1, msg: '查询失败' };
  }
};

/**
 * 区域分布统计数据适配器
 */
export const getRegionDistributionStatisticsAdapter = async (): Promise<
  API.ResType<any>
> => {
  try {
    const { getRegionDistributionStatistics } = await import(
      '@/services/portal'
    );
    return await getRegionDistributionStatistics();
  } catch (error) {
    console.error('区域分布统计数据适配器错误:', error);
    return { errCode: -1, msg: '查询失败' };
  }
};
