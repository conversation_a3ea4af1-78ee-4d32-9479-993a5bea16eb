/**
 * 要素关联管理演示数据
 * 用于开发和测试阶段的数据展示
 */

// 模拟网络图数据
export const mockNetworkGraphData: API.NetworkGraphData = {
  nodes: [
    // 山塬节点
    {
      id: 'mountain_1',
      name: '骊山',
      type: 'mountain',
      category: '山塬',
      size: 15,
      color: '#8B4513',
    },
    {
      id: 'mountain_2',
      name: '华山',
      type: 'mountain',
      category: '山塬',
      size: 20,
      color: '#8B4513',
    },
    {
      id: 'mountain_3',
      name: '终南山',
      type: 'mountain',
      category: '山塬',
      size: 18,
      color: '#8B4513',
    },
    // 水系节点
    {
      id: 'water_system_1',
      name: '渭河',
      type: 'water_system',
      category: '水系',
      size: 25,
      color: '#4169E1',
    },
    {
      id: 'water_system_2',
      name: '灞河',
      type: 'water_system',
      category: '水系',
      size: 12,
      color: '#4169E1',
    },
    {
      id: 'water_system_3',
      name: '浐河',
      type: 'water_system',
      category: '水系',
      size: 10,
      color: '#4169E1',
    },
    // 历史要素节点
    {
      id: 'historical_element_1',
      name: '大雁塔',
      type: 'historical_element',
      category: '历史要素',
      size: 16,
      color: '#DC143C',
    },
    {
      id: 'historical_element_2',
      name: '兵马俑',
      type: 'historical_element',
      category: '历史要素',
      size: 22,
      color: '#DC143C',
    },
    {
      id: 'historical_element_3',
      name: '华清池',
      type: 'historical_element',
      category: '历史要素',
      size: 14,
      color: '#DC143C',
    },
    {
      id: 'historical_element_4',
      name: '大明宫',
      type: 'historical_element',
      category: '历史要素',
      size: 18,
      color: '#DC143C',
    },
  ],
  links: [
    // 山塬与水系的关系
    {
      source: 'mountain_1',
      target: 'water_system_2',
      relation: '选址关联',
      direction: '北有',
      term: '北有',
      weight: 2,
      color: '#4169E1',
    },
    {
      source: 'mountain_2',
      target: 'water_system_1',
      relation: '选址关联',
      direction: '北连',
      term: '北连',
      weight: 3,
      color: '#4169E1',
    },
    {
      source: 'mountain_3',
      target: 'water_system_1',
      relation: '选址关联',
      direction: '南有',
      term: '南有',
      weight: 2,
      color: '#4169E1',
    },
    // 历史要素与山塬的关系
    {
      source: 'historical_element_2',
      target: 'mountain_1',
      relation: '选址关联',
      direction: '依山而建',
      term: '依山而建',
      weight: 3,
      color: '#4169E1',
    },
    {
      source: 'historical_element_3',
      target: 'mountain_1',
      relation: '选址关联',
      direction: '山下',
      term: '山下',
      weight: 2,
      color: '#4169E1',
    },
    // 历史要素与水系的关系
    {
      source: 'historical_element_1',
      target: 'water_system_3',
      relation: '选址关联',
      direction: '临水',
      term: '临水',
      weight: 2,
      color: '#4169E1',
    },
    {
      source: 'historical_element_4',
      target: 'water_system_2',
      relation: '选址关联',
      direction: '东临',
      term: '东临',
      weight: 2,
      color: '#4169E1',
    },
    // 历史要素之间的关系
    {
      source: 'historical_element_1',
      target: 'historical_element_4',
      relation: '视线关联',
      direction: '遥望',
      term: '遥望',
      weight: 1,
      color: '#9932CC',
    },
    {
      source: 'historical_element_2',
      target: 'historical_element_3',
      relation: '历史关联',
      direction: '同期',
      term: '同期',
      weight: 2,
      color: '#DC143C',
    },
    // 水系之间的关系
    {
      source: 'water_system_2',
      target: 'water_system_1',
      relation: '功能关联',
      direction: '汇入',
      term: '汇入',
      weight: 3,
      color: '#32CD32',
    },
    {
      source: 'water_system_3',
      target: 'water_system_1',
      relation: '功能关联',
      direction: '汇入',
      term: '汇入',
      weight: 3,
      color: '#32CD32',
    },
  ],
  categories: ['山塬', '水系', '历史要素'],
};

// 模拟统计数据
export const mockStatisticsData: API.ElementRelationStatistics = {
  total: 156,
  bySourceType: [
    { sourceType: 'mountain', count: 45 },
    { sourceType: 'water_system', count: 38 },
    { sourceType: 'historical_element', count: 73 },
  ],
  byTargetType: [
    { targetType: 'element', count: 140 },
    { targetType: 'category', count: 16 },
  ],
  byRelationType: [
    { relationId: 1, relationName: '选址关联', count: 67 },
    { relationId: 2, relationName: '视线关联', count: 23 },
    { relationId: 3, relationName: '历史关联', count: 34 },
    { relationId: 4, relationName: '功能关联', count: 32 },
  ],
  byDirection: [
    { direction: '北有', count: 28 },
    { direction: '南临', count: 22 },
    { direction: '东接', count: 19 },
    { direction: '西邻', count: 15 },
    { direction: '遥望', count: 31 },
    { direction: '依托', count: 41 },
  ],
};

// 模拟关联列表数据
export const mockElementRelationList: API.ElementRelation[] = [
  {
    id: 1,
    name: '骊山-灞河关联',
    code: 'REL001',
    sourceType: 'mountain',
    sourceId: 1,
    targetType: 'element',
    targetEntityType: 'water_system',
    targetId: 2,
    relationDictId: 1,
    direction: '北有',
    term: '北有',
    record: '骊山北有灞河流过',
    sort: 1,
    status: 1,
    createdAt: '2024-01-15T10:00:00.000Z',
    updatedAt: '2024-01-15T10:00:00.000Z',
    relationDict: {
      id: 1,
      relationName: '选址关联',
      relationCode: 'SITE_RELATION',
    },
    sourceElement: {
      id: 1,
      name: '骊山',
      code: 'LS001',
    },
    targetElement: {
      id: 2,
      name: '灞河',
      code: 'BH001',
    },
  },
  {
    id: 2,
    name: '兵马俑-骊山关联',
    code: 'REL002',
    sourceType: 'historical_element',
    sourceId: 2,
    targetType: 'element',
    targetEntityType: 'mountain',
    targetId: 1,
    relationDictId: 1,
    direction: '依山而建',
    term: '依山而建',
    record: '兵马俑依骊山而建',
    sort: 2,
    status: 1,
    createdAt: '2024-01-15T11:00:00.000Z',
    updatedAt: '2024-01-15T11:00:00.000Z',
    relationDict: {
      id: 1,
      relationName: '选址关联',
      relationCode: 'SITE_RELATION',
    },
    sourceElement: {
      id: 2,
      name: '兵马俑',
      code: 'BMY001',
    },
    targetElement: {
      id: 1,
      name: '骊山',
      code: 'LS001',
    },
  },
  {
    id: 3,
    name: '大雁塔-大明宫关联',
    code: 'REL003',
    sourceType: 'historical_element',
    sourceId: 1,
    targetType: 'element',
    targetEntityType: 'historical_element',
    targetId: 4,
    relationDictId: 2,
    direction: '遥望',
    term: '遥望',
    record: '大雁塔与大明宫遥相呼应',
    sort: 3,
    status: 1,
    createdAt: '2024-01-15T12:00:00.000Z',
    updatedAt: '2024-01-15T12:00:00.000Z',
    relationDict: {
      id: 2,
      relationName: '视线关联',
      relationCode: 'SIGHT_RELATION',
    },
    sourceElement: {
      id: 1,
      name: '大雁塔',
      code: 'DYT001',
    },
    targetElement: {
      id: 4,
      name: '大明宫',
      code: 'DMG001',
    },
  },
];

// 导出所有演示数据
export const demoData = {
  networkGraph: mockNetworkGraphData,
  statistics: mockStatisticsData,
  relationList: mockElementRelationList,
};
