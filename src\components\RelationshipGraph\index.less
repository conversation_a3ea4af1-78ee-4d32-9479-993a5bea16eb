.relationship-graph {
  .relationship-graph-loading,
  .relationship-graph-empty {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .graph-container {
    width: 100%;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background: #fff;
    overflow: hidden;
  }
}

// 响应式布局
@media (max-width: 768px) {
  .graph-container {
    height: 300px !important;
  }
}

@media (max-width: 576px) {
  .graph-container {
    height: 250px !important;
  }
}
