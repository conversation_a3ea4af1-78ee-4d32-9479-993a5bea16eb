import { getCulturalElementList } from '@/services/culturalElement';
import { getAncientCityDictList, getTypeDictTree } from '@/services/dictionary';
import { Spin, TreeSelect, message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

interface HierarchicalElementSelectorProps {
  value?: number;
  onChange?: (value: number, option?: any) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  entityType: 'ancient_city' | 'cultural_element';
}

interface TreeNode {
  title: string;
  value: string | number;
  key: string | number;
  children?: TreeNode[];
  isLeaf?: boolean;
  selectable?: boolean;
  data?: any;
}

const HierarchicalElementSelector: React.FC<
  HierarchicalElementSelectorProps
> = ({
  value,
  onChange,
  placeholder = '请选择要素',
  allowClear = true,
  disabled = false,
  style,
  entityType,
}) => {
  const [loading, setLoading] = useState(false);
  const [ancientCities, setAncientCities] = useState<API.AncientCityDict[]>([]);
  const [typeDict, setTypeDict] = useState<API.TypeDict[]>([]);
  const [culturalElements, setCulturalElements] = useState<
    API.CulturalElement[]
  >([]);

  // 加载基础数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // 并行加载所有需要的数据
        const [ancientCityRes, typeDictRes, culturalElementRes] =
          await Promise.all([
            getAncientCityDictList({ pageSize: 1000 }),
            getTypeDictTree(),
            getCulturalElementList({ pageSize: 1000 }),
          ]);

        if (ancientCityRes.errCode === 0 && ancientCityRes.data?.list) {
          setAncientCities(ancientCityRes.data.list);
        }

        if (typeDictRes.errCode === 0 && typeDictRes.data) {
          // 只取第一层类型字典
          const firstLevelTypes = typeDictRes.data.filter(
            (item) => !item.parentId,
          );
          console.log('类型字典数据:', firstLevelTypes);
          setTypeDict(firstLevelTypes);
        }

        if (culturalElementRes.errCode === 0 && culturalElementRes.data?.list) {
          console.log(
            '文化要素数据示例:',
            culturalElementRes.data.list.slice(0, 3),
          );
          setCulturalElements(culturalElementRes.data.list);
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        message.error('加载数据失败');
      } finally {
        setLoading(false);
      }
    };

    if (entityType === 'cultural_element') {
      loadData();
    } else if (entityType === 'ancient_city') {
      // 古城类型只需要加载古城数据
      const loadAncientCities = async () => {
        setLoading(true);
        try {
          const response = await getAncientCityDictList({ pageSize: 1000 });
          if (response.errCode === 0 && response.data?.list) {
            setAncientCities(response.data.list);
          }
        } catch (error) {
          console.error('加载古城数据失败:', error);
          message.error('加载古城数据失败');
        } finally {
          setLoading(false);
        }
      };
      loadAncientCities();
    }
  }, [entityType]);

  // 构建树形数据
  const buildTreeData = useMemo(() => {
    if (entityType === 'ancient_city') {
      // 古城类型：直接显示古城列表
      return ancientCities.map((city) => ({
        title: `${city.cityName} (${city.cityCode})`,
        value: city.id,
        key: `city_${city.id}`,
        isLeaf: true,
        data: city,
      }));
    }

    if (entityType === 'cultural_element') {
      // 文化要素类型：三层结构
      const treeNodes: TreeNode[] = [];

      ancientCities.forEach((city) => {
        // 第一层：古城
        const cityNode: TreeNode = {
          title: `🏛️ ${city.cityName}`,
          value: `city_${city.id}`,
          key: `city_${city.id}`,
          selectable: false,
          children: [],
        };

        // 获取该古城下的文化要素
        const cityElements = culturalElements.filter(
          (element) => element.ancientCityId === city.id,
        );

        if (cityElements.length > 0) {
          // 按类型分组
          const elementsByType = new Map<number, API.CulturalElement[]>();
          cityElements.forEach((element) => {
            // 使用 typeDictId，如果没有则使用 0 作为默认值
            const typeId = element.typeDictId || 0;
            if (!elementsByType.has(typeId)) {
              elementsByType.set(typeId, []);
            }
            elementsByType.get(typeId)!.push(element);
          });

          // 第二层：类型
          elementsByType.forEach((elements, typeId) => {
            // 查找对应的类型信息
            let typeName = '其他类型';
            const firstElement = elements[0];

            console.log(`处理类型ID ${typeId}，要素示例:`, {
              id: firstElement.id,
              name: firstElement.name,
              typeDictId: firstElement.typeDictId,
              typeName: (firstElement as any).typeName,
            });

            if (typeId > 0) {
              // 首先尝试从第一层类型字典中查找
              const typeInfo = typeDict.find((t) => t.id === typeId);
              if (typeInfo) {
                typeName = typeInfo.typeName;
                console.log(`从类型字典找到: ${typeName}`);
              } else {
                // 如果在第一层类型中找不到，尝试使用文化要素自带的 typeName
                if ((firstElement as any).typeName) {
                  typeName = (firstElement as any).typeName;
                  console.log(
                    `使用文化要素自带的类型名称: ${typeName} (ID: ${typeId})`,
                  );
                } else {
                  console.log(
                    `未找到类型ID ${typeId} 对应的类型信息，且文化要素没有 typeName 字段`,
                  );
                }
              }
            }

            const typeNode: TreeNode = {
              title: `📂 ${typeName}`,
              value: `type_${city.id}_${typeId}`,
              key: `type_${city.id}_${typeId}`,
              selectable: false,
              children: [],
            };

            // 第三层：具体要素
            elements.forEach((element) => {
              typeNode.children!.push({
                title: `${element.name} (${element.code})`,
                value: element.id,
                key: `element_${element.id}`,
                isLeaf: true,
                data: element,
              });
            });

            cityNode.children!.push(typeNode);
          });

          treeNodes.push(cityNode);
        }
      });

      return treeNodes;
    }

    return [];
  }, [entityType, ancientCities, typeDict, culturalElements]);

  const handleChange = (selectedValue: number, option: any) => {
    if (onChange) {
      onChange(selectedValue, option);
    }
  };

  return (
    <TreeSelect
      value={value}
      onChange={handleChange}
      treeData={buildTreeData}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      style={style}
      loading={loading}
      showSearch
      treeDefaultExpandAll={false}
      treeNodeFilterProp="title"
      filterTreeNode={(input, node) => {
        const title = node.title as string;
        return title.toLowerCase().includes(input.toLowerCase());
      }}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
    />
  );
};

export default HierarchicalElementSelector;
