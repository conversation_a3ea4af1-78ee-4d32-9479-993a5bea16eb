/**
 * 关系网络图组件的类型定义
 */

import type { CSSProperties } from 'react';

export interface NetworkNode {
  id: string;
  name: string;
  category: number;
  symbolSize: number;
  itemStyle: {
    color: string;
  };
  label: {
    show: boolean;
  };
}

export interface NetworkLink {
  source: string;
  target: string;
  name: string;
  lineStyle: {
    width: number;
    curveness: number;
  };
  label: {
    show: boolean;
    formatter: string;
  };
  value: number;
}

export interface NetworkCategory {
  name: string;
  itemStyle: {
    color: string;
  };
}

export interface NetworkGraphData {
  nodes: NetworkNode[];
  links: NetworkLink[];
  categories: NetworkCategory[];
}

export interface NetworkGraphFilters {
  sourceEntityType?: API.EntityType;
  targetEntityType?: API.EntityType;
  relationDictId?: number;
  status?: number;
}

export interface NetworkGraphProps {
  height?: number;
  title?: string;
  showFilters?: boolean;
  showCard?: boolean;
  isPublic?: boolean;
  onDataFetch?: (
    filters: NetworkGraphFilters,
  ) => Promise<NetworkGraphData | null>;
  initialFilters?: NetworkGraphFilters;
  className?: string;
  style?: CSSProperties;
}
