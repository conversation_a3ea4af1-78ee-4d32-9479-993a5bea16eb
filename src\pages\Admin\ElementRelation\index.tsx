import AncientCityListSelector from '@/components/AncientCityListSelector';
import DataManagementLayout from '@/components/DataManagementLayout';
import { PageContainer } from '@ant-design/pro-components';
import { useDispatch } from '@umijs/max';
import React, { useEffect, useState } from 'react';
import ElementRelationManagement from './ElementRelationManagement';

const ElementRelationPage: React.FC = () => {
  const dispatch = useDispatch();
  const [selectedCityId, setSelectedCityId] = useState<number | undefined>();

  // 处理古城选择变化
  const handleCityChange = (cityId?: number) => {
    setSelectedCityId(cityId);
  };

  // 初始化加载古城数据
  useEffect(() => {
    dispatch({ type: 'ancientCity/fetchAncientCityList' });
  }, [dispatch]);

  return (
    <PageContainer
      title="要素关联管理"
      content="管理文化要素之间的关联关系，支持创建、编辑、查看和批量导入关系数据"
    >
      {/* 左右布局 */}
      <DataManagementLayout
        selectorTitle="古城选择"
        selectorContent={
          <AncientCityListSelector
            selectedCityId={selectedCityId}
            onCityChange={handleCityChange}
          />
        }
        mainContent={
          <ElementRelationManagement selectedCityId={selectedCityId} />
        }
      />
    </PageContainer>
  );
};

export default ElementRelationPage;
